// Notification System Tables for Batch Notification System

model notification_queue {
  id String @id @default(cuid())

  // Notification Identification
  notification_type NotificationChannel

  // Recipient Information - recipient_user now stores email address
  recipient_user    String?
  recipient_phone   String?
  recipient_name    String?

  // Content
  subject      String?
  message_body String
  html_body    String?

  // Context and References - Required for batch notifications
  application_id String

  // Batch Notification Data
  document_changes Json @default("[]") // Array of document status changes
  sent_at          DateTime?

  // Scheduling and Status
  status       NotificationStatus @default(Pending)
  scheduled_at DateTime           @default(now())
  delivered_at DateTime?
  failed_at    DateTime?

  // Retry Logic
  retry_count   Int       @default(0)
  max_retries   Int       @default(3)
  next_retry_at DateTime?

  // Error Handling
  error_message String?

  // Audit Fields
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Relationships
  application application? @relation(fields: [application_id], references: [id])

  @@index([status])
  @@index([notification_type])
  @@index([recipient_user])
  @@index([scheduled_at])
  @@index([application_id])
  @@index([created_at])
  @@map("notification_queue")
}


