// Document Management Tables for Dynamic Workflow System

model document_master {
  id String @id @default(cuid())

  // Document Template Information
  name         String  // Document template name (required)
  description  String? // Document description (optional)
  category     String  // Document category for grouping (required)
  // Task 4: Removing document_type field
  // document_type String // Type classification (required)
  instructions String? // User-facing instructions for document submission (optional)

  // Audit Fields
  created_by String? // User ID who created this document master (simple string, no FK)
  updated_by String? // User ID who last updated this document master (simple string, no FK)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@index([category])
  // Task 4: Removing document_type index
  // @@index([document_type])
  @@index([created_at])
  @@index([updated_at])
  @@map("document_master")
}

model document_vault {
  id String @id @default(cuid())

  // Document Identification
  document_name     String
  original_filename String
  document_type     DocumentType
  document_category String? // Custom categorization

  // File Information
  file_path String // Supabase storage path
  file_size Int // File size in bytes

  // Ownership and Access
  user_id     String? // Document owner (if user)
  guest_email String? // Document owner (if guest)

  // Expiry Management
  expiry_date          DateTime?
  expiry_reminder_sent Boolean @default(false)
  auto_renewal_enabled Boolean @default(false)

  // Audit Fields
  uploaded_by String? // User who uploaded
  uploaded_at DateTime @default(now())
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relationships
  user user? @relation(fields: [user_id], references: [id])

  // Application associations
  application_documents application_document[]

  @@index([user_id])
  @@index([guest_email])
  @@index([document_type])
  @@index([expiry_date])
  @@index([created_at])
  @@map("document_vault")
}

model application_document {
  id                String @id @default(cuid())
  application_id    String
  document_vault_id String

  // NEW: Workflow stage information
  stage_order Int
  file_name   String
  file_url    String
  required    Boolean @default(false)
  status      String  @default("pending") // pending/approved/rejected

  // Document Requirement Information
  request_reason String? // Renamed from document_requirement

  // Custom Request Information
  requested_by String? // Admin/Agent who requested

  // Review Information
  reviewed_by      String?
  reviewed_at      DateTime?
  review_comments  String?
  rejection_reason String?
  uploaded_by      String? // User who uploaded the document

  // Audit Fields
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt
  upload_date  DateTime  @default(now()) // When document was uploaded
  submitted_at DateTime?

  // Relationships
  application application    @relation(fields: [application_id], references: [id], onDelete: Cascade)
  document    document_vault @relation(fields: [document_vault_id], references: [id])

  @@unique([application_id, document_vault_id])
  @@index([application_id])
  @@index([document_vault_id])
  @@index([stage_order])
  @@index([status])
  @@index([upload_date])
  @@map("application_document")
}
