model payment {
  id                       String               @id @default(cuid())
  amount                   Int
  status                   String
  payment_type             String
  service_type             String
  progress                 Status               @default(Pending)
  userId                   String?
  serviceId                String?
  packageId                String?
  immigration_serviceId    String?
  trainingId               String?
  guest_name               String?
  guest_email              String?
  guest_mobile             String?
  stripe_session_id        String?
  createdAt                DateTime             @default(now())
  updatedAt                DateTime             @updatedAt
  payment_method           String?
  stripe_payment_intent_id String?
  transaction_id           String?
  discount_amount          Int?
  actual_amount            Int?
  immigration_service      immigration_service? @relation(fields: [immigration_serviceId], references: [id])
  package                  packages?            @relation(fields: [packageId], references: [id])
  service                  service?             @relation(fields: [serviceId], references: [id])
  training                 training?            @relation(fields: [trainingId], references: [id])
  user                     user?                @relation(fields: [userId], references: [id])

  // Note: Removed direct relationship to applications
  // Applications now reference payments via payment_ids array

  @@index([userId])
  @@index([status])
  @@index([createdAt])
  @@index([payment_type])
  @@index([service_type])
  @@index([stripe_session_id])
  @@index([transaction_id])
  @@map("payment")
}
