model agent {
  id                String      @id @default(cuid())
  name              String
  email             String      @unique
  password_hash     String
  phone             String?
  status            AgentStatus @default(Active)
  created_at        DateTime    @default(now())
  updated_at        DateTime    @updatedAt
  created_by_admin_id String

  // Relationships
  created_by_admin  admin @relation(fields: [created_by_admin_id], references: [id])

  // Note: Removed direct relationship to applications
  // Applications now reference agents via agent_ids array

  @@index([email])
  @@index([status])
  @@index([created_by_admin_id])
  @@index([created_at])
  @@map("agent")
}
