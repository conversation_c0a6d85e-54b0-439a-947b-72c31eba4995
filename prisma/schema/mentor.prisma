model mentor {
  id            String    @id @default(cuid())
  name          String
  email         String    @unique
  emailVerified Boolean   @default(false)
  image         String
  password      String?
  location      String?
  designation   String
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  servicesId    String?
  desc          String
  status        Status    @default(Active)
  linkedin      String?
  profile       String?
  order         Int?
  reviews       review[]
  services      service[]
}

model service {
  id           String                 @id @default(cuid())
  name         String
  price        Int
  description  String
  createdAt    DateTime               @default(now())
  updatedAt    DateTime               @updatedAt
  mentorId     String?
  status       Status                 @default(Active)
  meeting_link String
  guest        guest_mentor_service[]
  payments     payment[]
  mentor       mentor?                @relation(fields: [mentorId], references: [id])
  users        user_mentor_service[]
}
