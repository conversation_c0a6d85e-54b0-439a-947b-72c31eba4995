-- Migration: Add Multiple Payments and Agents Support
-- This migration modifies the application table to support multiple payment_ids and agent_ids as arrays

-- Add new array fields for multiple payments and agents
ALTER TABLE "application" ADD COLUMN "payment_ids" TEXT[];
ALTER TABLE "application" ADD COLUMN "agent_ids" TEXT[];

-- Migrate existing data from single fields to arrays
UPDATE "application" 
SET "payment_ids" = CASE 
    WHEN "payment_id" IS NOT NULL THEN ARRAY["payment_id"]
    ELSE ARRAY[]::TEXT[]
END;

UPDATE "application" 
SET "agent_ids" = CASE 
    WHEN "assigned_to" IS NOT NULL THEN ARRAY["assigned_to"]
    ELSE ARRAY[]::TEXT[]
END;

-- Remove foreign key constraints for the old fields
ALTER TABLE "application" DROP CONSTRAINT IF EXISTS "application_payment_id_fkey";
ALTER TABLE "application" DROP CONSTRAINT IF EXISTS "application_assigned_to_fkey";

-- Drop the old single fields
ALTER TABLE "application" DROP COLUMN "payment_id";
ALTER TABLE "application" DROP COLUMN "assigned_to";

-- Add indexes for the new array fields for better query performance
CREATE INDEX "application_payment_ids_idx" ON "application" USING GIN ("payment_ids");
CREATE INDEX "application_agent_ids_idx" ON "application" USING GIN ("agent_ids");
