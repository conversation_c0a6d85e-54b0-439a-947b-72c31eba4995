/*
  Warnings:

  - Made the column `application_id` on table `notification_queue` required. This step will fail if there are existing NULL values in that column.
  - Made the column `document_changes` on table `notification_queue` required. This step will fail if there are existing NULL values in that column.
  - Made the column `is_sent` on table `notification_queue` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterEnum
ALTER TYPE "ApplicationStatus" ADD VALUE 'Pending';

-- DropForeignKey
ALTER TABLE "notification_queue" DROP CONSTRAINT "notification_queue_application_id_fkey";

-- AlterTable
ALTER TABLE "notification_queue" ALTER COLUMN "application_id" SET NOT NULL,
ALTER COLUMN "document_changes" SET NOT NULL,
ALTER COLUMN "is_sent" SET NOT NULL;

-- AddForeignKey
ALTER TABLE "notification_queue" ADD CONSTRAINT "notification_queue_application_id_fkey" FOREIGN KEY ("application_id") REFERENCES "application"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
