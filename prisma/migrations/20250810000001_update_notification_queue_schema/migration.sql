-- Migration: Update notification_queue schema for API endpoint modifications
-- Date: 2025-08-10
-- Description: Modify notification_queue table to store email in recipient_user field and remove specified fields

-- Step 1: Add new recipient_user column to store email addresses
ALTER TABLE "notification_queue" 
ADD COLUMN IF NOT EXISTS "recipient_user" TEXT;

-- Step 2: Migrate existing data from recipient_email to recipient_user
UPDATE "notification_queue" 
SET "recipient_user" = "recipient_email"
WHERE "recipient_email" IS NOT NULL;

-- Step 3: Drop the user relationship foreign key constraint
ALTER TABLE "notification_queue" 
DROP CONSTRAINT IF EXISTS "notification_queue_recipient_user_id_fkey";

-- Step 4: Remove columns that are no longer needed
ALTER TABLE "notification_queue" 
DROP COLUMN IF EXISTS "recipient_user_id",
DROP COLUMN IF EXISTS "recipient_email",
DROP COLUMN IF EXISTS "is_sent";

-- Step 5: Update indexes - remove old ones and add new ones
DROP INDEX IF EXISTS "notification_queue_recipient_email_idx";
DROP INDEX IF EXISTS "notification_queue_recipient_user_id_idx";
DROP INDEX IF EXISTS "notification_queue_application_id_is_sent_idx";
DROP INDEX IF EXISTS "notification_queue_is_sent_idx";

-- Step 6: Create new index for recipient_user field
CREATE INDEX IF NOT EXISTS "notification_queue_recipient_user_idx" 
ON "notification_queue"("recipient_user");

-- Step 7: Clean up any orphaned records that don't have application_id
-- (Keep existing records for historical purposes, but they won't be used in new system)
-- No action needed - handled in application logic
