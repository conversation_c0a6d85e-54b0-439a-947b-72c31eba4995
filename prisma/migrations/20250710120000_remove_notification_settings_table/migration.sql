-- Remove notification_settings table and migrate to file-based storage
-- This migration removes the database table as notification settings are now stored in JSON files

-- Drop foreign key constraint first
ALTER TABLE "notification_settings" DROP CONSTRAINT "notification_settings_user_id_fkey";

-- Drop indexes
DROP INDEX "notification_settings_user_id_key";
DROP INDEX "notification_settings_user_id_idx";
DROP INDEX "notification_settings_created_at_idx";

-- Drop the table
DROP TABLE "notification_settings";
