-- CreateEnum
CREATE TYPE "Day" AS ENUM ('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday');

-- CreateEnum
CREATE TYPE "Status" AS ENUM ('Accepted', 'Rejected', 'Pending', 'Completed', 'Active', 'Inactive', 'Blocked', 'Cancelled', 'Refunded');

-- CreateEnum
CREATE TYPE "Provider" AS ENUM ('credentials', 'google');

-- CreateEnum
CREATE TYPE "ApplicationStatus" AS ENUM ('Draft', 'Submitted', 'Under_Review', 'Additional_Info_Required', 'Approved', 'Rejected', 'Completed', 'Cancelled', 'On_Hold');

-- CreateEnum
CREATE TYPE "WorkflowStepStatus" AS ENUM ('Not_Started', 'In_Progress', 'Completed', 'Skipped', 'Blocked', 'Requires_Review');

-- CreateEnum
CREATE TYPE "DocumentStatus" AS ENUM ('Pending', 'Under_Review', 'Approved', 'Rejected', 'Requires_Revision', 'Expired', 'Superseded', 'Archived');

-- CreateEnum
CREATE TYPE "DocumentType" AS ENUM ('Passport', 'Resume', 'Offer_Letter', 'Educational_Certificate', 'Work_Experience_Letter', 'Medical_Report', 'Police_Clearance', 'Birth_Certificate', 'Marriage_Certificate', 'Bank_Statement', 'Insurance_Document', 'Other');

-- CreateEnum
CREATE TYPE "NotificationChannel" AS ENUM ('Email', 'In_App', 'SMS', 'Push');

-- CreateEnum
CREATE TYPE "NotificationStatus" AS ENUM ('Pending', 'Sent', 'Delivered', 'Failed', 'Cancelled');

-- CreateEnum
CREATE TYPE "PriorityLevel" AS ENUM ('Low', 'Medium', 'High', 'Critical');

-- CreateTable
CREATE TABLE "admin" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "emailVerified" BOOLEAN DEFAULT false,
    "image" TEXT,
    "password" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "admin_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "application" (
    "id" TEXT NOT NULL,
    "application_number" TEXT NOT NULL,
    "service_type" TEXT NOT NULL,
    "service_id" TEXT,
    "user_id" TEXT,
    "guest_name" TEXT,
    "guest_email" TEXT,
    "guest_mobile" TEXT,
    "status" "ApplicationStatus" NOT NULL DEFAULT 'Draft',
    "current_step" TEXT NOT NULL DEFAULT '1',
    "workflow_template_id" TEXT,
    "priority_level" "PriorityLevel" NOT NULL DEFAULT 'Medium',
    "estimated_completion" TIMESTAMP(3),
    "payment_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "completed_at" TIMESTAMP(3),
    "created_by" TEXT,
    "assigned_to" TEXT,

    CONSTRAINT "application_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "application_step" (
    "id" TEXT NOT NULL,
    "application_id" TEXT NOT NULL,
    "step_name" TEXT NOT NULL,
    "step_order" INTEGER NOT NULL,
    "status" "WorkflowStepStatus" NOT NULL DEFAULT 'Not_Started',
    "required_fields" JSONB,
    "validation_rules" JSONB,
    "completion_criteria" JSONB,
    "estimated_duration" INTEGER,
    "sla_threshold" INTEGER,
    "started_at" TIMESTAMP(3),
    "completed_at" TIMESTAMP(3),
    "due_date" TIMESTAMP(3),
    "assignee_role" TEXT,
    "assigned_to" TEXT,
    "reviewer_id" TEXT,
    "review_notes" TEXT,
    "step_data" JSONB,
    "attachments" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "application_step_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "blog" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "summary" TEXT NOT NULL,
    "blogger" TEXT NOT NULL,
    "img" TEXT NOT NULL,
    "desc" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "blog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "comment" (
    "id" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "blogId" TEXT NOT NULL,
    "authorId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "parentId" TEXT,

    CONSTRAINT "comment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "contact_us" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "mobile" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "contact_us_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "document_master" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT NOT NULL,
    "instructions" TEXT,
    "created_by" TEXT,
    "updated_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "document_master_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "document_vault" (
    "id" TEXT NOT NULL,
    "document_name" TEXT NOT NULL,
    "original_filename" TEXT NOT NULL,
    "document_type" "DocumentType" NOT NULL,
    "document_category" TEXT,
    "file_path" TEXT NOT NULL,
    "file_size" INTEGER NOT NULL,
    "file_hash" TEXT NOT NULL,
    "mime_type" TEXT NOT NULL,
    "status" "DocumentStatus" NOT NULL DEFAULT 'Pending',
    "version" TEXT NOT NULL DEFAULT '1.0',
    "is_current_version" BOOLEAN NOT NULL DEFAULT true,
    "parent_document_id" TEXT,
    "user_id" TEXT,
    "guest_email" TEXT,
    "access_permissions" JSONB,
    "sharing_settings" JSONB,
    "tags" TEXT[],
    "metadata" JSONB,
    "expiry_date" TIMESTAMP(3),
    "expiry_reminder_sent" BOOLEAN NOT NULL DEFAULT false,
    "auto_renewal_enabled" BOOLEAN NOT NULL DEFAULT false,
    "verified_by" TEXT,
    "verified_at" TIMESTAMP(3),
    "verification_notes" TEXT,
    "review_required" BOOLEAN NOT NULL DEFAULT false,
    "uploaded_by" TEXT,
    "uploaded_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "document_vault_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "application_document" (
    "id" TEXT NOT NULL,
    "application_id" TEXT NOT NULL,
    "document_vault_id" TEXT NOT NULL,
    "document_requirement" TEXT NOT NULL,
    "is_required" BOOLEAN NOT NULL DEFAULT true,
    "is_optional" BOOLEAN NOT NULL DEFAULT false,
    "is_custom_request" BOOLEAN NOT NULL DEFAULT false,
    "requested_by" TEXT,
    "request_reason" TEXT,
    "request_deadline" TIMESTAMP(3),
    "request_priority" "PriorityLevel" NOT NULL DEFAULT 'Medium',
    "internal_notes" TEXT,
    "submission_status" "DocumentStatus" NOT NULL DEFAULT 'Pending',
    "review_status" "DocumentStatus",
    "approval_status" "DocumentStatus",
    "reviewed_by" TEXT,
    "reviewed_at" TIMESTAMP(3),
    "review_comments" TEXT,
    "rejection_reason" TEXT,
    "meets_requirements" BOOLEAN,
    "validation_errors" JSONB,
    "compliance_notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "submitted_at" TIMESTAMP(3),

    CONSTRAINT "application_document_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "mentor" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "emailVerified" BOOLEAN NOT NULL DEFAULT false,
    "image" TEXT NOT NULL,
    "password" TEXT,
    "location" TEXT,
    "designation" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "servicesId" TEXT,
    "desc" TEXT NOT NULL,
    "status" "Status" NOT NULL DEFAULT 'Active',
    "linkedin" TEXT,
    "profile" TEXT,
    "order" INTEGER,

    CONSTRAINT "mentor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "service" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "description" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "mentorId" TEXT,
    "status" "Status" NOT NULL DEFAULT 'Active',
    "meeting_link" TEXT NOT NULL,

    CONSTRAINT "service_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notification_template" (
    "id" TEXT NOT NULL,
    "template_name" TEXT NOT NULL,
    "template_type" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "subject" TEXT,
    "body_template" TEXT NOT NULL,
    "html_template" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_system_template" BOOLEAN NOT NULL DEFAULT false,
    "merge_fields" JSONB,
    "trigger_event" TEXT NOT NULL,
    "trigger_conditions" JSONB,
    "send_immediately" BOOLEAN NOT NULL DEFAULT true,
    "delay_minutes" INTEGER,
    "retry_attempts" INTEGER NOT NULL DEFAULT 3,
    "retry_interval" INTEGER NOT NULL DEFAULT 60,
    "supports_localization" BOOLEAN NOT NULL DEFAULT false,
    "default_language" TEXT NOT NULL DEFAULT 'en',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by" TEXT,
    "last_modified_by" TEXT,

    CONSTRAINT "notification_template_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notification_queue" (
    "id" TEXT NOT NULL,
    "notification_type" "NotificationChannel" NOT NULL,
    "template_id" TEXT,
    "recipient_user_id" TEXT,
    "recipient_email" TEXT,
    "recipient_phone" TEXT,
    "recipient_name" TEXT,
    "subject" TEXT,
    "message_body" TEXT NOT NULL,
    "html_body" TEXT,
    "application_id" TEXT,
    "document_id" TEXT,
    "step_id" TEXT,
    "reference_type" TEXT,
    "reference_id" TEXT,
    "status" "NotificationStatus" NOT NULL DEFAULT 'Pending',
    "priority" "PriorityLevel" NOT NULL DEFAULT 'Medium',
    "scheduled_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "sent_at" TIMESTAMP(3),
    "delivered_at" TIMESTAMP(3),
    "failed_at" TIMESTAMP(3),
    "retry_count" INTEGER NOT NULL DEFAULT 0,
    "max_retries" INTEGER NOT NULL DEFAULT 3,
    "next_retry_at" TIMESTAMP(3),
    "error_message" TEXT,
    "error_code" TEXT,
    "opened_at" TIMESTAMP(3),
    "clicked_at" TIMESTAMP(3),
    "tracking_id" TEXT,
    "metadata" JSONB,
    "merge_data" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notification_queue_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "payment" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "payment_type" TEXT NOT NULL,
    "service_type" TEXT NOT NULL,
    "progress" "Status" NOT NULL DEFAULT 'Pending',
    "userId" TEXT,
    "serviceId" TEXT,
    "packageId" TEXT,
    "immigration_serviceId" TEXT,
    "trainingId" TEXT,
    "guest_name" TEXT,
    "guest_email" TEXT,
    "guest_mobile" TEXT,
    "stripe_session_id" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "payment_method" TEXT,
    "stripe_payment_intent_id" TEXT,
    "transaction_id" TEXT,

    CONSTRAINT "payment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "review" (
    "id" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "mentorId" TEXT,
    "userId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "rating" INTEGER NOT NULL,

    CONSTRAINT "review_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_review" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "img" TEXT,
    "comment" TEXT NOT NULL,
    "source" TEXT NOT NULL,
    "rating" INTEGER NOT NULL,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "order" INTEGER,

    CONSTRAINT "customer_review_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "packages" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "note" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "service" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "order" INTEGER,

    CONSTRAINT "packages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "immigration_service" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "service" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "order" INTEGER,

    CONSTRAINT "immigration_service_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "training" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "service" TEXT[],
    "highlights" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "order" INTEGER,
    "img" TEXT NOT NULL,

    CONSTRAINT "training_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "emailVerified" BOOLEAN DEFAULT false,
    "image" TEXT,
    "password" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "provider" "Provider" NOT NULL,

    CONSTRAINT "user_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_mentor_service" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT,
    "serviceId" TEXT,
    "status" TEXT NOT NULL,
    "progress" "Status" NOT NULL DEFAULT 'Pending',

    CONSTRAINT "user_mentor_service_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "guest_mentor_service" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "mobile_no" TEXT NOT NULL,
    "progress" "Status" NOT NULL DEFAULT 'Pending',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "serviceId" TEXT,

    CONSTRAINT "guest_mentor_service_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_package" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT,
    "packageId" TEXT,
    "progress" "Status" NOT NULL DEFAULT 'Pending',

    CONSTRAINT "user_package_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "guest_package" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "mobile_no" TEXT NOT NULL,
    "progress" "Status" NOT NULL DEFAULT 'Pending',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "packageId" TEXT,

    CONSTRAINT "guest_package_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_immigration_service" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT,
    "immigration_serviceId" TEXT,
    "progress" "Status" NOT NULL DEFAULT 'Pending',

    CONSTRAINT "user_immigration_service_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "guest_immigration_service" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "mobile_no" TEXT NOT NULL,
    "progress" "Status" NOT NULL DEFAULT 'Pending',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "immigration_serviceId" TEXT,

    CONSTRAINT "guest_immigration_service_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_training" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "progress" "Status" NOT NULL DEFAULT 'Pending',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT,
    "trainingId" TEXT,

    CONSTRAINT "user_training_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "guest_training" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "mobile_no" TEXT NOT NULL,
    "progress" "Status" NOT NULL DEFAULT 'Pending',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "trainingId" TEXT,

    CONSTRAINT "guest_training_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow_master" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_by" TEXT,
    "updated_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "workflow_master_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow_template" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "serviceType" TEXT NOT NULL,
    "serviceId" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "workflowTemplate" JSONB NOT NULL,
    "createdBy" TEXT,
    "updatedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "workflow_template_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "admin_email_key" ON "admin"("email");

-- CreateIndex
CREATE UNIQUE INDEX "application_application_number_key" ON "application"("application_number");

-- CreateIndex
CREATE INDEX "application_application_number_idx" ON "application"("application_number");

-- CreateIndex
CREATE INDEX "application_service_type_idx" ON "application"("service_type");

-- CreateIndex
CREATE INDEX "application_status_idx" ON "application"("status");

-- CreateIndex
CREATE INDEX "application_user_id_idx" ON "application"("user_id");

-- CreateIndex
CREATE INDEX "application_guest_email_idx" ON "application"("guest_email");

-- CreateIndex
CREATE INDEX "application_created_at_idx" ON "application"("created_at");

-- CreateIndex
CREATE INDEX "application_payment_id_idx" ON "application"("payment_id");

-- CreateIndex
CREATE INDEX "application_assigned_to_idx" ON "application"("assigned_to");

-- CreateIndex
CREATE INDEX "application_step_application_id_idx" ON "application_step"("application_id");

-- CreateIndex
CREATE INDEX "application_step_status_idx" ON "application_step"("status");

-- CreateIndex
CREATE INDEX "application_step_step_order_idx" ON "application_step"("step_order");

-- CreateIndex
CREATE INDEX "application_step_assigned_to_idx" ON "application_step"("assigned_to");

-- CreateIndex
CREATE INDEX "application_step_due_date_idx" ON "application_step"("due_date");

-- CreateIndex
CREATE INDEX "application_step_created_at_idx" ON "application_step"("created_at");

-- CreateIndex
CREATE UNIQUE INDEX "blog_slug_key" ON "blog"("slug");

-- CreateIndex
CREATE INDEX "document_master_category_idx" ON "document_master"("category");

-- CreateIndex
CREATE INDEX "document_master_created_at_idx" ON "document_master"("created_at");

-- CreateIndex
CREATE INDEX "document_master_updated_at_idx" ON "document_master"("updated_at");

-- CreateIndex
CREATE INDEX "document_vault_user_id_idx" ON "document_vault"("user_id");

-- CreateIndex
CREATE INDEX "document_vault_guest_email_idx" ON "document_vault"("guest_email");

-- CreateIndex
CREATE INDEX "document_vault_document_type_idx" ON "document_vault"("document_type");

-- CreateIndex
CREATE INDEX "document_vault_status_idx" ON "document_vault"("status");

-- CreateIndex
CREATE INDEX "document_vault_file_hash_idx" ON "document_vault"("file_hash");

-- CreateIndex
CREATE INDEX "document_vault_expiry_date_idx" ON "document_vault"("expiry_date");

-- CreateIndex
CREATE INDEX "document_vault_created_at_idx" ON "document_vault"("created_at");

-- CreateIndex
CREATE INDEX "document_vault_is_current_version_idx" ON "document_vault"("is_current_version");

-- CreateIndex
CREATE INDEX "document_vault_tags_idx" ON "document_vault"("tags");

-- CreateIndex
CREATE INDEX "application_document_application_id_idx" ON "application_document"("application_id");

-- CreateIndex
CREATE INDEX "application_document_document_vault_id_idx" ON "application_document"("document_vault_id");

-- CreateIndex
CREATE INDEX "application_document_submission_status_idx" ON "application_document"("submission_status");

-- CreateIndex
CREATE INDEX "application_document_is_required_idx" ON "application_document"("is_required");

-- CreateIndex
CREATE INDEX "application_document_is_custom_request_idx" ON "application_document"("is_custom_request");

-- CreateIndex
CREATE INDEX "application_document_request_deadline_idx" ON "application_document"("request_deadline");

-- CreateIndex
CREATE INDEX "application_document_reviewed_at_idx" ON "application_document"("reviewed_at");

-- CreateIndex
CREATE UNIQUE INDEX "application_document_application_id_document_vault_id_key" ON "application_document"("application_id", "document_vault_id");

-- CreateIndex
CREATE UNIQUE INDEX "mentor_email_key" ON "mentor"("email");

-- CreateIndex
CREATE UNIQUE INDEX "notification_template_template_name_key" ON "notification_template"("template_name");

-- CreateIndex
CREATE INDEX "notification_template_template_type_idx" ON "notification_template"("template_type");

-- CreateIndex
CREATE INDEX "notification_template_category_idx" ON "notification_template"("category");

-- CreateIndex
CREATE INDEX "notification_template_is_active_idx" ON "notification_template"("is_active");

-- CreateIndex
CREATE INDEX "notification_template_trigger_event_idx" ON "notification_template"("trigger_event");

-- CreateIndex
CREATE INDEX "notification_template_created_at_idx" ON "notification_template"("created_at");

-- CreateIndex
CREATE UNIQUE INDEX "notification_queue_tracking_id_key" ON "notification_queue"("tracking_id");

-- CreateIndex
CREATE INDEX "notification_queue_status_idx" ON "notification_queue"("status");

-- CreateIndex
CREATE INDEX "notification_queue_notification_type_idx" ON "notification_queue"("notification_type");

-- CreateIndex
CREATE INDEX "notification_queue_recipient_email_idx" ON "notification_queue"("recipient_email");

-- CreateIndex
CREATE INDEX "notification_queue_recipient_user_id_idx" ON "notification_queue"("recipient_user_id");

-- CreateIndex
CREATE INDEX "notification_queue_scheduled_at_idx" ON "notification_queue"("scheduled_at");

-- CreateIndex
CREATE INDEX "notification_queue_priority_idx" ON "notification_queue"("priority");

-- CreateIndex
CREATE INDEX "notification_queue_application_id_idx" ON "notification_queue"("application_id");

-- CreateIndex
CREATE INDEX "notification_queue_reference_type_reference_id_idx" ON "notification_queue"("reference_type", "reference_id");

-- CreateIndex
CREATE INDEX "notification_queue_created_at_idx" ON "notification_queue"("created_at");

-- CreateIndex
CREATE INDEX "payment_userId_idx" ON "payment"("userId");

-- CreateIndex
CREATE INDEX "payment_status_idx" ON "payment"("status");

-- CreateIndex
CREATE INDEX "payment_createdAt_idx" ON "payment"("createdAt");

-- CreateIndex
CREATE INDEX "payment_payment_type_idx" ON "payment"("payment_type");

-- CreateIndex
CREATE INDEX "payment_service_type_idx" ON "payment"("service_type");

-- CreateIndex
CREATE INDEX "payment_stripe_session_id_idx" ON "payment"("stripe_session_id");

-- CreateIndex
CREATE INDEX "payment_transaction_id_idx" ON "payment"("transaction_id");

-- CreateIndex
CREATE UNIQUE INDEX "user_email_key" ON "user"("email");

-- CreateIndex
CREATE INDEX "workflow_master_name_idx" ON "workflow_master"("name");

-- CreateIndex
CREATE INDEX "workflow_master_is_active_idx" ON "workflow_master"("is_active");

-- CreateIndex
CREATE INDEX "workflow_master_created_at_idx" ON "workflow_master"("created_at");

-- CreateIndex
CREATE INDEX "workflow_master_updated_at_idx" ON "workflow_master"("updated_at");

-- CreateIndex
CREATE INDEX "workflow_template_name_idx" ON "workflow_template"("name");

-- CreateIndex
CREATE INDEX "workflow_template_serviceType_idx" ON "workflow_template"("serviceType");

-- CreateIndex
CREATE INDEX "workflow_template_isActive_idx" ON "workflow_template"("isActive");

-- CreateIndex
CREATE INDEX "workflow_template_createdAt_idx" ON "workflow_template"("createdAt");

-- CreateIndex
CREATE INDEX "workflow_template_updatedAt_idx" ON "workflow_template"("updatedAt");

-- AddForeignKey
ALTER TABLE "application" ADD CONSTRAINT "application_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "application" ADD CONSTRAINT "application_payment_id_fkey" FOREIGN KEY ("payment_id") REFERENCES "payment"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "application" ADD CONSTRAINT "application_workflow_template_id_fkey" FOREIGN KEY ("workflow_template_id") REFERENCES "workflow_template"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comment" ADD CONSTRAINT "comment_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "user"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comment" ADD CONSTRAINT "comment_blogId_fkey" FOREIGN KEY ("blogId") REFERENCES "blog"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comment" ADD CONSTRAINT "comment_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "comment"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document_vault" ADD CONSTRAINT "document_vault_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document_vault" ADD CONSTRAINT "document_vault_parent_document_id_fkey" FOREIGN KEY ("parent_document_id") REFERENCES "document_vault"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "application_document" ADD CONSTRAINT "application_document_application_id_fkey" FOREIGN KEY ("application_id") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "application_document" ADD CONSTRAINT "application_document_document_vault_id_fkey" FOREIGN KEY ("document_vault_id") REFERENCES "document_vault"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service" ADD CONSTRAINT "service_mentorId_fkey" FOREIGN KEY ("mentorId") REFERENCES "mentor"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notification_queue" ADD CONSTRAINT "notification_queue_template_id_fkey" FOREIGN KEY ("template_id") REFERENCES "notification_template"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notification_queue" ADD CONSTRAINT "notification_queue_recipient_user_id_fkey" FOREIGN KEY ("recipient_user_id") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notification_queue" ADD CONSTRAINT "notification_queue_application_id_fkey" FOREIGN KEY ("application_id") REFERENCES "application"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payment" ADD CONSTRAINT "payment_immigration_serviceId_fkey" FOREIGN KEY ("immigration_serviceId") REFERENCES "immigration_service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payment" ADD CONSTRAINT "payment_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "packages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payment" ADD CONSTRAINT "payment_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payment" ADD CONSTRAINT "payment_trainingId_fkey" FOREIGN KEY ("trainingId") REFERENCES "training"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payment" ADD CONSTRAINT "payment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "review" ADD CONSTRAINT "review_mentorId_fkey" FOREIGN KEY ("mentorId") REFERENCES "mentor"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "review" ADD CONSTRAINT "review_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_mentor_service" ADD CONSTRAINT "user_mentor_service_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_mentor_service" ADD CONSTRAINT "user_mentor_service_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guest_mentor_service" ADD CONSTRAINT "guest_mentor_service_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_package" ADD CONSTRAINT "user_package_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "packages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_package" ADD CONSTRAINT "user_package_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guest_package" ADD CONSTRAINT "guest_package_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "packages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_immigration_service" ADD CONSTRAINT "user_immigration_service_immigration_serviceId_fkey" FOREIGN KEY ("immigration_serviceId") REFERENCES "immigration_service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_immigration_service" ADD CONSTRAINT "user_immigration_service_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guest_immigration_service" ADD CONSTRAINT "guest_immigration_service_immigration_serviceId_fkey" FOREIGN KEY ("immigration_serviceId") REFERENCES "immigration_service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_training" ADD CONSTRAINT "user_training_trainingId_fkey" FOREIGN KEY ("trainingId") REFERENCES "training"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_training" ADD CONSTRAINT "user_training_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guest_training" ADD CONSTRAINT "guest_training_trainingId_fkey" FOREIGN KEY ("trainingId") REFERENCES "training"("id") ON DELETE SET NULL ON UPDATE CASCADE;
