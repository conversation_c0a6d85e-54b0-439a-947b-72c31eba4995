-- Migration: Implement Batch Notification System
-- Date: 2025-08-09
-- Description: Update notification_queue table for batch notifications and remove template table

-- Step 1: Drop the notification_template table completely
DROP TABLE IF EXISTS "notification_template" CASCADE;

-- Step 2: Remove columns from notification_queue that are no longer needed
ALTER TABLE "notification_queue" 
DROP COLUMN IF EXISTS "document_id",
DROP COLUMN IF EXISTS "step_id", 
DROP COLUMN IF EXISTS "reference_type",
DROP COLUMN IF EXISTS "reference_id",
DROP COLUMN IF EXISTS "priority",
DROP COLUMN IF EXISTS "error_code",
DROP COLUMN IF EXISTS "opened_at",
DROP COLUMN IF EXISTS "clicked_at", 
DROP COLUMN IF EXISTS "tracking_id",
DROP COLUMN IF EXISTS "metadata",
DROP COLUMN IF EXISTS "merge_data",
DROP COLUMN IF EXISTS "template_id";

-- Step 3: Add new columns for batch notification system
ALTER TABLE "notification_queue"
ADD COLUMN IF NOT EXISTS "document_changes" JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS "is_sent" BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS "sent_at" TIMESTAMP(3);

-- Step 4: Update existing records to set is_sent based on current status
UPDATE "notification_queue" 
SET "is_sent" = true, "sent_at" = "sent_at"
WHERE "status" = 'Sent';

UPDATE "notification_queue" 
SET "is_sent" = false
WHERE "status" != 'Sent';

-- Step 5: Create indexes for better performance
CREATE INDEX IF NOT EXISTS "notification_queue_application_id_is_sent_idx" 
ON "notification_queue"("application_id", "is_sent");

CREATE INDEX IF NOT EXISTS "notification_queue_is_sent_idx" 
ON "notification_queue"("is_sent");

-- Step 6: Add constraint to ensure application_id is not null for new batch notifications
-- (We'll handle this in the application logic rather than database constraint for flexibility)

-- Step 7: Clean up any orphaned records that don't have application_id
-- (Keep existing records for historical purposes, but they won't be used in new system)
