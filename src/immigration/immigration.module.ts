import { <PERSON>du<PERSON> } from '@nestjs/common';
import { ImmigrationController } from './immigration.controller';
import { ImmigrationService } from './immigration.service';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';

@Module({
  controllers: [ImmigrationController],
  providers: [ImmigrationService, PrismaService, JwtService],
})
export class ImmigrationModule {}
