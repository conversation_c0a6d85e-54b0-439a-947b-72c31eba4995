/**
 * Response Utilities
 * Task 8: API Implementation and Documentation
 *
 * Standardized response formats for consistent API responses
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Standard API Response Format
 */
export class StandardResponseDto<T = any> {
  @ApiProperty({
    description: 'Response status',
    example: 'success',
    enum: ['success', 'error'],
  })
  status: 'success' | 'error';

  @ApiProperty({
    description: 'Response message',
    example: 'Operation completed successfully',
  })
  message: string;

  @ApiPropertyOptional({
    description: 'Response data',
  })
  data?: T;

  @ApiPropertyOptional({
    description: 'Error details (only present when status is error)',
  })
  error?: {
    code?: string;
    details?: any;
  };

  @ApiPropertyOptional({
    description: 'Pagination information (for paginated responses)',
  })
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };

  @ApiPropertyOptional({
    description: 'Response metadata',
  })
  meta?: {
    timestamp: string;
    version: string;
    [key: string]: any;
  };
}

/**
 * Paginated Response Format
 */
export class PaginatedResponseDto<T = any> {
  @ApiProperty({
    description: 'Response status',
    example: 'success',
  })
  status: 'success' | 'error';

  @ApiProperty({
    description: 'Response message',
    example: 'Data retrieved successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Array of data items',
    type: 'array',
  })
  data: T[];

  @ApiProperty({
    description: 'Pagination information',
  })
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Error Response Format
 */
export class ErrorResponseDto {
  @ApiProperty({
    description: 'Response status',
    example: 'error',
  })
  status: 'error';

  @ApiProperty({
    description: 'Error message',
    example: 'An error occurred',
  })
  message: string;

  @ApiProperty({
    description: 'Error details',
  })
  error: {
    code: string;
    details?: any;
    timestamp: string;
  };
}

/**
 * Response Utility Class
 */
export class ResponseUtil {
  /**
   * Create a success response
   */
  static success<T>(
    message: string,
    data?: T,
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages?: number;
    },
  ): StandardResponseDto<T> {
    const response: StandardResponseDto<T> = {
      status: 'success',
      message,
    };

    if (data !== undefined) {
      response.data = data;
    }

    if (pagination) {
      response.pagination = {
        ...pagination,
        totalPages:
          pagination.totalPages ||
          Math.ceil(pagination.total / pagination.limit),
      };
    }

    response.meta = {
      timestamp: new Date().toISOString(),
      version: '1.0',
    };

    return response;
  }

  /**
   * Create a paginated success response
   */
  static paginated<T>(
    message: string,
    data: T[],
    pagination: {
      page: number;
      limit: number;
      total: number;
    },
  ): PaginatedResponseDto<T> {
    return {
      status: 'success',
      message,
      data,
      pagination: {
        ...pagination,
        totalPages: Math.ceil(pagination.total / pagination.limit),
      },
    };
  }

  /**
   * Create an error response
   */
  static error(
    message: string,
    code: string = 'INTERNAL_ERROR',
    details?: any,
  ): ErrorResponseDto {
    return {
      status: 'error',
      message,
      error: {
        code,
        details,
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Create a validation error response
   */
  static validationError(
    message: string = 'Validation failed',
    validationErrors: any,
  ): ErrorResponseDto {
    return {
      status: 'error',
      message,
      error: {
        code: 'VALIDATION_ERROR',
        details: validationErrors,
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Create a not found error response
   */
  static notFound(resource: string = 'Resource'): ErrorResponseDto {
    return {
      status: 'error',
      message: `${resource} not found`,
      error: {
        code: 'NOT_FOUND',
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Create an unauthorized error response
   */
  static unauthorized(
    message: string = 'Unauthorized access',
  ): ErrorResponseDto {
    return {
      status: 'error',
      message,
      error: {
        code: 'UNAUTHORIZED',
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Create a forbidden error response
   */
  static forbidden(message: string = 'Access forbidden'): ErrorResponseDto {
    return {
      status: 'error',
      message,
      error: {
        code: 'FORBIDDEN',
        timestamp: new Date().toISOString(),
      },
    };
  }
}

/**
 * Pagination Helper
 */
export class PaginationUtil {
  /**
   * Calculate pagination metadata
   */
  static calculatePagination(
    page: number = 1,
    limit: number = 20,
    total: number,
  ) {
    const totalPages = Math.ceil(total / limit);
    const offset = (page - 1) * limit;

    return {
      page: Math.max(1, page),
      limit: Math.max(1, Math.min(100, limit)), // Max 100 items per page
      total,
      totalPages,
      offset,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * Validate pagination parameters
   */
  static validatePagination(page?: number, limit?: number) {
    const validatedPage = Math.max(1, page || 1);
    const validatedLimit = Math.max(1, Math.min(100, limit || 20));

    return {
      page: validatedPage,
      limit: validatedLimit,
    };
  }
}
