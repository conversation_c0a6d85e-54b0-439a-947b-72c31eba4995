/**
 * Logger Service
 *
 * Centralized logging service using <PERSON> for structured logging
 * with file rotation and different log levels.
 *
 * Features:
 * - Structured JSON logging
 * - Daily log rotation
 * - Multiple log levels (error, warn, info, debug)
 * - Console and file output
 * - Request correlation IDs
 * - Performance monitoring
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */

import { Injectable } from '@nestjs/common';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';
import { format } from 'winston';
import * as path from 'path';
import * as fs from 'fs';

@Injectable()
export class LoggerService {
  private logger: winston.Logger;

  constructor() {
    this.logger = this.createLogger();
  }

  private createLogger(): winston.Logger {
    // Ensure logs directory exists
    const logsDir = path.join(process.cwd(), 'logs');

    // Create logs directory if it doesn't exist
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    // Custom format for structured logging
    const customFormat = format.combine(
      format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss.SSS',
      }),
      format.errors({ stack: true }),
      format.json(),
      format.printf(
        ({ timestamp, level, message, service, correlationId, ...meta }) => {
          const logEntry = {
            timestamp,
            level: level.toUpperCase(),
            service: service || 'careerireland-api',
            message,
            ...(correlationId && { correlationId }),
            ...meta,
          };
          return JSON.stringify(logEntry);
        },
      ),
    );

    // Console format for development
    const consoleFormat = format.combine(
      format.colorize(),
      format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss',
      }),
      format.printf(
        ({ timestamp, level, message, service, correlationId, ...meta }) => {
          const metaStr = Object.keys(meta).length
            ? ` ${JSON.stringify(meta)}`
            : '';
          const corrId = correlationId ? ` [${correlationId}]` : '';
          return `[${timestamp}] ${level} [${service || 'api'}]${corrId}: ${message}${metaStr}`;
        },
      ),
    );

    // Daily rotate file transport for all logs
    const fileTransport = new DailyRotateFile({
      filename: path.join(logsDir, '%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d',
      format: customFormat,
    });

    // Daily rotate file transport for error logs only
    const errorFileTransport = new DailyRotateFile({
      filename: path.join(logsDir, 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      level: 'error',
      format: customFormat,
    });

    // Console transport for development
    const consoleTransport = new winston.transports.Console({
      format: consoleFormat,
      level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    });

    return winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: customFormat,
      defaultMeta: {
        service: 'careerireland-api',
        environment: process.env.NODE_ENV || 'development',
      },
      transports: [
        fileTransport,
        errorFileTransport,
        ...(process.env.NODE_ENV !== 'test' ? [consoleTransport] : []),
      ],
      exitOnError: false,
    });
  }

  /**
   * Log error message
   */
  error(message: string, error?: Error | any, meta?: any): void {
    this.logger.error(message, {
      error: error?.message || error,
      stack: error?.stack,
      ...meta,
    });
  }

  /**
   * Log warning message
   */
  warn(message: string, meta?: any): void {
    this.logger.warn(message, meta);
  }

  /**
   * Log info message
   */
  info(message: string, meta?: any): void {
    this.logger.info(message, meta);
  }

  /**
   * Log debug message
   */
  debug(message: string, meta?: any): void {
    this.logger.debug(message, meta);
  }

  /**
   * Log payment-specific events
   */
  logPayment(event: string, paymentData: any, meta?: any): void {
    this.info(`Payment ${event}`, {
      paymentId: paymentData.id,
      amount: paymentData.amount,
      currency: paymentData.currency || 'EUR',
      serviceType: paymentData.service_type,
      paymentType: paymentData.payment_type,
      status: paymentData.status,
      userId: paymentData.userId,
      ...meta,
    });
  }

  /**
   * Log API request/response
   */
  logRequest(
    method: string,
    url: string,
    statusCode: number,
    responseTime: number,
    meta?: any,
  ): void {
    this.info('API Request', {
      method,
      url,
      statusCode,
      responseTime: `${responseTime}ms`,
      ...meta,
    });
  }

  /**
   * Log with correlation ID for request tracing
   */
  logWithCorrelation(
    level: string,
    message: string,
    correlationId: string,
    meta?: any,
  ): void {
    this.logger.log(level, message, {
      correlationId,
      ...meta,
    });
  }

  /**
   * Log authentication events
   */
  logAuth(event: string, userId: string, meta?: any): void {
    this.info(`Auth ${event}`, {
      userId,
      event,
      ...meta,
    });
  }

  /**
   * Log file operations
   */
  logFileOperation(operation: string, fileName: string, meta?: any): void {
    this.info(`File ${operation}`, {
      fileName,
      operation,
      ...meta,
    });
  }

  /**
   * Log database operations with performance tracking
   */
  logDatabaseOperation(
    operation: string,
    table: string,
    duration?: number,
    meta?: any,
  ): void {
    this.debug(`Database ${operation}`, {
      table,
      operation,
      ...(duration && { duration: `${duration}ms` }),
      ...meta,
    });
  }

  /**
   * Log application errors with context
   */
  logApplicationError(error: Error, context: string, meta?: any): void {
    this.error(`Application Error in ${context}`, error, {
      context,
      errorName: error.name,
      ...meta,
    });
  }

  /**
   * Log security events
   */
  logSecurity(
    event: string,
    severity: 'low' | 'medium' | 'high',
    meta?: any,
  ): void {
    this.warn(`Security Event: ${event}`, {
      event,
      severity,
      timestamp: new Date().toISOString(),
      ...meta,
    });
  }

  /**
   * Get the underlying Winston logger instance
   */
  getLogger(): winston.Logger {
    return this.logger;
  }
}
