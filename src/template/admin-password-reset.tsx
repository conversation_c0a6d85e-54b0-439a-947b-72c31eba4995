import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface AdminPasswordResetEmailProps {
  adminName: string;
  resetUrl: string;
}

export default function AdminPasswordResetEmail({
  adminName = 'Admin',
  resetUrl = 'https://careerireland.com/admin/reset-password',
}: AdminPasswordResetEmailProps) {
  return (
    <Html>
      <Head />
      <Preview>Reset your CareerIreland Admin password</Preview>
      <Body style={main}>
        <Container style={container}>
          <Container style={logoSection}>
            <Link href={process.env.WEBSITE} style={logoLink}>
              <Img
                src="https://supabase.careerireland.com/storage/v1/object/public/careerireland-prod/logo/logo-careerireland.webp"
                width="80"
                height="80"
                alt="CareerIreland Logo"
                style={logoImage}
              />
            </Link>
          </Container>

          <Heading style={h1}>Admin Password Reset Request</Heading>

          <Text style={text}>Hi {adminName},</Text>

          <Text style={text}>
            We received a request to reset the password for your CareerIreland admin account. 
            If you made this request, click the button below to reset your password.
          </Text>

          <Section style={buttonContainer}>
            <Button style={button} href={resetUrl}>
              Reset Admin Password
            </Button>
          </Section>

          <Text style={text}>
            This password reset link will expire in <strong>1 hour</strong> for security purposes.
          </Text>

          <Hr style={hr} />

          <Text style={securityText}>
            <strong>Security Notice:</strong>
          </Text>
          
          <Text style={text}>
            • If you didn't request this password reset, you can safely ignore this email
          </Text>
          <Text style={text}>
            • Your password will remain unchanged unless you click the reset link above
          </Text>
          <Text style={text}>
            • Never share your password or reset links with anyone
          </Text>
          <Text style={text}>
            • If you're having trouble, contact our support team immediately
          </Text>
          <Text style={text}>
            • As an admin user, please ensure your account security is maintained at all times
          </Text>

          <Hr style={hr} />

          <Text style={footerText}>
            If the button above doesn't work, you can copy and paste this link into your browser:
          </Text>
          <Text style={linkText}>{resetUrl}</Text>

          <Text style={text}>
            Best regards,
            <br />
            The CareerIreland Security Team
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  border: '1px solid #f0f0f0',
  borderRadius: '5px',
  boxShadow: '0 5px 10px rgba(20, 50, 70, .2)',
  margin: '0 auto',
  maxWidth: '600px',
  padding: '68px 0 130px',
};

const logoSection = {
  display: 'flex',
  justifyContent: 'center',
  marginBottom: '32px',
};

const logoLink = {
  textDecoration: 'none',
};

const logoImage = {
  borderRadius: '50%',
  display: 'block',
  outline: 'none',
  border: 'none',
  textDecoration: 'none',
};

const h1 = {
  color: '#1a1a1a',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
  textAlign: 'center' as const,
};

const text = {
  color: '#484848',
  fontSize: '16px',
  lineHeight: '26px',
  textAlign: 'left' as const,
  margin: '16px 68px',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#dc2626',
  borderRadius: '4px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px 20px',
  margin: '0 auto',
  maxWidth: '200px',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '20px 68px',
};

const securityText = {
  color: '#dc2626',
  fontSize: '16px',
  fontWeight: 'bold',
  margin: '16px 68px 8px',
};

const footerText = {
  color: '#9ca299',
  fontSize: '14px',
  lineHeight: '22px',
  margin: '16px 68px 8px',
};

const linkText = {
  color: '#556cd6',
  fontSize: '14px',
  textDecoration: 'underline',
  margin: '0 68px 16px',
  wordBreak: 'break-all' as const,
};
