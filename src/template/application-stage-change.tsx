import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Row,
  Hr,
  Button,
} from '@react-email/components';
import { format } from 'date-fns';
import * as React from 'react';

// Define the type for the application stage change data
type ApplicationStageChangeData = {
  applicantName: string;
  applicationId: string;
  serviceName: string;
  previousStage?: string;
  currentStage: string;
  previousStatus?: string;
  currentStatus?: string;
  stageChangeDate?: Date;
  nextSteps?: string[];
  stageDescription?: string;
  estimatedDuration?: string;
  additionalNotes?: string;
  websiteUrl?: string;
};

// Stage color helper
const getStageColor = (stage: string) => {
  switch (stage.toLowerCase()) {
    case 'document collection':
    case 'initial submission':
      return '#3B82F6'; // blue
    case 'initial review':
    case 'document review':
    case 'under review':
      return '#F59E0B'; // amber
    case 'verification':
    case 'background check':
      return '#8B5CF6'; // purple
    case 'final review':
    case 'decision pending':
      return '#EF4444'; // red
    case 'approved':
    case 'completed':
    case 'finalized':
      return '#10B981'; // green
    default:
      return '#6B7280'; // gray
  }
};

// Stage message helper
const getStageMessage = (stage: string) => {
  switch (stage.toLowerCase()) {
    case 'document collection':
    case 'initial submission':
      return 'Your application has moved to the document collection stage. Please ensure all required documents are submitted.';
    case 'initial review':
      return 'Your application is now under initial review by our team. We will assess your submitted documents and information.';
    case 'document review':
      return 'Our team is currently reviewing your submitted documents for completeness and accuracy.';
    case 'verification':
      return 'Your application has moved to the verification stage. We are verifying the information and documents you provided.';
    case 'background check':
      return 'Your application is currently undergoing background verification as part of our standard process.';
    case 'final review':
      return 'Your application has reached the final review stage. A decision will be made shortly.';
    case 'decision pending':
      return 'Your application is in the final decision stage. We will notify you of the outcome soon.';
    case 'approved':
      return 'Congratulations! Your application has been approved and moved to the completion stage.';
    case 'completed':
    case 'finalized':
      return 'Great news! Your application process has been completed successfully.';
    default:
      return `Your application has progressed to the ${stage} stage.`;
  }
};

export default function ApplicationStageChangeEmail({
  applicantName = 'User',
  applicationId = 'APP-001',
  serviceName = 'Career Service',
  previousStage,
  currentStage = 'Under Review',
  previousStatus,
  currentStatus,
  stageChangeDate = new Date(),
  nextSteps = [],
  stageDescription,
  estimatedDuration,
  additionalNotes,
  websiteUrl = `${process.env.WEBSITE}/auth/login`,
}: ApplicationStageChangeData) {
  const formattedDate = format(
    new Date(stageChangeDate),
    "MMM d, yyyy 'at' h:mm a",
  );

  return (
    <Html>
      <Head />
      <Preview>
        Application Stage Update: {serviceName} - {currentStage}
      </Preview>
      <Body style={styles.body}>
        <Container style={styles.container}>
          <Section style={styles.main}>
            <Heading style={styles.heading}>
              Application Stage Update
            </Heading>
            <Text style={styles.paragraph}>
              Dear {applicantName},
            </Text>
            <Text style={styles.paragraph}>
              {getStageMessage(currentStage)}
            </Text>

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                Application Details
              </Heading>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Application ID:</strong> {applicationId}
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Service:</strong> {serviceName}
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Stage Updated:</strong> {formattedDate}
                </Text>
              </Row>
            </Section>

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                Stage Information
              </Heading>
              {previousStage && (
                <Row style={styles.row}>
                  <Text style={styles.label}>
                    <strong>Previous Stage:</strong>{' '}
                    <span
                      style={{
                        ...styles.value,
                        color: getStageColor(previousStage),
                        fontWeight: 'bold',
                      }}
                    >
                      {previousStage.toUpperCase()}
                    </span>
                  </Text>
                </Row>
              )}
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Current Stage:</strong>{' '}
                  <span
                    style={{
                      ...styles.value,
                      color: getStageColor(currentStage),
                      fontWeight: 'bold',
                    }}
                  >
                    {currentStage.toUpperCase()}
                  </span>
                </Text>
              </Row>
              {currentStatus && (
                <Row style={styles.row}>
                  <Text style={styles.label}>
                    <strong>Current Status:</strong>{' '}
                    <span style={styles.value}>{currentStatus}</span>
                  </Text>
                </Row>
              )}
              {estimatedDuration && (
                <Row style={styles.row}>
                  <Text style={styles.label}>
                    <strong>Estimated Duration:</strong>{' '}
                    <span style={styles.value}>{estimatedDuration}</span>
                  </Text>
                </Row>
              )}
            </Section>

            {stageDescription && (
              <Section style={styles.card}>
                <Heading as="h2" style={styles.subheading}>
                  Stage Description
                </Heading>
                <Text style={styles.paragraph}>
                  {stageDescription}
                </Text>
              </Section>
            )}

            {nextSteps && nextSteps.length > 0 && (
              <Section style={styles.card}>
                <Heading as="h2" style={styles.subheading}>
                  Next Steps
                </Heading>
                <Text style={styles.paragraph}>
                  Please take the following actions:
                </Text>
                {nextSteps.map((step, index) => (
                  <Row key={index} style={styles.row}>
                    <Text style={styles.listItem}>
                      {index + 1}. {step}
                    </Text>
                  </Row>
                ))}
              </Section>
            )}

            {additionalNotes && (
              <>
                <Hr style={styles.divider} />
                <Section style={styles.card}>
                  <Heading as="h2" style={styles.subheading}>
                    Additional Information
                  </Heading>
                  <Text style={styles.paragraph}>
                    {additionalNotes}
                  </Text>
                </Section>
              </>
            )}

            <Section style={styles.buttonSection}>
              <Button href={websiteUrl} style={styles.button}>
                View Application Details
              </Button>
            </Section>

            <Text style={styles.paragraph}>
              If you have any questions about this stage update or need assistance with your application, please don't hesitate to contact our support team. We're here to help you throughout the process.
            </Text>

            <Text style={styles.paragraph}>
              Thank you for your patience and for choosing our services.
            </Text>

            <Text style={styles.paragraph}>
              Best regards,<br />
              The Careerireland Team
            </Text>
          </Section>

          <Section style={styles.footer}>
            <Text style={styles.footerText}>
              © {new Date().getFullYear()} Careerireland all rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles consistent with existing templates
const styles = {
  body: {
    backgroundColor: '#f6f9fc',
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
  },
  container: {
    margin: '0 auto',
    padding: '20px 0',
    maxWidth: '600px',
  },
  main: {
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    padding: '40px 20px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
  },
  heading: {
    fontSize: '24px',
    lineHeight: '1.3',
    fontWeight: '700',
    color: '#333',
    textAlign: 'center' as const,
    margin: '0 0 24px',
  },
  subheading: {
    fontSize: '18px',
    lineHeight: '1.3',
    fontWeight: '600',
    color: '#333',
    margin: '0 0 16px',
  },
  paragraph: {
    fontSize: '16px',
    lineHeight: '1.5',
    color: '#4a5568',
    margin: '0 0 24px',
  },
  card: {
    backgroundColor: '#f9fafb',
    borderRadius: '6px',
    padding: '20px',
    marginBottom: '24px',
  },
  row: {
    marginBottom: '8px',
  },
  label: {
    fontSize: '14px',
    color: '#000',
    margin: '0',
  },
  listItem: {
    fontSize: '14px',
    color: '#4a5568',
    margin: '0',
    lineHeight: '1.5',
  },
  value: {
    fontSize: '14px',
    color: '#2d3748',
    fontWeight: '500',
    margin: '0',
  },
  divider: {
    borderColor: '#e2e8f0',
    margin: '24px 0',
  },
  button: {
    backgroundColor: '#4f46e5',
    borderRadius: '4px',
    color: '#fff',
    fontSize: '14px',
    fontWeight: '600',
    padding: '12px 24px',
    textDecoration: 'none',
    textAlign: 'center' as const,
    display: 'inline-block',
  },
  buttonSection: {
    textAlign: 'center' as const,
    margin: '32px 0',
  },
  footer: {
    textAlign: 'center' as const,
    padding: '20px',
  },
  footerText: {
    fontSize: '12px',
    color: '#a0aec0',
    margin: '4px 0',
  },
};
