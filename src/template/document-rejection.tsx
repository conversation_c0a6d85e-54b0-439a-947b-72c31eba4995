import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Row,
  Hr,
  Button,
} from '@react-email/components';
import { format } from 'date-fns';
import * as React from 'react';

// Define the type for rejected document information
type RejectedDocument = {
  documentName: string;
  rejectionReason: string;
  specificIssues?: string[];
};

// Define the type for the document rejection data
type DocumentRejectionData = {
  applicantName: string;
  applicationId: string;
  serviceName: string;
  rejectedDocuments: RejectedDocument[];
  rejectionDate?: Date;
  resubmissionDeadline?: string;
  websiteUrl?: string;
  supportEmail?: string;
  generalGuidelines?: string[];
};

export default function DocumentRejectionEmail({
  applicantName = 'User',
  applicationId = 'APP-001',
  serviceName = 'Career Service',
  rejectedDocuments = [
    {
      documentName: 'CV/Resume',
      rejectionReason: 'Document quality issues',
      specificIssues: ['Image is blurry', 'Text is not clearly readable'],
    },
  ],
  rejectionDate = new Date(),
  resubmissionDeadline,
  websiteUrl = process.env.WEBSITE || 'http://localhost:3001',
  supportEmail = '<EMAIL>',
  generalGuidelines = [
    'Ensure documents are clear and legible',
    'Use high-quality scans or photos',
    'Save files in PDF, JPG, or PNG format',
    'Make sure all text is readable',
    'Include all required information',
  ],
}: DocumentRejectionData) {
  const formattedDate = format(
    new Date(rejectionDate),
    "MMM d, yyyy 'at' h:mm a",
  );

  return (
    <Html>
      <Head />
      <Preview>
        Document Resubmission Required: {serviceName} - Please review and resubmit
      </Preview>
      <Body style={styles.body}>
        <Container style={styles.container}>
          <Section style={styles.main}>
            <Heading style={styles.heading}>
              Document Resubmission Required
            </Heading>
            <Text style={styles.paragraph}>
              Dear {applicantName},
            </Text>
            <Text style={styles.paragraph}>
              Thank you for submitting your documents for the {serviceName} application. After careful review, we need to request resubmission of some documents to ensure they meet our quality standards.
            </Text>
            <Text style={styles.supportiveText}>
              Don't worry - this is a common part of the process, and we're here to help you get everything sorted quickly.
            </Text>

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                Application Details
              </Heading>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Application ID:</strong> {applicationId}
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Service:</strong> {serviceName}
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Review Date:</strong> {formattedDate}
                </Text>
              </Row>
            </Section>

            <Section style={styles.rejectionCard}>
              <Heading as="h2" style={styles.subheading}>
                Documents Requiring Resubmission
              </Heading>
              {rejectedDocuments.map((doc, index) => (
                <div key={index} style={styles.documentItem}>
                  <Text style={styles.documentName}>
                    <strong>📄 {doc.documentName}</strong>
                  </Text>
                  <Text style={styles.rejectionReason}>
                    <strong>Reason:</strong> {doc.rejectionReason}
                  </Text>
                  {doc.specificIssues && doc.specificIssues.length > 0 && (
                    <>
                      <Text style={styles.issuesHeader}>
                        <strong>Specific Issues:</strong>
                      </Text>
                      {doc.specificIssues.map((issue, issueIndex) => (
                        <Row key={issueIndex} style={styles.row}>
                          <Text style={styles.issueItem}>
                            • {issue}
                          </Text>
                        </Row>
                      ))}
                    </>
                  )}
                  {index < rejectedDocuments.length - 1 && <Hr style={styles.documentDivider} />}
                </div>
              ))}
            </Section>

            {resubmissionDeadline && (
              <Section style={styles.deadlineCard}>
                <Heading as="h2" style={styles.subheading}>
                  ⏰ Resubmission Deadline
                </Heading>
                <Text style={styles.deadlineText}>
                  <strong>Please resubmit the corrected documents by: {resubmissionDeadline}</strong>
                </Text>
                <Text style={styles.paragraph}>
                  This will help ensure your application continues to be processed without delays.
                </Text>
              </Section>
            )}

            <Hr style={styles.divider} />

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                📋 Document Submission Guidelines
              </Heading>
              <Text style={styles.paragraph}>
                To ensure your resubmitted documents are accepted, please follow these guidelines:
              </Text>
              {generalGuidelines.map((guideline, index) => (
                <Row key={index} style={styles.row}>
                  <Text style={styles.listItem}>
                    • {guideline}
                  </Text>
                </Row>
              ))}
            </Section>

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                How to Resubmit Documents
              </Heading>
              <Text style={styles.paragraph}>
                Follow these steps to resubmit your corrected documents:
              </Text>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  1. Review the specific issues mentioned above for each document
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  2. Correct the issues and prepare new versions of the documents
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  3. Click the button below to access your application
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  4. Upload the corrected documents to replace the rejected ones
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  5. Submit for review once all documents are uploaded
                </Text>
              </Row>
            </Section>

            <Section style={styles.buttonSection}>
              <Button href={websiteUrl} style={styles.button}>
                Resubmit Documents
              </Button>
            </Section>

            <Text style={styles.paragraph}>
              We understand that document requirements can sometimes be confusing. If you need any clarification about the rejection reasons or help with preparing your documents, please don't hesitate to reach out to our support team at{' '}
              <a href={`mailto:${supportEmail}`} style={styles.link}>
                {supportEmail}
              </a>.
            </Text>

            <Text style={styles.supportiveText}>
              We're committed to helping you succeed and look forward to reviewing your resubmitted documents soon.
            </Text>

            <Text style={styles.paragraph}>
              Best regards,<br />
              The Careerireland Team
            </Text>
          </Section>

          <Section style={styles.footer}>
            <Text style={styles.footerText}>
              © {new Date().getFullYear()} Careerireland all rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles (same as purchase-notification.tsx with additional styles)
const styles = {
  body: {
    backgroundColor: '#f6f9fc',
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
  },
  container: {
    margin: '0 auto',
    padding: '20px 0',
    maxWidth: '600px',
  },
  header: {
    padding: '20px',
    textAlign: 'center' as const,
  },
  main: {
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    padding: '40px 20px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
  },
  heading: {
    fontSize: '24px',
    lineHeight: '1.3',
    fontWeight: '700',
    color: '#333',
    textAlign: 'center' as const,
    margin: '0 0 24px',
  },
  subheading: {
    fontSize: '18px',
    lineHeight: '1.3',
    fontWeight: '600',
    color: '#333',
    margin: '0 0 16px',
  },
  paragraph: {
    fontSize: '16px',
    lineHeight: '1.5',
    color: '#4a5568',
    margin: '0 0 24px',
  },
  supportiveText: {
    fontSize: '16px',
    lineHeight: '1.5',
    color: '#059669',
    margin: '0 0 24px',
    fontStyle: 'italic',
  },
  card: {
    backgroundColor: '#f9fafb',
    borderRadius: '6px',
    padding: '20px',
    marginBottom: '24px',
  },
  rejectionCard: {
    backgroundColor: '#fef2f2',
    borderRadius: '6px',
    padding: '20px',
    marginBottom: '24px',
    border: '1px solid #fecaca',
  },
  deadlineCard: {
    backgroundColor: '#fef3cd',
    borderRadius: '6px',
    padding: '20px',
    marginBottom: '24px',
    border: '1px solid #fbbf24',
  },
  deadlineText: {
    fontSize: '16px',
    lineHeight: '1.5',
    color: '#92400e',
    margin: '0 0 16px',
  },
  documentItem: {
    marginBottom: '16px',
  },
  documentName: {
    fontSize: '16px',
    color: '#dc2626',
    margin: '0 0 8px',
    fontWeight: '600',
  },
  rejectionReason: {
    fontSize: '14px',
    color: '#7f1d1d',
    margin: '0 0 8px',
  },
  issuesHeader: {
    fontSize: '14px',
    color: '#7f1d1d',
    margin: '0 0 4px',
  },
  issueItem: {
    fontSize: '13px',
    color: '#991b1b',
    margin: '0',
    lineHeight: '1.4',
    paddingLeft: '8px',
  },
  documentDivider: {
    borderColor: '#fecaca',
    margin: '16px 0',
  },
  row: {
    marginBottom: '8px',
  },
  label: {
    fontSize: '14px',
    color: '#000',
    margin: '0',
  },
  listItem: {
    fontSize: '14px',
    color: '#4a5568',
    margin: '0',
    lineHeight: '1.5',
  },
  value: {
    fontSize: '14px',
    color: '#2d3748',
    fontWeight: '500',
    margin: '0',
  },
  divider: {
    borderColor: '#e2e8f0',
    margin: '24px 0',
  },
  link: {
    color: '#3182ce',
    textDecoration: 'none',
  },
  button: {
    backgroundColor: '#4f46e5',
    borderRadius: '4px',
    color: '#fff',
    fontSize: '14px',
    fontWeight: '600',
    padding: '12px 24px',
    textDecoration: 'none',
    textAlign: 'center' as const,
    display: 'inline-block',
  },
  buttonSection: {
    textAlign: 'center' as const,
    margin: '32px 0',
  },
  footer: {
    textAlign: 'center' as const,
    padding: '20px',
  },
  footerText: {
    fontSize: '12px',
    color: '#a0aec0',
    margin: '4px 0',
  },
};
