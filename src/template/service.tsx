import {
  Body,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

import * as React from 'react';
import { format } from 'date-fns';
interface PaymentSuccessEmailProps {
  service: {
    id: string;
    createdAt: Date;
    status: string;
    mentor?: string;
    name: string;
    amount: number;
  };
  user: {
    email: string;
    name: string;
  };
}

export default function MentorPaymentSuccessEmail({
  service = {
    id: '',
    createdAt: new Date(),
    name: '',
    status: '',
    amount: 0,
  },
  user = {
    email: '',
    name: '',
  },
}: PaymentSuccessEmailProps) {
  return (
    <Html>
      <Head />
      <Preview>
        Your payment was {service.status === 'paid' ? 'successful' : 'failed'}
      </Preview>
      <Body style={main}>
        <Container style={container}>
          <Container style={logoSection}>
            <Link href={process.env.WEBSITE} style={logoLink}>
              <Img
                src="https://supabase.careerireland.com/storage/v1/object/public/careerireland-prod/logo/logo-careerireland.webp"
                width="80"
                height="80"
                alt="Logo"
                style={logoImage}
              />
            </Link>
          </Container>
          <Heading style={h1}>
            Payment {service.status === 'paid' ? 'Successful' : 'Failed'}
          </Heading>

          <Text style={text}>Hi {user?.name},</Text>

          <Text style={text}>
            Thank you for your payment. We're writing to confirm that your
            payment has been{' '}
            {service.status === 'paid' ? 'processed successfully' : 'failed'}.
          </Text>

          <Section style={boxContainer}>
            <Text style={boxHeading}>Payment Details</Text>
            <Hr style={hr} />
            {service?.mentor && (
              <Text style={detailRow}>
                <strong>Mentor:</strong> {service.mentor}
              </Text>
            )}
            <Text style={detailRow}>
              <strong>Service:</strong> {service.name}
            </Text>
            <Text style={detailRow}>
              <strong>Amount:</strong>{' '}
              {new Intl.NumberFormat('en-IE', {
                style: 'currency',
                currency: 'EUR',
              }).format(service?.amount || 0)}
            </Text>
            <Text style={detailRow}>
              <strong>Date:</strong>{' '}
              {format(
                new Date(service?.createdAt || new Date()),
                'MMMM dd, yyyy',
              )}
            </Text>
            <Text style={detailRow}>
              <strong>Status:</strong>{' '}
              <span style={statusStyle}>{service?.status}</span>
            </Text>
          </Section>

          <Text style={text}>
            NOTE: Charges include VAT. This is a non-refundable and
            non-transferable payment.
          </Text>
          <Text style={text}>
            If you have any questions about your payment, please don't hesitate
            to{' '}
            <Link href={`${process.env.WEBSITE}/contact-us`} style={link}>
              contact our support team
            </Link>
            .
          </Text>

          <Hr style={hr} />

          <Text style={footer}>
            This email was sent to {user?.email}. If you didn't make this
            payment, please contact us immediately.
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '48px 24px',
  maxWidth: '600px',
};

const logoLink = {
  display: 'inline-block',
  textDecoration: 'none',
};

const logoSection = {
  textAlign: 'center' as const,
  marginBottom: '32px',
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'center',
  maxWidth: '600px',
};

const logoImage = {
  margin: '0 auto',
  borderRadius: '12px',
  padding: '12px',
  backgroundColor: '#f8fafc',
  objectFit: 'contain' as const,
  transition: 'all 0.2s ease',
};

const h1 = {
  color: '#484848',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '1.4',
  margin: '0 0 20px',
};

const text = {
  color: '#484848',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '16px 0',
};

const boxContainer = {
  background: '#f9f9f9',
  border: '1px solid #e9e9e9',
  borderRadius: '5px',
  padding: '20px',
  margin: '20px 0',
};

const boxHeading = {
  fontSize: '18px',
  fontWeight: '600',
  margin: '0 0 10px',
};

const detailRow = {
  margin: '8px 0',
};

const hr = {
  borderColor: '#e9e9e9',
  margin: '20px 0',
};

const footer = {
  color: '#9898a6',
  fontSize: '12px',
  lineHeight: '16px',
};

const link = {
  color: '#5469d4',
  textDecoration: 'underline',
};

const statusStyle = {
  textTransform: 'capitalize' as const,
  color: '#0f766e',
  fontWeight: '500',
};
