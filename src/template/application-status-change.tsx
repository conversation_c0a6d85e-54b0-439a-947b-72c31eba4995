import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Row,
  Hr,
  Button,
} from '@react-email/components';
import { format } from 'date-fns';
import * as React from 'react';

// Define the type for the application status change data
type ApplicationStatusChangeData = {
  applicantName: string;
  applicationId: string;
  serviceName: string;
  previousStatus?: string;
  currentStatus: string;
  statusChangeDate?: Date;
  nextSteps?: string[];
  additionalNotes?: string;
  websiteUrl?: string;
};

// Status color helper
const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'approved':
    case 'completed':
    case 'accepted':
      return '#10B981'; // green
    case 'pending':
    case 'in review':
    case 'under review':
      return '#F59E0B'; // amber
    case 'rejected':
    case 'declined':
    case 'cancelled':
      return '#EF4444'; // red
    case 'documents required':
    case 'awaiting documents':
      return '#3B82F6'; // blue
    default:
      return '#6B7280'; // gray
  }
};

// Status message helper
const getStatusMessage = (status: string) => {
  switch (status.toLowerCase()) {
    case 'approved':
      return 'Congratulations! Your application has been approved.';
    case 'completed':
      return 'Great news! Your application has been completed successfully.';
    case 'pending':
      return 'Your application is currently pending review.';
    case 'in review':
    case 'under review':
      return 'Your application is currently being reviewed by our team.';
    case 'rejected':
    case 'declined':
      return 'We regret to inform you that your application has been declined.';
    case 'documents required':
    case 'awaiting documents':
      return 'Your application requires additional documents to proceed.';
    default:
      return `Your application status has been updated to: ${status}`;
  }
};

export default function ApplicationStatusChangeEmail({
  applicantName = 'User',
  applicationId = 'APP-001',
  serviceName = 'Career Service',
  previousStatus,
  currentStatus = 'Pending',
  statusChangeDate = new Date(),
  nextSteps = [],
  additionalNotes,
  websiteUrl = `${process.env.WEBSITE}/auth/login`,
}: ApplicationStatusChangeData) {
  const formattedDate = format(
    new Date(statusChangeDate),
    "MMM d, yyyy 'at' h:mm a",
  );

  return (
    <Html>
      <Head />
      <Preview>
        Application Status Update: {serviceName} - {currentStatus}
      </Preview>
      <Body style={styles.body}>
        <Container style={styles.container}>
          <Section style={styles.main}>
            <Heading style={styles.heading}>
              Application Status Update
            </Heading>
            <Text style={styles.paragraph}>
              Dear {applicantName},
            </Text>
            <Text style={styles.paragraph}>
              {getStatusMessage(currentStatus)}
            </Text>

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                Application Details
              </Heading>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Application ID:</strong> {applicationId}
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Service:</strong> {serviceName}
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Status Updated:</strong> {formattedDate}
                </Text>
              </Row>
            </Section>

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                Status Information
              </Heading>
              {previousStatus && (
                <Row style={styles.row}>
                  <Text style={styles.label}>
                    <strong>Previous Status:</strong>{' '}
                    <span
                      style={{
                        ...styles.value,
                        color: getStatusColor(previousStatus),
                        fontWeight: 'bold',
                      }}
                    >
                      {previousStatus.toUpperCase()}
                    </span>
                  </Text>
                </Row>
              )}
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Current Status:</strong>{' '}
                  <span
                    style={{
                      ...styles.value,
                      color: getStatusColor(currentStatus),
                      fontWeight: 'bold',
                    }}
                  >
                    {currentStatus.toUpperCase()}
                  </span>
                </Text>
              </Row>
            </Section>

            {nextSteps && nextSteps.length > 0 && (
              <Section style={styles.card}>
                <Heading as="h2" style={styles.subheading}>
                  Next Steps
                </Heading>
                <Text style={styles.paragraph}>
                  Please take the following actions:
                </Text>
                {nextSteps.map((step, index) => (
                  <Row key={index} style={styles.row}>
                    <Text style={styles.listItem}>
                      {index + 1}. {step}
                    </Text>
                  </Row>
                ))}
              </Section>
            )}

            {additionalNotes && (
              <>
                <Hr style={styles.divider} />
                <Section style={styles.card}>
                  <Heading as="h2" style={styles.subheading}>
                    Additional Information
                  </Heading>
                  <Text style={styles.paragraph}>
                    {additionalNotes}
                  </Text>
                </Section>
              </>
            )}

            <Section style={styles.buttonSection}>
              <Button href={websiteUrl} style={styles.button}>
                View Application Details
              </Button>
            </Section>

            <Text style={styles.paragraph}>
              If you have any questions about this status update or need assistance with your application, please don't hesitate to contact our support team. We're here to help you throughout the process.
            </Text>

            <Text style={styles.paragraph}>
              Thank you for your patience and for choosing our services.
            </Text>

            <Text style={styles.paragraph}>
              Best regards,<br />
              The Careerireland Team
            </Text>
          </Section>

          <Section style={styles.footer}>
            <Text style={styles.footerText}>
              © {new Date().getFullYear()} Careerireland all rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles (same as purchase-notification.tsx)
const styles = {
  body: {
    backgroundColor: '#f6f9fc',
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
  },
  container: {
    margin: '0 auto',
    padding: '20px 0',
    maxWidth: '600px',
  },
  header: {
    padding: '20px',
    textAlign: 'center' as const,
  },
  main: {
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    padding: '40px 20px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
  },
  heading: {
    fontSize: '24px',
    lineHeight: '1.3',
    fontWeight: '700',
    color: '#333',
    textAlign: 'center' as const,
    margin: '0 0 24px',
  },
  subheading: {
    fontSize: '18px',
    lineHeight: '1.3',
    fontWeight: '600',
    color: '#333',
    margin: '0 0 16px',
  },
  paragraph: {
    fontSize: '16px',
    lineHeight: '1.5',
    color: '#4a5568',
    margin: '0 0 24px',
  },
  card: {
    backgroundColor: '#f9fafb',
    borderRadius: '6px',
    padding: '20px',
    marginBottom: '24px',
  },
  row: {
    marginBottom: '8px',
  },
  label: {
    fontSize: '14px',
    color: '#000',
    margin: '0',
  },
  listItem: {
    fontSize: '14px',
    color: '#4a5568',
    margin: '0',
    lineHeight: '1.5',
  },
  value: {
    fontSize: '14px',
    color: '#2d3748',
    fontWeight: '500',
    margin: '0',
  },
  divider: {
    borderColor: '#e2e8f0',
    margin: '24px 0',
  },
  link: {
    color: '#3182ce',
    textDecoration: 'none',
  },
  button: {
    backgroundColor: '#4f46e5',
    borderRadius: '4px',
    color: '#fff',
    fontSize: '14px',
    fontWeight: '600',
    padding: '12px 24px',
    textDecoration: 'none',
    textAlign: 'center' as const,
    display: 'inline-block',
  },
  buttonSection: {
    textAlign: 'center' as const,
    margin: '32px 0',
  },
  footer: {
    textAlign: 'center' as const,
    padding: '20px',
  },
  footerText: {
    fontSize: '12px',
    color: '#a0aec0',
    margin: '4px 0',
  },
};
