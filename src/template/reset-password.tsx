import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface ResetPasswordEmailProps {
  name?: string;
  url?: string;
}

export const ResetPasswordEmail = ({
  name = 'there',
  url = 'https://example.com/reset-password',
}: ResetPasswordEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>Reset your password</Preview>
      <Body style={main}>
        <Container style={container}>
          <Container style={logoSection}>
            <Link href={process.env.WEBSITE} style={logoLink}>
              <Img
                src="https://supabase.careerireland.com/storage/v1/object/public/careerireland-prod/logo/logo-careerireland.webp"
                width="80"
                height="80"
                alt="Logo"
                style={logoImage}
              />
            </Link>
          </Container>
          <Section style={logo}>
            <Text style={logoText}>Career Ireland</Text>
          </Section>

          <Section style={content}>
            <Text style={heading}>Hi {name},</Text>
            <Text style={paragraph}>
              Someone recently requested a password reset for your account. If
              this was you, you can reset your password by clicking the button
              below:
            </Text>

            <Button style={button} href={url}>
              Reset Password
            </Button>

            <Text style={paragraph}>
              If you didn't request this, you can safely ignore this email. Your
              password will remain unchanged.
            </Text>

            <Hr style={hr} />

            <Text style={footer}>. This link will expire in 15 min.</Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
};

const logoLink = {
  display: 'inline-block',
  textDecoration: 'none',
};

const logoSection = {
  textAlign: 'center' as const,
  marginBottom: '32px',
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'center',
  maxWidth: '600px',
};

const logoImage = {
  margin: '0 auto',
  borderRadius: '12px',
  padding: '12px',
  backgroundColor: '#f8fafc',
  objectFit: 'contain' as const,
  transition: 'all 0.2s ease',
};
const logo = {
  padding: '32px 48px',
};

const logoText = {
  fontSize: '24px',
  fontWeight: 'bold',
  textAlign: 'center' as const,
  margin: '0',
};

const content = {
  padding: '0 48px',
};

const heading = {
  fontSize: '24px',
  letterSpacing: '-0.5px',
  lineHeight: '1.3',
  fontWeight: '400',
  color: '#484848',
  padding: '17px 0 0',
};

const paragraph = {
  margin: '0 0 15px',
  fontSize: '15px',
  lineHeight: '1.4',
  color: '#3c4149',
};

const button = {
  backgroundColor: '#656ee8',
  borderRadius: '5px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '100%',
  padding: '12px 20px',
  margin: '27px 0',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '20px 0',
};

const footer = {
  color: '#8898aa',
  fontSize: '12px',
  lineHeight: '16px',
};

export default ResetPasswordEmail;
