import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

import * as React from 'react';

interface AgentWelcomeEmailProps {
  agentName: string;
  agentEmail: string;
  temporaryPassword: string;
  loginUrl?: string;
}

export default function AgentWelcomeEmail({
  agentName = 'Agent',
  agentEmail = '<EMAIL>',
  temporaryPassword = 'TEMP123',
  loginUrl = process.env.WEBSITE,
}: AgentWelcomeEmailProps) {
  return (
    <Html>
      <Head />
      <Preview>Welcome to CareerIreland Agent Portal</Preview>
      <Body style={main}>
        <Container style={container}>
          <Container style={logoSection}>
            <Link href={process.env.WEBSITE} style={logoLink}>
              <Img
                src="https://supabase.careerireland.com/storage/v1/object/public/careerireland-prod/logo/logo-careerireland.webp"
                width="80"
                height="80"
                alt="CareerIreland Logo"
                style={logoImage}
              />
            </Link>
          </Container>
          
          <Heading style={h1}>Welcome to CareerIreland Agent Portal</Heading>

          <Text style={text}>Hi {agentName},</Text>

          <Text style={text}>
            Welcome to the CareerIreland team! Your agent account has been successfully created. 
            You now have access to our agent portal where you can manage applications, 
            communicate with clients, and track progress.
          </Text>

          <Section style={credentialsContainer}>
            <Text style={credentialsTitle}>Your Login Credentials</Text>
            <Text style={credentialItem}>
              <strong>Email:</strong> {agentEmail}
            </Text>
            <Text style={credentialItem}>
              <strong>Temporary Password:</strong> <span style={passwordText}>{temporaryPassword}</span>
            </Text>
          </Section>

          <Text style={warningText}>
            ⚠️ <strong>Important:</strong> Please log in and change your password immediately for security purposes.
          </Text>

          <Section style={buttonContainer}>
            <Button style={button} href={loginUrl}>
              Login to Agent Portal
            </Button>
          </Section>

          <Text style={text}>
            <strong>What you can do in the Agent Portal:</strong>
          </Text>
          
          <Text style={listItem}>• Manage assigned applications</Text>
          <Text style={listItem}>• Update application status and progress</Text>
          <Text style={listItem}>• Communicate with clients</Text>
          <Text style={listItem}>• Upload and review documents</Text>
          <Text style={listItem}>• Track application timelines</Text>

          <Text style={text}>
            If you have any questions or need assistance getting started, 
            please don't hesitate to contact our support team.
          </Text>

          <Text style={text}>
            Welcome aboard!
            <br />
            The CareerIreland Team
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  padding: '0 48px',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '24px auto',
  padding: '48px 24px',
  borderRadius: '8px',
  maxWidth: '600px',
  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
};

const logoLink = {
  display: 'inline-block',
  textDecoration: 'none',
};

const logoSection = {
  textAlign: 'center' as const,
  marginBottom: '32px',
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'center',
  maxWidth: '600px',
};

const logoImage = {
  margin: '0 auto',
  borderRadius: '12px',
  padding: '12px',
  backgroundColor: '#f8fafc',
  objectFit: 'contain' as const,
  transition: 'all 0.2s ease',
};

const h1 = {
  color: '#1f2937',
  fontSize: '28px',
  fontWeight: '700',
  lineHeight: '1.3',
  margin: '0 0 32px',
  textAlign: 'center' as const,
};

const text = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '16px 0',
};

const credentialsContainer = {
  backgroundColor: '#f8fafc',
  border: '2px solid #e5e7eb',
  borderRadius: '8px',
  padding: '24px',
  margin: '32px 0',
};

const credentialsTitle = {
  color: '#1f2937',
  fontSize: '18px',
  fontWeight: '600',
  margin: '0 0 16px',
  textAlign: 'center' as const,
};

const credentialItem = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '8px 0',
};

const passwordText = {
  backgroundColor: '#fef3c7',
  padding: '4px 8px',
  borderRadius: '4px',
  fontFamily: 'monospace',
  fontSize: '14px',
  fontWeight: '600',
  color: '#92400e',
};

const warningText = {
  color: '#dc2626',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '24px 0',
  padding: '16px',
  backgroundColor: '#fef2f2',
  border: '1px solid #fecaca',
  borderRadius: '6px',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#3b82f6',
  borderRadius: '6px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '14px 28px',
  margin: '0 auto',
  transition: 'background-color 0.2s ease',
};

const listItem = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '8px 0',
  paddingLeft: '8px',
};
