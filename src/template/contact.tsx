import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Link,
  Preview,
  Section,
  Text,
  Row,
  Column,
} from '@react-email/components';
import * as React from 'react';

interface ContactFormEmailProps {
  name: string;
  email: string;
  message: string;
  mobile?: string;
  createdAt?: Date;
}

export default function ContactFormEmail({
  name = '<PERSON>',
  email = '<EMAIL>',
  message = 'Hello, I would like to get in touch with you.',
  mobile = 'Not provided',
  createdAt = new Date(),
}: ContactFormEmailProps) {
  return (
    <Html>
      <Head />
      <Preview>New Contact Form Submission from {name}</Preview>
      <Body style={main}>
        <Container style={container}>
          {/* Main Content */}
          <Section style={section}>
            <Row style={detailRow}>
              <Column style={labelColumn}>
                <Text style={label}>Submitted At:</Text>
              </Column>
              <Column>
                <Text style={value}>{createdAt.toLocaleString()}</Text>
              </Column>
            </Row>

            <Row style={detailRow}>
              <Column style={labelColumn}>
                <Text style={label}>Name:</Text>
              </Column>
              <Column>
                <Text style={value}>{name}</Text>
              </Column>
            </Row>

            <Row style={detailRow}>
              <Column style={labelColumn}>
                <Text style={label}>Email:</Text>
              </Column>
              <Column>
                <Link href={`mailto:${email}`} style={emailLink}>
                  {email}
                </Link>
              </Column>
            </Row>

            {mobile && (
              <Row style={detailRow}>
                <Column style={labelColumn}>
                  <Text style={label}>Phone:</Text>
                </Column>
                <Column>
                  <Link href={`tel:${mobile}`} style={phoneLink}>
                    {mobile}
                  </Link>
                </Column>
              </Row>
            )}

            <Hr style={hr} />

            <Section style={messageSection}>
              <Text style={messageLabel}>Message:</Text>
              <Text style={messageText}>{message}</Text>
            </Section>
          </Section>

          {/* Action Buttons */}
          <Section style={actionSection}>
            <Link href={`mailto:${email}`} style={replyButton}>
              Reply to {name}
            </Link>
          </Section>

          {/* Footer */}
          <Section style={footer}>
            <Text style={footerText}>
              This is an automated email sent from your contact form. Please do
              not reply to this email directly.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  padding: '20px 0',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '24px',
  marginBottom: '24px',
  borderRadius: '12px',
  boxShadow:
    '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  maxWidth: '600px',
};

const section = {
  padding: '0',
};

const detailRow = {
  marginBottom: '16px',
};

const labelColumn = {
  width: '120px',
};

const label = {
  color: '#6b7280',
  fontSize: '14px',
  fontWeight: '600',
  margin: '0',
};

const value = {
  color: '#1f2937',
  fontSize: '14px',
  margin: '0',
};

const emailLink = {
  color: '#3b82f6',
  textDecoration: 'none',
  fontSize: '14px',
};

const phoneLink = {
  color: '#3b82f6',
  textDecoration: 'none',
  fontSize: '14px',
};

const hr = {
  borderColor: '#e5e7eb',
  margin: '24px 0',
};

const messageSection = {
  backgroundColor: '#f9fafb',
  padding: '16px',
  borderRadius: '8px',
  marginTop: '24px',
};

const messageLabel = {
  color: '#6b7280',
  fontSize: '14px',
  fontWeight: '600',
  marginBottom: '8px',
};

const messageText = {
  color: '#1f2937',
  fontSize: '14px',
  lineHeight: '1.5',
  margin: '0',
  whiteSpace: 'pre-wrap' as const,
};

const actionSection = {
  textAlign: 'center' as const,
  marginTop: '32px',
};

const replyButton = {
  backgroundColor: '#3b82f6',
  borderRadius: '8px',
  color: '#ffffff',
  fontSize: '14px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
};

const footer = {
  marginTop: '32px',
  textAlign: 'center' as const,
  borderTop: '1px solid #e5e7eb',
  paddingTop: '24px',
};

const footerText = {
  color: '#9ca3af',
  fontSize: '12px',
  margin: '0',
};
