import {
  ConflictException,
  Injectable,
  UnauthorizedException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import {
  CreateUserDto,
  LoginDto,
  ProgressDto,
  ResetAdminPasswordDto,
  ConfirmResetAdminPasswordDto
} from './dto/admin.dto';
import { compare, hash } from 'bcrypt';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { MailerService } from 'src/mailer/mailer.service';
import { LoggerService } from 'src/utils/logger.service';
import { render } from '@react-email/components';
import AdminPasswordResetEmail from 'src/template/admin-password-reset';

const EXPIRE_TIME = 5 * 60 * 60 * 1000;

@Injectable()
export class AdminService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private mailerService: MailerService,
    private logger: LoggerService,
  ) {}

  async create(dto: CreateUserDto) {
    const user = await this.prisma.admin.findUnique({
      where: {
        email: dto.email,
      },
    });

    if (user) throw new ConflictException('email duplicated');

    const newUser = await this.prisma.admin.create({
      data: {
        ...dto,
        password: await hash(dto.password, 10),
      },
    });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = newUser;
    return result;
  }

  async findByEmail(email: string) {
    return await this.prisma.admin.findUnique({
      where: {
        email: email,
      },
    });
  }
  async findById(id: string) {
    return await this.prisma.user.findUnique({
      where: {
        id: id,
      },
    });
  }

  async login(dto: LoginDto) {
    const user = await this.validateUser(dto);
    const payload = {
      id: user.id,
      email: user.email,
      tokenType: 'admin',
      sub: {
        name: user.name,
      },
    };
    return {
      user,
      backendTokens: {
        accessToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE,
          secret: process.env.jwtAdminSecretKey,
        }),
        refreshToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE,
          secret: process.env.jwtRefreshTokenKey,
        }),
        expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
      },
    };
  }

  async validateUser(dto: LoginDto) {
    const user = await this.findByEmail(dto.email);

    if (user && (await compare(dto.password, user.password))) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password, ...result } = user;
      return result;
    }
    throw new UnauthorizedException('Wrong email or password');
  }

  async refreshToken(user: any) {
    const payload = {
      id: user.id,
      email: user.email,
      tokenType: 'admin',
      sub: {
        name: user.name,
      },
    };

    return {
      accessToken: await this.jwtService.signAsync(payload, {
        expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE,
        secret: process.env.jwtAdminSecretKey,
      }),
      refreshToken: await this.jwtService.signAsync(payload, {
        expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE,
        secret: process.env.jwtRefreshTokenKey,
      }),
      expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
    };
  }

  async progress_update_mentor_service(dto: ProgressDto) {
    const data = await this.prisma.user_mentor_service.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });

    return data;
  }
  async guest_progress_update_mentor_service(dto: ProgressDto) {
    const data = await this.prisma.guest_mentor_service.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });

    return data;
  }
  async progress_update_package(dto: ProgressDto) {
    const data = await this.prisma.user_package.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });

    return data;
  }
  async guest_progress_update_package(dto: ProgressDto) {
    const data = await this.prisma.guest_package.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });

    return data;
  }
  async progress_update_immigration(dto: ProgressDto) {
    const data = await this.prisma.user_immigration_service.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });
    return data;
  }
  async guest_progress_update_immigration(dto: ProgressDto) {
    const data = await this.prisma.guest_immigration_service.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });
    return data;
  }
  async progress_update_training(dto: ProgressDto) {
    const data = await this.prisma.user_training.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });
    return data;
  }
  async guest_progress_update_training(dto: ProgressDto) {
    const data = await this.prisma.guest_training.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });
    return data;
  }

  /**
   * Reset admin password (without login)
   */
  async resetPassword(dto: ResetAdminPasswordDto): Promise<void> {
    try {
      const admin = await this.findByEmail(dto.email);
      if (!admin) {
        // Don't reveal if email exists or not for security
        this.logger.warn(
          `Password reset attempted for non-existent admin: ${dto.email}`,
        );
        return;
      }

      // Generate reset token
      const resetToken = await this.jwtService.signAsync(
        { email: admin.email, type: 'password_reset' },
        {
          secret: process.env.RESET_PASSWORD_SECRET || 'reset-secret',
          expiresIn: '1h', // Token expires in 1 hour
        },
      );

      // Send password reset email
      await this.sendPasswordResetEmail(admin, resetToken);

      this.logger.info(`Password reset email sent to admin: ${admin.email}`);
    } catch (error) {
      this.logger.error(
        `Failed to process password reset for admin: ${dto.email}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Confirm password reset with token
   * Validates email existence and resets password for valid tokens
   */
  async confirmResetPassword(dto: ConfirmResetAdminPasswordDto): Promise<void> {
    try {
      // Verify reset token
      const payload = await this.jwtService.verifyAsync(dto.token, {
        secret: process.env.RESET_PASSWORD_SECRET || 'reset-secret',
      });

      // Validate token type
      if (payload.type !== 'password_reset') {
        throw new UnauthorizedException('Invalid reset token type');
      }

      // Validate email exists in token payload
      if (!payload.email) {
        throw new UnauthorizedException('Invalid token: missing email');
      }

      // Check if admin exists in database
      const admin = await this.findByEmail(payload.email);
      if (!admin) {
        // Log the attempt for security monitoring
        this.logger.warn(
          `Password reset attempted for non-existent admin email: ${payload.email}`,
        );
        throw new NotFoundException(
          'Email address not found in our records. Please verify the email address and try again.',
        );
      }

      // Hash new password
      const hashedNewPassword = await hash(dto.newPassword, 10);

      // Update password in database
      await this.prisma.admin.update({
        where: { id: admin.id },
        data: { password: hashedNewPassword },
      });

      this.logger.info(`Password successfully reset for admin: ${admin.email}`);
    } catch (error) {
      // Log detailed error for debugging
      this.logger.error(
        `Password reset confirmation failed: ${error.message}`,
        error.stack,
      );

      // Re-throw specific exceptions to maintain proper HTTP status codes
      if (error instanceof UnauthorizedException || error instanceof NotFoundException) {
        throw error;
      }

      // Handle JWT-specific errors
      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('Reset token has expired. Please request a new password reset.');
      }

      if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('Invalid reset token. Please request a new password reset.');
      }

      // Generic fallback for other errors
      throw new BadRequestException('Failed to reset password. Please try again or contact support.');
    }
  }

  /**
   * Send password reset email to admin
   */
  private async sendPasswordResetEmail(
    admin: any,
    resetToken: string,
  ): Promise<void> {
    try {
      const resetUrl = `${process.env.ADMIN_WEBSITE}/admin/reset-password?token=${resetToken}`;

      await this.mailerService.sendEmail({
        to: admin.email,
        subject: 'CareerIreland Admin - Password Reset',
        html: await render(
          AdminPasswordResetEmail({
            adminName: admin.name,
            resetUrl,
          }),
        ),
        from: process.env.EMAIL || '<EMAIL>',
        cc: [],
      });

      this.logger.info(`Password reset email sent to admin: ${admin.email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send password reset email to admin: ${admin.email}`,
        error.stack,
      );
      throw error;
    }
  }
}
