import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { MailerService } from 'src/mailer/mailer.service';
import { LoggerService } from 'src/utils/logger.service';

@Module({
  controllers: [AdminController],
  providers: [AdminService, PrismaService, JwtService, MailerService, LoggerService],
})
export class AdminModule {}
