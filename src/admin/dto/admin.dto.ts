import { IsBoolean } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Status } from '@prisma/client';
import { IsEmail, IsEnum, IsOptional, IsString, MinLength } from 'class-validator';

export class CreateUserDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  emailVerified?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsString()
  image?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  password?: string;
}

export class LoginDto {
  @ApiProperty()
  @IsEmail()
  email: string;
  @ApiProperty()
  @IsString()
  password: string;
}
export class ProgressDto {
  @ApiProperty()
  @IsString()
  id: string;
  @ApiProperty({ enum: Status, enumName: 'Status' })
  @IsEnum(Status)
  status: Status;
}

/**
 * DTO for admin password reset (without login)
 */
export class ResetAdminPasswordDto {
  @ApiProperty({
    description: 'Admin email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;
}

/**
 * DTO for admin password reset confirmation
 */
export class ConfirmResetAdminPasswordDto {
  @ApiProperty({
    description: 'Password reset token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  token: string;

  @ApiProperty({
    description: 'New password (minimum 8 characters)',
    example: 'NewSecurePassword123!',
  })
  @IsString()
  @MinLength(8)
  newPassword: string;
}
