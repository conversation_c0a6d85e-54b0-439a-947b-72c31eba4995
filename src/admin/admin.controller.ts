import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Request,
  UseGuards,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { JwtGuard } from 'src/guards/jwt.guard';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { RefreshJwtGuard } from 'src/guards/refresh.guard';
import {
  CreateUserDto,
  LoginDto,
  ProgressDto,
  ResetAdminPasswordDto,
  ConfirmResetAdminPasswordDto
} from './dto/admin.dto';
import { AdminService } from './admin.service';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';
import { LoggerService } from 'src/utils/logger.service';
import { ApiBody, ApiResponse } from '@nestjs/swagger';

@ApiTags('admin')
@Controller('admin')
export class AdminController {
  constructor(
    private admin: AdminService,
    private logger: LoggerService,
  ) {}

  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Get(':id')
  async getUserProfile(@Param('id') id: string) {
    return await this.admin.findById(id);
  }

  @Post('register')
  async registerUser(@Body() dto: CreateUserDto) {
    return await this.admin.create(dto);
  }

  @Post('login')
  async login(@Body() dto: LoginDto) {
    return await this.admin.login(dto);
  }

  @UseGuards(RefreshJwtGuard)
  @Post('refresh')
  async refreshToken(@Request() req) {
    return await this.admin.refreshToken(req.user);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/service')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async progress_update_mentor_service(@Body() dto: ProgressDto) {
    return await this.admin.progress_update_mentor_service(dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/guest-service')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async guest_progress_update_mentor_service(@Body() dto: ProgressDto) {
    return await this.admin.guest_progress_update_mentor_service(dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/package')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async progress_update_package(@Body() dto: ProgressDto) {
    return await this.admin.progress_update_package(dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/guest-package')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async guest_progress_update_package(@Body() dto: ProgressDto) {
    return await this.admin.guest_progress_update_package(dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/immigration')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async progress_update_immigration(@Body() dto: ProgressDto) {
    return await this.admin.progress_update_immigration(dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/guest-immigration')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async guest_progress_update_immigration(@Body() dto: ProgressDto) {
    return await this.admin.guest_progress_update_immigration(dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/training')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async progress_update_training(@Body() dto: ProgressDto) {
    return await this.admin.progress_update_training(dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/guest-training')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async guest_progress_update_training(@Body() dto: ProgressDto) {
    return await this.admin.guest_progress_update_training(dto);
  }

  @Post('reset-password')
  @ApiOperation({
    summary: 'Request admin password reset',
    description: 'Send a password reset email to the admin user',
  })
  @ApiBody({ type: ResetAdminPasswordDto })
  @ApiResponse({ status: 200, description: 'Password reset email sent' })
  async resetPassword(
    @Body() dto: ResetAdminPasswordDto,
  ): Promise<{ success: boolean; message: string }> {
    try {
      await this.admin.resetPassword(dto);

      return {
        success: true,
        message:
          'If an admin account with this email exists, a password reset link has been sent.',
      };
    } catch (error) {
      this.logger.error(`Password reset failed: ${error.message}`, error.stack);
      // Don't reveal specific error details for security
      return {
        success: true,
        message:
          'If an admin account with this email exists, a password reset link has been sent.',
      };
    }
  }

  /**
   * Confirm admin password reset with token
   * Validates email existence and resets password for valid tokens
   */
  @Post('reset-password/confirm')
  @ApiOperation({
    summary: 'Confirm admin password reset',
    description: 'Reset admin password using the reset token. Validates email existence before processing.',
  })
  @ApiBody({ type: ConfirmResetAdminPasswordDto })
  @ApiResponse({ status: 200, description: 'Password reset successfully' })
  @ApiResponse({ status: 401, description: 'Invalid or expired token' })
  @ApiResponse({ status: 404, description: 'Email address not found in our records' })
  async confirmResetPassword(
    @Body() dto: ConfirmResetAdminPasswordDto,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Call service to validate email existence and reset password
      await this.admin.confirmResetPassword(dto);

      return {
        success: true,
        message: 'Password reset successfully. You can now log in with your new password.',
      };
    } catch (error) {
      // Log error for debugging
      this.logger.error(
        `Admin password reset confirmation failed: ${error.message}`,
        error.stack,
      );

      // Handle specific error types with appropriate HTTP status codes
      if (error instanceof HttpException) {
        throw error; // Re-throw HttpExceptions as-is
      }

      // Map service exceptions to appropriate HTTP responses
      const statusCode = error.status ||
        (error.message?.includes('not found') ? HttpStatus.NOT_FOUND : HttpStatus.UNAUTHORIZED);

      throw new HttpException(
        error.message || 'Failed to reset password. Please try again or contact support.',
        statusCode,
      );
    }
  }
}
