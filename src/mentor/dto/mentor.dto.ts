import { IsBoolean } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsInt, IsOptional, IsString, Min } from 'class-validator';

export class MentorDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  emailVerified?: boolean;

  @ApiProperty()
  @IsString()
  image: string;

  @ApiProperty({ description: 'Training order' })
  @IsOptional()
  @IsInt()
  @Min(0)
  order: number;

  @ApiProperty()
  @IsString()
  desc: string;

  @ApiProperty()
  @IsString()
  designation: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  password?: string;
  @ApiProperty()
  @IsOptional()
  @IsString()
  linkedin?: string;
  @ApiProperty()
  @IsOptional()
  @IsString()
  profile?: string;
}

export class LoginDto {
  @ApiProperty()
  @IsEmail()
  email: string;
  @ApiProperty()
  @IsString()
  password: string;
}
