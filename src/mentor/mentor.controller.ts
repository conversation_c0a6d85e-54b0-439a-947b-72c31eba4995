import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';

import { LoginDto, MentorDto } from './dto/mentor.dto';
import { RefreshJwtGuard } from '../guards/refresh.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { MentorService } from './mentor.service';
import { VerifyOtpDto } from 'src/otp/dto/otp.dto';
import { JwtMentor } from 'src/guards/jwt.mentor.guard';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';
@ApiTags('mentor')
@Controller('mentor')
export class MentorController {
  constructor(private mentorService: MentorService) {}
  @Post('register')
  async registerUser(@Body() dto: MentorDto) {
    return await this.mentorService.register(dto);
  }

  @Post('login')
  async login(@Body() dto: LoginDto) {
    return await this.mentorService.login(dto);
  }
  @Post('other-login')
  async otherLogin(@Body() dto: MentorDto) {
    return await this.mentorService.otherLogin(dto);
  }

  @UseGuards(RefreshJwtGuard)
  @Post('refresh')
  async refreshToken(@Request() req) {
    return await this.mentorService.refreshToken(req.user);
  }

  @Post('verify')
  async verifyEmail(@Body() dto: VerifyOtpDto) {
    return await this.mentorService.verifyEmail(dto);
  }

  @UseGuards(JwtMentor)
  @ApiBearerAuth()
  @Get('profile')
  async profile(@Request() req) {
    return await this.mentorService.profile(req.user);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Post('admin/register')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async adminRegister(@Body() dto: MentorDto) {
    return await this.mentorService.adminRegister(dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('admin/:mentorId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async adminUpdateMentor(
    @Param('mentorId') id: string,
    @Body() dto: MentorDto,
  ) {
    return await this.mentorService.adminUpdateMentor(id, dto);
  }

  @Get('')
  async getMentor(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.mentorService.getMentor(page, limit);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Get('/admin/:id')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'ID of the mentor',
  })
  async getMentorByIdForAdmin(@Param('id') id: string) {
    return await this.mentorService.getMentorByIdForAdmin(id);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Delete('/admin/:mentorId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async adminRemove(@Param('mentorId') id: string) {
    return await this.mentorService.adminRemove(id);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    required: true,
    description: 'ID of the mentor',
  })
  async getMentorById(@Param('id') id: string) {
    return await this.mentorService.getMentorById(id);
  }
  @Get('/by/:name')
  @ApiParam({
    name: 'name',
    required: true,
    description: 'Name of the mentor',
  })
  async getMentorByName(@Param('name') name: string) {
    return await this.mentorService.getMentorByName(name);
  }
}
