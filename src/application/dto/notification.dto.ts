/**
 * Notification DTOs
 * Task 8: API Implementation and Documentation
 *
 * Data Transfer Objects for notification management
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEmail,
  IsEnum,
  IsObject,
  IsDateString,
  IsBoolean,
  IsInt,
  Min,
  Max,
  IsNotEmpty,
} from 'class-validator';

export enum NotificationType {
  EMAIL = 'Email',
  SMS = 'SMS',
  IN_APP = 'In_App',
}

export enum NotificationStatus {
  PENDING = 'Pending',
  SENT = 'Sent',
  FAILED = 'Failed',
  CANCELLED = 'Cancelled',
}

/**
 * DTO for sending notifications
 */
export class SendNotificationDto {
  @ApiProperty({
    description: 'Type of notification',
    enum: NotificationType,
    default: NotificationType.EMAIL,
  })
  @IsEnum(NotificationType)
  notification_type: NotificationType;

  @ApiPropertyOptional({
    description: 'Template ID for predefined templates',
  })
  @IsOptional()
  @IsString()
  template_id?: string;

  @ApiPropertyOptional({
    description: 'Recipient user ID (for registered users)',
  })
  @IsOptional()
  @IsString()
  recipient_user_id?: string;

  @ApiProperty({
    description: 'Recipient email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  recipient_email: string;

  @ApiPropertyOptional({
    description: 'Recipient mobile number (for SMS)',
    example: '+353123456789',
  })
  @IsOptional()
  @IsString()
  recipient_mobile?: string;

  @ApiProperty({
    description: 'Notification subject',
    example: 'Application Status Update',
  })
  @IsString()
  subject: string;

  @ApiProperty({
    description:
      'Notification message body (HTML for email, plain text for SMS)',
    example: '<p>Your application has been updated.</p>',
  })
  @IsString()
  message_body: string;

  @ApiPropertyOptional({
    description: 'Related application ID',
  })
  @IsOptional()
  @IsString()
  application_id?: string;

  @ApiPropertyOptional({
    description: 'Related document ID',
  })
  @IsOptional()
  @IsString()
  document_id?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { priority: 'high', category: 'status_update' },
  })
  @IsOptional()
  @IsObject()
  metadata?: any;
}

/**
 * DTO for scheduling notifications
 */
export class ScheduleNotificationDto extends SendNotificationDto {
  @ApiProperty({
    description: 'Scheduled delivery time (ISO 8601 format)',
    example: '2024-01-15T10:00:00Z',
  })
  @IsDateString()
  scheduled_at: string;
}

/**
 * DTO for notification template context
 */
export class NotificationTemplateContextDto {
  @ApiProperty({
    description: 'Template ID',
  })
  @IsString()
  template_id: string;

  @ApiProperty({
    description: 'Context variables for template replacement',
    example: {
      name: 'John Doe',
      application_number: 'APP-2024-001',
      status: 'Approved',
    },
  })
  @IsObject()
  context: any;
}

/**
 * DTO for notification response
 */
export class NotificationResponseDto {
  @ApiProperty({
    description: 'Response status',
    example: 'success',
  })
  status: string;

  @ApiProperty({
    description: 'Response message',
    example: 'Notification sent successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Notification data',
  })
  data: {
    id: string;
    notification_type: string;
    recipient_email: string;
    subject: string;
    status: string;
    sent_at?: Date;
    scheduled_at?: Date;
    created_at: Date;
  };
}

/**
 * DTO for notification filters
 */
export class NotificationFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by notification type',
    enum: NotificationType,
  })
  @IsOptional()
  @IsEnum(NotificationType)
  notification_type?: NotificationType;

  @ApiPropertyOptional({
    description: 'Filter by status',
    enum: NotificationStatus,
  })
  @IsOptional()
  @IsEnum(NotificationStatus)
  status?: NotificationStatus;

  @ApiPropertyOptional({
    description: 'Filter by application ID',
  })
  @IsOptional()
  @IsString()
  application_id?: string;

  @ApiPropertyOptional({
    description: 'Filter by recipient email',
  })
  @IsOptional()
  @IsEmail()
  recipient_email?: string;

  @ApiPropertyOptional({
    description: 'Filter from date (ISO 8601)',
  })
  @IsOptional()
  @IsDateString()
  date_from?: string;

  @ApiPropertyOptional({
    description: 'Filter to date (ISO 8601)',
  })
  @IsOptional()
  @IsDateString()
  date_to?: string;

  @ApiPropertyOptional({
    description: 'Page number',
    default: 1,
  })
  @IsOptional()
  page?: number;

  @ApiPropertyOptional({
    description: 'Items per page',
    default: 20,
  })
  @IsOptional()
  limit?: number;
}

/**
 * DTO for bulk notification sending
 */
export class BulkNotificationDto {
  @ApiProperty({
    description: 'List of recipient emails',
    type: [String],
    example: ['<EMAIL>', '<EMAIL>'],
  })
  @IsEmail({}, { each: true })
  recipient_emails: string[];

  @ApiProperty({
    description: 'Notification subject',
    example: 'Important Update',
  })
  @IsString()
  subject: string;

  @ApiProperty({
    description: 'Notification message body',
    example: '<p>This is an important update for all users.</p>',
  })
  @IsString()
  message_body: string;

  @ApiPropertyOptional({
    description: 'Template ID for bulk notifications',
  })
  @IsOptional()
  @IsString()
  template_id?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
  })
  @IsOptional()
  @IsObject()
  metadata?: any;
}

/**
 * DTO for notification settings response
 */
export class NotificationSettingsDto {
  @ApiProperty({
    description: 'Receive notifications when an agent is assigned to your case',
    example: true,
  })
  @IsBoolean()
  agent_assigned: boolean;

  @ApiProperty({
    description: 'Receive notifications when your case status is updated',
    example: true,
  })
  @IsBoolean()
  case_status_update: boolean;

  @ApiProperty({
    description:
      'Receive notifications when an agent has a query about your case',
    example: true,
  })
  @IsBoolean()
  agent_query: boolean;

  @ApiProperty({
    description: 'Receive notifications when a document is rejected',
    example: true,
  })
  @IsBoolean()
  document_rejection: boolean;

  @ApiProperty({
    description:
      'Number of days before sending missing document reminders (1-365 days)',
    example: 7,
    minimum: 1,
    maximum: 365,
  })
  @IsInt()
  @Min(1)
  @Max(365)
  missing_document_reminder_days: number;

  @ApiProperty({
    description: 'Receive notifications about system maintenance',
    example: true,
  })
  @IsBoolean()
  system_maintenance: boolean;

  @ApiProperty({
    description: 'Receive notifications when a final decision is issued',
    example: true,
  })
  @IsBoolean()
  final_decision_issued: boolean;
}

/**
 * DTO for updating notification settings
 */
export class UpdateNotificationSettingsDto {
  @ApiPropertyOptional({
    description: 'Receive notifications when an agent is assigned to your case',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  agent_assigned?: boolean;

  @ApiPropertyOptional({
    description: 'Receive notifications when your case status is updated',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  case_status_update?: boolean;

  @ApiPropertyOptional({
    description:
      'Receive notifications when an agent has a query about your case',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  agent_query?: boolean;

  @ApiPropertyOptional({
    description: 'Receive notifications when a document is rejected',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  document_rejection?: boolean;

  @ApiPropertyOptional({
    description:
      'Number of days before sending missing document reminders (1-365 days)',
    example: 7,
    minimum: 1,
    maximum: 365,
  })
  @IsOptional()
  @IsInt()
  @Min(1, { message: 'Missing document reminder days must be at least 1 day' })
  @Max(365, {
    message: 'Missing document reminder days cannot exceed 365 days',
  })
  missing_document_reminder_days?: number;

  @ApiPropertyOptional({
    description: 'Receive notifications about system maintenance',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  system_maintenance?: boolean;

  @ApiPropertyOptional({
    description: 'Receive notifications when a final decision is issued',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  final_decision_issued?: boolean;
}

/**
 * Document Status Change DTO for batch notifications
 */
export class DocumentStatusChangeDto {
  @ApiProperty({
    description: 'Document ID',
    example: 'doc_123',
  })
  @IsString()
  @IsNotEmpty()
  documentId: string;

  @ApiProperty({
    description: 'Document name',
    example: 'Passport Copy',
  })
  @IsString()
  @IsNotEmpty()
  documentName: string;

  @ApiProperty({
    description: 'Previous status',
    example: 'PENDING',
  })
  @IsString()
  @IsNotEmpty()
  previousStatus: string;

  @ApiProperty({
    description: 'Current status',
    example: 'APPROVED',
  })
  @IsString()
  @IsNotEmpty()
  currentStatus: string;

  @ApiProperty({
    description: 'Date when status was changed',
    example: '2025-08-09T10:30:00.000Z',
  })
  @IsDateString()
  changedAt: Date | string;

  @ApiProperty({
    description: 'User who changed the status',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  changedBy: string;

  @ApiProperty({
    description: 'Reason for status change',
    example: 'Document verified successfully',
    required: false,
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiProperty({
    description: 'Review comments',
    example: 'All information is correct',
    required: false,
  })
  @IsOptional()
  @IsString()
  reviewComments?: string;
}

/**
 * Send Batch Notification DTO (Legacy - for backward compatibility)
 */
export class SendBatchNotificationDto {
  @ApiProperty({
    description: 'Application ID to send batch notification for',
    example: 'app_123',
  })
  @IsString()
  @IsNotEmpty()
  applicationId: string;

  @ApiProperty({
    description: 'Force send even if no pending notifications',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  force?: boolean;
}

/**
 * Send Notification DTO (New - with notification_queue_id)
 */
export class SendNotificationByIdDto {
  @ApiProperty({
    description: 'Notification queue ID to send specific email',
    example: 'clm123abc456def789',
  })
  @IsString()
  @IsNotEmpty()
  notification_queue_id: string;
}

/**
 * Pending Notification Response DTO
 */
export class PendingNotificationResponseDto {
  @ApiProperty({
    description: 'Notification queue ID',
    example: 'clm123abc456def789',
  })
  notification_queue_id: string;

  @ApiProperty({
    description: 'Application number',
    example: 'IMM-2025-000001',
  })
  applicationNumber: string;

  @ApiProperty({
    description: 'Email preview',
    type: 'object',
    properties: {
      subject: { type: 'string', example: 'Document Status Updates - Application IMM-2025-000001' },
      htmlContent: { type: 'string', example: '<h2>Document Status Updates</h2>...' },
    },
  })
  emailPreview: {
    subject: string;
    htmlContent: string;
  };
}

/**
 * Send Batch Notification Response DTO
 */
export class SendBatchNotificationResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Batch notification sent successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Application ID',
    example: 'app_123',
  })
  applicationId: string;

  @ApiProperty({
    description: 'Number of notifications sent',
    example: 3,
  })
  notificationsSent: number;

  @ApiProperty({
    description: 'Recipient email',
    example: '<EMAIL>',
  })
  recipientEmail: string;
}

/**
 * Batch Notification Error DTO
 */
export class BatchNotificationErrorDto {
  @ApiProperty({
    description: 'Error status',
    example: false,
  })
  success: boolean;

  @ApiProperty({
    description: 'Error message',
    example: 'No pending notifications found for application',
  })
  message: string;

  @ApiProperty({
    description: 'Error code',
    example: 'NO_PENDING_NOTIFICATIONS',
  })
  errorCode: string;

  @ApiProperty({
    description: 'Application ID',
    example: 'app_123',
  })
  applicationId: string;
}
