/**
 * Batch Notification System Interfaces
 * 
 * Defines TypeScript interfaces for the new batch notification system
 * that groups document status changes by application_id.
 * 
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-08-09
 */

/**
 * Represents a single document status change in a batch
 */
export interface DocumentStatusChange {
  documentId: string;
  documentName: string;
  previousStatus: string;
  currentStatus: string;
  reason?: string;
  reviewComments?: string;
  changedAt: Date | string;
  changedBy: string;
}

/**
 * Batch notification queue entry data structure
 */
export interface BatchNotificationData {
  id: string;
  applicationId: string;
  recipientEmail: string;
  recipientName: string;
  documentChanges: DocumentStatusChange[];
  isSent: boolean;
  sentAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Request DTO for sending batch notification
 */
export interface SendBatchNotificationRequest {
  applicationId: string;
  force?: boolean;
}

/**
 * Response DTO for sending batch notification
 */
export interface SendBatchNotificationResponse {
  success: boolean;
  message: string;
  applicationId: string;
  notificationsSent: number;
  recipientEmail: string;
}

/**
 * Response DTO for getting pending notifications
 */
export interface PendingNotificationResponse {
  notification_queue_id: string;
  applicationNumber: string;
  emailPreview: {
    subject: string;
    htmlContent: string;
  };
}

/**
 * Error response for batch notification operations
 */
export interface BatchNotificationError {
  success: false;
  message: string;
  errorCode: string;
  applicationId: string;
}
