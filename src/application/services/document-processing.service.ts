import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../utils/prisma.service';
import * as pdfParse from 'pdf-parse';
import * as mammoth from 'mammoth';

/**
 * Document Processing Service
 *
 * Handles document processing operations including:
 * - OCR and text extraction from various file formats
 * - Document content analysis
 * - Metadata extraction
 * - Text indexing for search functionality
 */
@Injectable()
export class DocumentProcessingService {
  private readonly logger = new Logger(DocumentProcessingService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Extract text content from a document file
   *
   * @param file - The uploaded file
   * @returns Promise<string> - Extracted text content
   */
  async extractTextFromDocument(file: Express.Multer.File): Promise<string> {
    try {
      this.logger.log(`Extracting text from document: ${file.originalname}`);

      let extractedText = '';

      switch (file.mimetype) {
        case 'application/pdf':
          extractedText = await this.extractTextFromPDF(file.buffer);
          break;

        case 'application/msword':
        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          extractedText = await this.extractTextFromWord(file.buffer);
          break;

        case 'text/plain':
          extractedText = file.buffer.toString('utf-8');
          break;

        case 'image/jpeg':
        case 'image/png':
        case 'image/gif':
          extractedText = await this.extractTextFromImage(file.buffer);
          break;

        default:
          throw new BadRequestException(
            `Unsupported file type: ${file.mimetype}`,
          );
      }

      this.logger.log(
        `Text extraction completed. Length: ${extractedText.length} characters`,
      );
      return extractedText;
    } catch (error) {
      this.logger.error(
        `Failed to extract text from document: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process document and update search index
   *
   * @param documentId - Document ID
   * @param file - The uploaded file
   * @returns Promise<void>
   */
  async processDocumentForSearch(
    documentId: string,
    file: Express.Multer.File,
  ): Promise<void> {
    try {
      this.logger.log(`Processing document for search indexing: ${documentId}`);

      // Extract text content
      const textContent = await this.extractTextFromDocument(file);

      // Update document with extracted text for search
      await this.prisma.document_vault.update({
        where: { id: documentId },
        data: {
          // Note: metadata field removed from schema for simplification
          // metadata: {
          //   extracted_text: textContent,
          //   text_length: textContent.length,
          //   processed_at: new Date().toISOString(),
          // },
        },
      });

      this.logger.log(`Document processed for search indexing: ${documentId}`);
    } catch (error) {
      this.logger.error(
        `Failed to process document for search: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Analyze document content and extract metadata
   *
   * @param file - The uploaded file
   * @returns Promise<any> - Document analysis results
   */
  async analyzeDocumentContent(file: Express.Multer.File): Promise<any> {
    try {
      this.logger.log(`Analyzing document content: ${file.originalname}`);

      const textContent = await this.extractTextFromDocument(file);

      const analysis = {
        word_count: this.countWords(textContent),
        character_count: textContent.length,
        language: this.detectLanguage(textContent),
        contains_personal_info: this.detectPersonalInfo(textContent),
        document_structure: this.analyzeStructure(textContent),
        keywords: this.extractKeywords(textContent),
        readability_score: this.calculateReadabilityScore(textContent),
      };

      this.logger.log(`Document analysis completed for: ${file.originalname}`);
      return analysis;
    } catch (error) {
      this.logger.error(
        `Failed to analyze document content: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Extract text from PDF files
   *
   * @param buffer - PDF file buffer
   * @returns Promise<string> - Extracted text
   */
  private async extractTextFromPDF(buffer: Buffer): Promise<string> {
    try {
      const data = await pdfParse(buffer);
      return data.text;
    } catch (error) {
      this.logger.error(`Failed to extract text from PDF: ${error.message}`);
      throw new BadRequestException('Failed to process PDF file');
    }
  }

  /**
   * Extract text from Word documents
   *
   * @param buffer - Word file buffer
   * @returns Promise<string> - Extracted text
   */
  private async extractTextFromWord(buffer: Buffer): Promise<string> {
    try {
      const result = await mammoth.extractRawText({ buffer });
      return result.value;
    } catch (error) {
      this.logger.error(
        `Failed to extract text from Word document: ${error.message}`,
      );
      throw new BadRequestException('Failed to process Word document');
    }
  }

  /**
   * Extract text from images using OCR
   * Note: This is a placeholder implementation. In production, you would use
   * a service like Tesseract.js, Google Vision API, or AWS Textract
   *
   * @param buffer - Image file buffer
   * @returns Promise<string> - Extracted text
   */
  private async extractTextFromImage(buffer: Buffer): Promise<string> {
    try {
      // Placeholder implementation
      // In production, integrate with OCR service like:
      // - Tesseract.js for client-side OCR
      // - Google Vision API
      // - AWS Textract
      // - Azure Computer Vision

      this.logger.warn(
        'OCR functionality not implemented. Returning empty string.',
      );
      return '';
    } catch (error) {
      this.logger.error(`Failed to extract text from image: ${error.message}`);
      throw new BadRequestException('Failed to process image file');
    }
  }

  /**
   * Count words in text
   *
   * @param text - Input text
   * @returns number - Word count
   */
  private countWords(text: string): number {
    return text
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0).length;
  }

  /**
   * Detect language of text (basic implementation)
   *
   * @param text - Input text
   * @returns string - Detected language
   */
  private detectLanguage(text: string): string {
    // Basic language detection based on common words
    const englishWords = [
      'the',
      'and',
      'or',
      'but',
      'in',
      'on',
      'at',
      'to',
      'for',
      'of',
      'with',
      'by',
    ];
    const words = text.toLowerCase().split(/\s+/);

    let englishCount = 0;
    words.forEach((word) => {
      if (englishWords.includes(word)) {
        englishCount++;
      }
    });

    const englishRatio = englishCount / words.length;
    return englishRatio > 0.1 ? 'en' : 'unknown';
  }

  /**
   * Detect potential personal information in text
   *
   * @param text - Input text
   * @returns boolean - Whether personal info is detected
   */
  private detectPersonalInfo(text: string): boolean {
    // Basic patterns for personal information
    const patterns = [
      /\b\d{3}-\d{2}-\d{4}\b/, // SSN pattern
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Email pattern
      /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/, // Phone number pattern
      /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/, // Credit card pattern
    ];

    return patterns.some((pattern) => pattern.test(text));
  }

  /**
   * Analyze document structure
   *
   * @param text - Input text
   * @returns any - Structure analysis
   */
  private analyzeStructure(text: string): any {
    const lines = text.split('\n');
    const paragraphs = text.split('\n\n').filter((p) => p.trim().length > 0);

    return {
      line_count: lines.length,
      paragraph_count: paragraphs.length,
      average_paragraph_length:
        paragraphs.reduce((sum, p) => sum + p.length, 0) / paragraphs.length,
      has_headers: /^[A-Z\s]+$/m.test(text),
      has_bullet_points: /^\s*[-*•]\s/m.test(text),
      has_numbers: /^\s*\d+\.\s/m.test(text),
    };
  }

  /**
   * Extract keywords from text
   *
   * @param text - Input text
   * @returns string[] - Extracted keywords
   */
  private extractKeywords(text: string): string[] {
    // Simple keyword extraction based on word frequency
    const words = text
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter((word) => word.length > 3);

    const wordCount: { [key: string]: number } = {};
    words.forEach((word) => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });

    // Return top 10 most frequent words
    return Object.entries(wordCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  /**
   * Calculate basic readability score
   *
   * @param text - Input text
   * @returns number - Readability score
   */
  private calculateReadabilityScore(text: string): number {
    const sentences = text.split(/[.!?]+/).filter((s) => s.trim().length > 0);
    const words = text.split(/\s+/).filter((w) => w.length > 0);
    const syllables = words.reduce(
      (count, word) => count + this.countSyllables(word),
      0,
    );

    if (sentences.length === 0 || words.length === 0) return 0;

    // Flesch Reading Ease Score
    const avgWordsPerSentence = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;

    const score =
      206.835 - 1.015 * avgWordsPerSentence - 84.6 * avgSyllablesPerWord;
    return Math.max(0, Math.min(100, score));
  }

  /**
   * Count syllables in a word (basic implementation)
   *
   * @param word - Input word
   * @returns number - Syllable count
   */
  private countSyllables(word: string): number {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;

    const vowels = 'aeiouy';
    let count = 0;
    let previousWasVowel = false;

    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i]);
      if (isVowel && !previousWasVowel) {
        count++;
      }
      previousWasVowel = isVowel;
    }

    // Adjust for silent 'e'
    if (word.endsWith('e')) {
      count--;
    }

    return Math.max(1, count);
  }
}
