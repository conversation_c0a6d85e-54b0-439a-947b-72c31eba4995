/**
 * Simplified Application Controller
 *
 * Clean, maintainable controller for application management.
 * Replaces complex abstractions with direct implementation.
 *
 * Features:
 * - User application retrieval with role-based access
 * - Admin application management
 * - Proper authentication guards
 * - Simple, clean endpoints
 */

import {
  Controller,
  Get,
  Put,
  Patch,
  Post,
  Delete,
  Param,
  Query,
  Body,
  UseGuards,
  Logger,
  HttpStatus,
  HttpException,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiConsumes,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nest-lab/fastify-multer';
import { JwtGuard } from '../../guards/jwt.guard';
import { JwtAdmin } from '../../guards/jwt.admin.guard';
import { JwtAdminOrAgent } from '../../guards/jwt.admin-or-agent.guard';
import { GetUser } from '../../decorator/user.decorator';
import { IJWTPayload } from '../../types/auth';
import { ApplicationService } from '../application.service';
import { PrismaService } from '../../utils/prisma.service';

import {
  UpdateApplicationDto,
  UpdateApplicationResponseDto,
  UpdateCurrentStepDto,
  UpdateCurrentStepResponseDto,
  ApplicationQueryDto,
  ApplicationListResponseDto,
  ApplicationDetailsResponseDto,
  UploadApplicationDocumentDto,
  UploadApplicationDocumentResponseDto,
  UpdateEstimatedCompletionDto,
  UpdateEstimatedCompletionResponseDto,
  UpdateApplicationStatusDto,
  UpdateApplicationStatusResponseDto,
} from '../dto/update-application.dto';
import {
  UpdateDocumentStatusDto,
  UpdateDocumentStatusResponseDto,
  RequestDocumentDto,
  RequestDocumentResponseDto,
} from '../dto/document-status.dto';
import {
  UpdateApplicationNoteDto,
  UpdateApplicationNoteResponseDto,
} from '../dto/application-note.dto';
import {
  CreateNewApplicationDto,
  CreateApplicationResponseDto,
  AssignWorkflowTemplateDto,
  AssignWorkflowTemplateResponseDto,
} from '../dto/application.dto';

/**
 * Application Controller
 * Provides clean, simple endpoints for application management
 */
@ApiTags('applications')
@Controller('applications')
export class ApplicationController {
  private readonly logger = new Logger(ApplicationController.name);

  constructor(
    private readonly applicationService: ApplicationService,
    private readonly prisma: PrismaService,
  ) {}

  /**
   * Create a new application
   */
  @Post()
  @UseGuards(JwtAdminOrAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create new application',
    description:
      'Create a new application with multiple payments and agents support',
  })
  @ApiBody({ type: CreateNewApplicationDto })
  @ApiResponse({
    status: 201,
    description: 'Application created successfully',
    type: CreateApplicationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied',
  })
  async createApplication(
    @GetUser() user: IJWTPayload,
    @Body() createApplicationDto: CreateNewApplicationDto,
  ): Promise<CreateApplicationResponseDto> {
    try {
      // Validate user access - only authenticated users can create applications
      if (!user || !user.id) {
        throw new HttpException(
          'Authentication required',
          HttpStatus.UNAUTHORIZED,
        );
      }

      // Create the application using the service
      const newApplication = await this.applicationService.createNewApplication(
        createApplicationDto,
        user.id,
        user.tokenType,
      );

      return {
        success: true,
        message: 'Application created successfully',
        data: newApplication,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to create application: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to create application',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get user's applications with filtering and pagination
   */
  @Get()
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get user applications',
    description:
      'Retrieve applications for the authenticated user with optional filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Applications retrieved successfully',
    type: ApplicationListResponseDto,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
  })
  @ApiQuery({
    name: 'service_type',
    required: false,
    type: String,
    description: 'Filter by service type',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by status',
  })
  async getUserApplications(
    @GetUser() user: IJWTPayload,
    @Query() query: ApplicationQueryDto,
  ): Promise<ApplicationListResponseDto> {
    try {
      // Add user filter to ensure users only see their own applications
      let userFilters: any = {};
      if (user.tokenType === 'user') {
        userFilters.user_id = user.id;
      }

      if (user.tokenType === 'agent') {
        userFilters.assigned_to = user.id;
      }

      // Add query filters
      if (query.service_type) userFilters.service_type = query.service_type;
      if (query.status) userFilters.status = query.status;
      if (query.priority_level)
        userFilters.priority_level = query.priority_level;
      if (query.created_from) userFilters.created_from = query.created_from;
      if (query.created_to) userFilters.created_to = query.created_to;

      const result =
        await this.applicationService.getApplicationsWithTransformation(
          userFilters,
          query.page || 1,
          query.limit || 10,
          query.search,
        );

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to get user applications: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to retrieve applications',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get specific application details
   */
  @Get(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get application details',
    description: 'Retrieve detailed information about a specific application',
  })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({
    status: 200,
    description: 'Application details retrieved successfully',
    type: ApplicationDetailsResponseDto,
  })
  async getApplicationDetails(
    @GetUser() user: IJWTPayload,
    @Param('id') applicationId: string,
  ): Promise<ApplicationDetailsResponseDto> {
    try {
      // First check if user has access to this application
      if (user.tokenType === 'user') {
        const hasAccess = await this.applicationService.validateUserAccess(
          applicationId,
          user.id,
        );
        if (!hasAccess) {
          throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
        }
      }

      const application =
        await this.applicationService.getApplicationById(applicationId);

      // Additional access check for agents
      if (
        user.tokenType === 'agent' &&
        !(application.assigned_to || []).includes(user.id)
      ) {
        throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
      }

      return application; // Already transformed by the service
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to get application details: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to retrieve application details',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update application form data
   */
  @Put(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update application form data',
    description: 'Update form field values for specific application stages',
  })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiBody({ type: UpdateApplicationDto })
  @ApiResponse({
    status: 200,
    description: 'Application updated successfully',
    type: UpdateApplicationResponseDto,
  })
  async updateApplication(
    @GetUser() user: IJWTPayload,
    @Param('id') applicationId: string,
    @Body() updateData: UpdateApplicationDto,
  ): Promise<UpdateApplicationResponseDto> {
    try {
      // Validate user access to this application
      if (user.tokenType === 'user') {
        const hasAccess = await this.applicationService.validateUserAccess(
          applicationId,
          user.id,
        );
        if (!hasAccess) {
          throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
        }
      }

      // Update the application
      const updatedApplication =
        await this.applicationService.updateApplicationFormData(
          applicationId,
          updateData.formData,
          updateData.currentStep,
        );

      return {
        success: true,
        message: 'Application updated successfully',
        data: updatedApplication,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to update application: ${applicationId}`,
        error,
      );
      throw new HttpException(
        'Failed to update application',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update application current step
   */
  @Put(':id/current-step')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update application current step',
    description:
      'Update only the current workflow step for a specific application',
  })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiBody({ type: UpdateCurrentStepDto })
  @ApiResponse({
    status: 200,
    description: 'Current step updated successfully',
    type: UpdateCurrentStepResponseDto,
  })
  @ApiResponse({
    status: 403,
    description:
      'Access denied - user does not have permission to update this application',
  })
  @ApiResponse({
    status: 404,
    description: 'Application not found',
  })
  async updateCurrentStep(
    @GetUser() user: IJWTPayload,
    @Param('id') applicationId: string,
    @Body() updateData: UpdateCurrentStepDto,
  ): Promise<UpdateCurrentStepResponseDto> {
    try {
      // Validate user access to this application
      if (user.tokenType === 'user') {
        const hasAccess = await this.applicationService.validateUserAccess(
          applicationId,
          user.id,
        );
        if (!hasAccess) {
          throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
        }
      }

      // Update the current step
      const result = await this.applicationService.updateCurrentStep(
        applicationId,
        updateData.currentStep,
      );

      return {
        success: true,
        message: 'Current step updated successfully',
        applicationId: result.applicationId,
        currentStep: result.currentStep,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to update current step for application: ${applicationId}`,
        error,
      );
      throw new HttpException(
        'Failed to update current step',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update application estimated completion date
   */
  @Put(':id/estimated-completion')
  @UseGuards(JwtAdminOrAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update estimated completion date (Admin/Agent only)',
    description:
      'Update the estimated completion date for a specific application',
  })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiBody({ type: UpdateEstimatedCompletionDto })
  @ApiResponse({
    status: 200,
    description: 'Estimated completion date updated successfully',
    type: UpdateEstimatedCompletionResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid date format or date in the past',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - admin/agent access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Application not found',
  })
  async updateEstimatedCompletion(
    @GetUser() user: IJWTPayload,
    @Param('id') applicationId: string,
    @Body() updateData: UpdateEstimatedCompletionDto,
  ): Promise<UpdateEstimatedCompletionResponseDto> {
    try {
      this.logger.log(
        `User ${user.id} updating estimated completion for application: ${applicationId}`,
      );

      // Update the estimated completion date
      const updatedApplication =
        await this.applicationService.updateEstimatedCompletion(
          applicationId,
          updateData.estimated_completion,
        );

      return {
        success: true,
        message: 'Estimated completion date updated successfully',
        data: updatedApplication,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to update estimated completion for application: ${applicationId}`,
        error,
      );
      throw new HttpException(
        'Failed to update estimated completion date',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update application note
   */
  @Put(':id/note')
  @UseGuards(JwtAdminOrAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update application note',
    description: 'Update the note field for a specific application',
  })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiBody({ type: UpdateApplicationNoteDto })
  @ApiResponse({
    status: 200,
    description: 'Application note updated successfully',
    type: UpdateApplicationNoteResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid note content',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - access denied',
  })
  @ApiResponse({
    status: 404,
    description: 'Application not found',
  })
  async updateApplicationNote(
    @GetUser() user: IJWTPayload,
    @Param('id') applicationId: string,
    @Body() updateData: UpdateApplicationNoteDto,
  ): Promise<UpdateApplicationNoteResponseDto> {
    try {
      this.logger.log(
        `User ${user.id} updating note for application: ${applicationId}`,
      );
      // Validate user access to this application
      if (user.tokenType === 'user') {
        const hasAccess = await this.applicationService.validateUserAccess(
          applicationId,
          user.id,
        );
        if (!hasAccess) {
          throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
        }
      }

      // Update the application note
      const updatedApplication =
        await this.applicationService.updateApplicationNote(
          applicationId,
          updateData.note,
        );

      return {
        success: true,
        message: 'Application note updated successfully',
        note: updatedApplication.note,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to update note for application: ${applicationId}`,
        error,
      );
      throw new HttpException(
        'Failed to update application note',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Upload document for application
   */
  @Put(':id/document')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @UseInterceptors(
    FileInterceptor('file', { limits: { fileSize: 25 * 1024 * 1024 } }),
  )
  @ApiOperation({
    summary: 'Upload document for application',
    description: 'Upload a document file for a specific application stage',
  })
  @ApiConsumes('multipart/form-data')
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Document file to upload (max 25MB)',
        },
        document_name: {
          type: 'string',
          description: 'Document name/title',
          example: 'Passport Copy',
        },

        document_category: {
          type: 'string',
          description: 'Document category (optional)',
          example: 'identity_documents',
        },
        stage_order: {
          type: 'number',
          description: 'Workflow stage order',
          example: 1,
        },
        required: {
          type: 'boolean',
          description: 'Whether document is required (optional)',
          example: true,
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
          description: 'Document tags (optional)',
          example: ['identity', 'required'],
        },
        expiry_date: {
          type: 'string',
          format: 'date',
          description: 'Document expiry date (optional)',
          example: '2030-12-31',
        },
      },
      required: ['file', 'document_name', 'stage_order'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Document uploaded successfully',
    type: UploadApplicationDocumentResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid file or metadata',
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied',
  })
  @ApiResponse({
    status: 404,
    description: 'Application not found',
  })
  async uploadApplicationDocument(
    @GetUser() user: IJWTPayload,
    @Param('id') applicationId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() metadata: UploadApplicationDocumentDto,
  ): Promise<UploadApplicationDocumentResponseDto> {
    try {
      // Validate user access to this application
      if (user.tokenType === 'user') {
        const hasAccess = await this.applicationService.validateUserAccess(
          applicationId,
          user.id,
        );
        if (!hasAccess) {
          throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
        }
      }

      // Upload the document
      const uploadResult =
        await this.applicationService.uploadApplicationDocument(
          applicationId,
          file,
          {
            document_id: metadata.document_id,
            document_name: metadata.document_name,
            document_category: metadata.document_category,
            stage_order: metadata.stage_order,
            required: metadata.required,
            expiry_date: metadata.expiry_date,
          },
          user.id,
        );

      return {
        success: true,
        message: 'Document uploaded successfully',
        data: uploadResult,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to upload document for application: ${applicationId}`,
        error,
      );
      throw new HttpException(
        'Failed to upload document',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get all applications (admin only)
   */
  @Get('admin/all')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all applications (Admin only)',
    description:
      'Retrieve all applications with filtering and pagination (admin access required)',
  })
  @ApiResponse({
    status: 200,
    description: 'Applications retrieved successfully',
    type: ApplicationListResponseDto,
  })
  async getAllApplications(
    @Query() query: ApplicationQueryDto,
  ): Promise<ApplicationListResponseDto> {
    try {
      const filters: any = {};

      // Add query filters
      if (query.service_type) filters.service_type = query.service_type;
      if (query.status) filters.status = query.status;
      if (query.priority_level) filters.priority_level = query.priority_level;
      if (query.created_from) filters.created_from = query.created_from;
      if (query.created_to) filters.created_to = query.created_to;

      const result =
        await this.applicationService.getApplicationsWithTransformation(
          filters,
          query.page || 1,
          query.limit || 10,
          query.search,
        );

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to get all applications: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to retrieve applications',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update document status (Admin/Agent only)
   */
  @Patch(':id/documents/:documentId/status')
  @UseGuards(JwtAdminOrAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update document status (Admin/Agent only)',
    description:
      'Update the status of an application document with email notification to the applicant. Requires reason for certain statuses.',
  })
  @ApiParam({
    name: 'id',
    description: 'Application ID',
    example: 'app_123456789',
  })
  @ApiParam({
    name: 'documentId',
    description: 'Application document ID',
    example: 'ad_123456789',
  })
  @ApiBody({
    type: UpdateDocumentStatusDto,
    description: 'Document status update data',
  })
  @ApiResponse({
    status: 200,
    description: 'Document status updated successfully',
    type: UpdateDocumentStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed or reason required',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - admin/agent access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Document not found',
  })
  async updateDocumentStatus(
    @GetUser() admin: IJWTPayload,
    @Param('id') applicationId: string,
    @Param('documentId') documentId: string,
    @Body() updateDto: UpdateDocumentStatusDto,
  ): Promise<UpdateDocumentStatusResponseDto> {
    try {
      this.logger.log(
        `Admin ${admin.id} updating document status: ${documentId} to ${updateDto.status} for application: ${applicationId}`,
      );

      const result = await this.applicationService.updateDocumentStatus(
        documentId,
        updateDto,
        admin.id,
      );

      return {
        success: true,
        message: 'Document status updated successfully',
        data: result,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to update document status: ${documentId}`,
        error,
      );
      throw new HttpException(
        'Failed to update document status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Request new document from applicant (Admin/Agent only)
   */
  @Post(':id/documents/request')
  @UseGuards(JwtAdminOrAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Request new document (Admin/Agent only)',
    description:
      'Request a new document from an applicant with email notification. Checks for duplicate document names.',
  })
  @ApiParam({
    name: 'id',
    description: 'Application ID',
    example: 'app_123456789',
  })
  @ApiBody({
    type: RequestDocumentDto,
    description: 'Document request data',
  })
  @ApiResponse({
    status: 201,
    description: 'Document request created successfully',
    type: RequestDocumentResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - admin/agent access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Application not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - document with same name already exists',
  })
  async requestDocument(
    @GetUser() admin: IJWTPayload,
    @Param('id') applicationId: string,
    @Body() requestDto: RequestDocumentDto,
  ): Promise<RequestDocumentResponseDto> {
    try {
      this.logger.log(
        `Admin ${admin.id} requesting document for application: ${applicationId}`,
      );

      const result = await this.applicationService.requestDocument(
        applicationId,
        requestDto,
        admin.id,
      );

      return {
        success: true,
        message: 'Document request created successfully',
        data: result,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to request document for application: ${applicationId}`,
        error,
      );
      throw new HttpException(
        'Failed to request document',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update application status with conditional email notifications
   */
  @Put(':id/status')
  @UseGuards(JwtAdminOrAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update application status (Admin/Agent only)',
    description:
      'Update the status of an application with conditional email notification based on user notification settings. Reads notification preferences from config/notification-settings.json.',
  })
  @ApiParam({
    name: 'id',
    description: 'Application ID',
    example: 'app_123456789',
  })
  @ApiBody({
    type: UpdateApplicationStatusDto,
    description: 'Application status update data',
  })
  @ApiResponse({
    status: 200,
    description: 'Application status updated successfully',
    type: UpdateApplicationStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - invalid status or application already in requested status',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - admin/agent access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Application not found',
  })
  async updateApplicationStatus(
    @GetUser() user: IJWTPayload,
    @Param('id') applicationId: string,
    @Body() updateDto: UpdateApplicationStatusDto,
  ): Promise<UpdateApplicationStatusResponseDto> {
    try {
      this.logger.log(
        `User ${user.id} (${user.tokenType}) updating application status: ${applicationId} to ${updateDto.status}`,
      );

      const result = await this.applicationService.updateApplicationStatus(
        applicationId,
        updateDto,
        user.id,
      );

      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to update application status: ${applicationId}`,
        error,
      );
      throw new HttpException(
        'Failed to update application status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update application priority (Admin only)
   */
  @Put(':id/priority')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update application priority (Admin only)',
    description:
      'Update the priority level of an application. Only admins can perform this action.',
  })
  @ApiParam({
    name: 'id',
    description: 'Application ID',
    example: 'app_123456789',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        priority_level: {
          type: 'string',
          enum: ['Low', 'Medium', 'High', 'Critical'],
          description: 'New priority level for the application',
          example: 'High',
        },
      },
      required: ['priority_level'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Priority updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Application priority updated successfully',
        },
        priority_level: { type: 'string', example: 'High' },
        applicationId: { type: 'string', example: 'app_123456789' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Application not found',
  })
  async updateApplicationPriority(
    @GetUser() admin: IJWTPayload,
    @Param('id') applicationId: string,
    @Body()
    updateData: { priority_level: 'Low' | 'Medium' | 'High' | 'Critical' },
  ): Promise<{
    success: boolean;
    message: string;
    priority_level: string;
    applicationId: string;
  }> {
    try {
      this.logger.log(
        `Admin ${admin.id} updating priority for application: ${applicationId} to ${updateData.priority_level}`,
      );

      // Validate the application exists
      const application =
        await this.applicationService.getApplicationById(applicationId);
      if (!application) {
        throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
      }

      // Update the priority
      await this.applicationService.updateApplicationPriority(
        applicationId,
        updateData.priority_level,
        admin.id,
      );

      return {
        success: true,
        message: 'Application priority updated successfully',
        priority_level: updateData.priority_level,
        applicationId,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to update priority for application: ${applicationId}`,
        error,
      );
      throw new HttpException(
        'Failed to update application priority',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Assign application to agent
   */
  @Put(':id/assign')
  @UseGuards(JwtAdminOrAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Assign application to agent',
    description:
      'Admin can assign to any agent, agent can only assign to themselves',
  })
  @ApiParam({
    name: 'id',
    description: 'Application ID',
    example: 'app_123456789',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        agentId: {
          type: 'string',
          description: 'Agent ID to assign the application to',
          example: 'agent_123456789',
        },
      },
      required: ['agentId'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Application assigned successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Application assigned successfully',
        },
        assignedAgent: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'agent_123456789' },
            name: { type: 'string', example: 'John Smith' },
            email: { type: 'string', example: '<EMAIL>' },
          },
        },
        applicationId: { type: 'string', example: 'app_123456789' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - admin or agent access required',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - agents can only assign to themselves',
  })
  @ApiResponse({
    status: 404,
    description: 'Application or agent not found',
  })
  async assignApplication(
    @GetUser() user: IJWTPayload,
    @Param('id') applicationId: string,
    @Body() assignData: { agentId: string },
  ): Promise<{
    success: boolean;
    message: string;
    assignedAgent: {
      id: string;
      name: string;
      email: string;
    };
    applicationId: string;
  }> {
    try {
      this.logger.log(
        `User ${user.id} (${user.tokenType}) assigning application: ${applicationId} to agent: ${assignData.agentId}`,
      );

      // Role-based authorization checks
      if (user.tokenType === 'agent') {
        // Agents can only assign applications to themselves
        if (user.id !== assignData.agentId) {
          throw new HttpException(
            'Agents can only assign applications to themselves',
            HttpStatus.FORBIDDEN,
          );
        }
      }

      // Validate the application exists
      const application =
        await this.applicationService.getApplicationById(applicationId);
      if (!application) {
        throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
      }

      // Get agent details for response
      const agent = await this.prisma.agent.findUnique({
        where: { id: assignData.agentId },
        select: {
          id: true,
          name: true,
          email: true,
          status: true,
        },
      });

      if (!agent) {
        throw new HttpException('Agent not found', HttpStatus.NOT_FOUND);
      }

      if (agent.status !== 'Active') {
        throw new HttpException(
          'Cannot assign to inactive agent',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Assign the application
      await this.applicationService.assignApplicationToAgent(
        applicationId,
        assignData.agentId,
        user.id,
      );

      return {
        success: true,
        message: 'Application assigned successfully',
        assignedAgent: {
          id: agent.id,
          name: agent.name,
          email: agent.email,
        },
        applicationId,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to assign application: ${applicationId} to agent: ${assignData.agentId}`,
        error,
      );
      throw new HttpException(
        'Failed to assign application',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Assign a new workflow template to an application
   */
  @Post('assign-workflow-template')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Assign workflow template to application (Admin only)',
    description:
      'Assign a new workflow template to an existing application with atomic cleanup of old data',
  })
  @ApiBody({ type: AssignWorkflowTemplateDto })
  @ApiResponse({
    status: 200,
    description: 'Workflow template assigned successfully',
    type: AssignWorkflowTemplateResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - validation failed or incompatible workflow template',
  })
  @ApiResponse({
    status: 404,
    description: 'Application or workflow template not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied - admin access required',
  })
  async assignWorkflowTemplate(
    @Body() assignDto: AssignWorkflowTemplateDto,
  ): Promise<AssignWorkflowTemplateResponseDto> {
    try {
      this.logger.log(
        `Admin assigning workflow template ${assignDto.new_workflow_template_id} to application ${assignDto.application_id}`,
      );

      const result =
        await this.applicationService.assignWorkflowTemplate(assignDto);

      this.logger.log(
        `Successfully assigned workflow template to application ${assignDto.application_id}`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to assign workflow template: ${error.message}`,
        error.stack,
      );

      // Re-throw known HTTP exceptions
      if (error instanceof HttpException) {
        throw error;
      }

      // Convert service exceptions to HTTP exceptions
      if (error.message?.includes('not found')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      if (
        error.message?.includes('not active') ||
        error.message?.includes('does not match') ||
        error.message?.includes('already uses')
      ) {
        throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
      }

      // Generic error fallback
      throw new HttpException(
        'Failed to assign workflow template',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Remove agent from application
   */
  @Delete(':applicationId/agents/:agentId')
  @UseGuards(JwtAdminOrAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Remove agent from application',
    description:
      'Remove specified agent from application agent_ids array. Admin can remove any agent, agent can only remove themselves.',
  })
  @ApiParam({
    name: 'applicationId',
    description: 'Application ID',
    example: 'app_123456789',
  })
  @ApiParam({
    name: 'agentId',
    description: 'Agent ID to remove',
    example: 'agent_123456789',
  })
  @ApiResponse({
    status: 200,
    description: 'Agent removed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Agent removed from application successfully',
        },
        applicationId: { type: 'string', example: 'app_123456789' },
        agentId: { type: 'string', example: 'agent_123456789' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - admin or agent access required',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - agents can only remove themselves',
  })
  @ApiResponse({
    status: 404,
    description: 'Application or agent not found',
  })
  async removeAgentFromApplication(
    @GetUser() user: IJWTPayload,
    @Param('applicationId') applicationId: string,
    @Param('agentId') agentId: string,
  ): Promise<{
    success: boolean;
    message: string;
    applicationId: string;
    agentId: string;
  }> {
    try {
      this.logger.log(
        `User ${user.id} (${user.tokenType}) removing agent: ${agentId} from application: ${applicationId}`,
      );

      // Role-based authorization checks
      if (user.tokenType === 'agent') {
        // Agents can only remove themselves from applications
        if (user.id !== agentId) {
          throw new HttpException(
            'Agents can only remove themselves from applications',
            HttpStatus.FORBIDDEN,
          );
        }
      }

      // Remove the agent from the application
      await this.applicationService.removeAgentFromApplication(
        applicationId,
        agentId,
        user.id,
      );

      return {
        success: true,
        message: 'Agent removed from application successfully',
        applicationId,
        agentId,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to remove agent: ${agentId} from application: ${applicationId}`,
        error,
      );
      throw new HttpException(
        'Failed to remove agent from application',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Delete application document
   */
  @Delete(':id/documents/:documentId')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Delete application document',
    description:
      'Delete a document from an application. Regular users can only delete non-approved documents. Admin/Agent users can delete any document.',
  })
  @ApiParam({
    name: 'id',
    description: 'Application ID',
    example: 'app_123456789',
  })
  @ApiParam({
    name: 'documentId',
    description: 'Application document ID',
    example: 'ad_123456789',
  })
  @ApiResponse({
    status: 200,
    description: 'Document deleted successfully',
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
          example: 'success',
        },
        message: {
          type: 'string',
          example: 'Document deleted successfully',
        },
        data: {
          type: 'object',
          properties: {
            documentId: {
              type: 'string',
              example: 'ad_123456789',
            },
            applicationId: {
              type: 'string',
              example: 'app_123456789',
            },
            deletedFiles: {
              type: 'array',
              items: {
                type: 'string',
              },
              example: ['documents/app_123456789/document.pdf'],
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions to delete document',
  })
  @ApiResponse({
    status: 404,
    description: 'Document not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async deleteApplicationDocument(
    @GetUser() user: IJWTPayload,
    @Param('id') applicationId: string,
    @Param('documentId') documentId: string,
  ) {
    try {
      this.logger.log(
        `User ${user.id} (${user.tokenType || 'user'}) deleting document ${documentId} from application: ${applicationId}`,
      );

      const result = await this.applicationService.deleteApplicationDocument(
        applicationId,
        documentId,
        user.id,
        user.tokenType || 'user',
      );

      this.logger.log(
        `Document ${documentId} deleted from application ${applicationId} successfully`,
      );

      return {
        status: 'success',
        message: 'Document deleted successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(
        `Failed to delete document from application: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
