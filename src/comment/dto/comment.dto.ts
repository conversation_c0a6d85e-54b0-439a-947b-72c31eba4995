import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CommentDto {
  @ApiProperty({
    example: 'This is a great blog post!',
    description: 'The content of the comment',
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    example: 'cln2h8xjx000008l49fh1b5z7',
    description: 'ID of the blog being commented on',
  })
  @IsNotEmpty()
  @IsString()
  blogId: string;

  @ApiProperty({
    example: 'cln2h8xjx000008l49fh1b5z7',
    description: 'ID of the parent comment if this is a reply',
    required: false,
  })
  @IsOptional()
  @IsString()
  parentId?: string;
}
