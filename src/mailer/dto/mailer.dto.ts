import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>A<PERSON>y, IsEmail, IsOptional, IsString } from 'class-validator';
export class ResendEmailDto {
  @ApiProperty()
  @IsString()
  subject: string;
  @ApiProperty()
  @IsString()
  @IsEmail()
  to: string;

  @ApiProperty()
  @IsString()
  from: string;
  @ApiProperty()
  @IsOptional()
  @IsString()
  @IsEmail()
  @IsArray()
  cc: string[];

  @ApiProperty()
  @IsString()
  html: string;

  // @ApiProperty({ required: false })
  // @IsString()
  // @IsOptional()
  // text: string;
}
