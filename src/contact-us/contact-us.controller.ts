import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { ContactUsService } from './contact-us.service';
import { ContactUsDto } from './dto/contact-us.dto';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';

@ApiTags('contact-us')
@Controller('contact-us')
export class ContactUsController {
  constructor(private contact: ContactUsService) {}

  @Post('')
  async create(@Body() dto: ContactUsDto) {
    return await this.contact.create(dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Get('')
  async getAll() {
    return await this.contact.getAll();
  }
}
