import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class BlogDto {
  @ApiProperty({
    description: 'Title of the blog',
    example: 'Understanding Type Script',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'A brief summary of the blog',
    example: 'This blog explains the basics of TypeScript in detail.',
  })
  @IsString()
  summary: string;

  @ApiProperty({
    description: 'The name of the blogger',
    example: '<PERSON><PERSON>',
  })
  @IsString()
  blogger: string;

  @ApiProperty({
    description: 'Image URL for the blog',
    example: 'https://example.com/image.jpg',
  })
  @IsString()
  img: string;

  @ApiProperty({
    description: 'Full description/content of the blog',
    example:
      'This blog explains the fundamentals of TypeScript, including its syntax, features, and use cases.',
  })
  @IsString()
  desc: string;
}
