import { Injectable } from '@nestjs/common';
import { Request } from 'express';
import { PrismaService } from 'src/utils/prisma.service';
import { ServiceDto, UpdateServiceDto } from './dto/services.dto';

@Injectable()
export class ServicesService {
  constructor(private prisma: PrismaService) {}

  async mentorServiceCreate(req: Request, dto: ServiceDto) {
    const service = await this.prisma.service.create({
      data: {
        ...dto,
      },
    });
    return service;
  }
  async adminServiceCreate(mentorId: string, dto: ServiceDto) {
    const service = await this.prisma.service.create({
      data: {
        ...dto,
        mentorId,
        status: 'Accepted',
      },
    });
    return service;
  }
  async adminServiceUpdate(id: string, dto: ServiceDto) {
    const service = await this.prisma.service.update({
      where: {
        id: id,
      },
      data: {
        ...dto,
      },
    });
    return service;
  }
  async adminServiceDelete(id: string) {
    const service = await this.prisma.service.delete({
      where: {
        id: id,
      },
    });
    return service;
  }

  async getServicesForAdmin(mentorId: string) {
    const service = await this.prisma.service.findMany({
      where: {
        mentorId: mentorId,
      },
    });

    return service;
  }

  async approve(req: Request, dto: UpdateServiceDto) {
    const service = await this.prisma.service.update({
      where: {
        id: dto.id,
      },
      data: {
        status: 'Accepted',
      },
    });
    return service;
  }
}
