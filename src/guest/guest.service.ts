import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';

@Injectable()
export class GuestService {
  constructor(private prisma: PrismaService) {}

  async service(page: number, limit: number) {
    const isPagination = page > 0 && limit > 0;
    const data = await this.prisma.guest_mentor_service.findMany({
      skip: isPagination ? (page - 1) * limit : undefined,
      take: isPagination ? limit : undefined,
      where: {
        serviceId: { not: null },
        mentor_services: {
          mentorId: { not: null },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      select: {
        id: true,
        amount: true,
        name: true,
        email: true,
        mobile_no: true,
        status: true,
        progress: true,
        createdAt: true,
        mentor_services: {
          select: {
            price: true,
            name: true,
            id: true,
            meeting_link: true,
            mentor: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
      },
    });

    return data;
  }
  async package(page: number, limit: number) {
    const isPagination = page > 0 && limit > 0;
    const data = await this.prisma.guest_package.findMany({
      skip: isPagination ? (page - 1) * limit : undefined,
      take: isPagination ? limit : undefined,
      where: {
        packageId: { not: null },
      },
      orderBy: {
        createdAt: 'desc',
      },
      select: {
        id: true,
        amount: true,
        name: true,
        email: true,
        mobile_no: true,
        status: true,
        progress: true,
        createdAt: true,
        package: {
          select: {
            amount: true,
            name: true,
            id: true,
          },
        },
      },
    });

    return data;
  }
  async immigration(page: number, limit: number) {
    const isPagination = page > 0 && limit > 0;
    const data = await this.prisma.guest_immigration_service.findMany({
      skip: isPagination ? (page - 1) * limit : undefined,
      take: isPagination ? limit : undefined,
      where: {
        immigration_serviceId: { not: null },
      },
      orderBy: {
        createdAt: 'desc',
      },
      select: {
        id: true,
        amount: true,
        name: true,
        email: true,
        mobile_no: true,
        status: true,
        progress: true,
        createdAt: true,
        immigration_service: {
          select: {
            amount: true,
            name: true,
            id: true,
          },
        },
      },
    });

    return data;
  }
  async training(page: number, limit: number) {
    const isPagination = page > 0 && limit > 0;
    const data = await this.prisma.guest_training.findMany({
      skip: isPagination ? (page - 1) * limit : undefined,
      take: isPagination ? limit : undefined,
      where: {
        trainingId: { not: null },
      },
      orderBy: {
        createdAt: 'desc',
      },
      select: {
        id: true,
        amount: true,
        name: true,
        email: true,
        mobile_no: true,
        status: true,
        progress: true,
        createdAt: true,
        training: {
          select: {
            amount: true,
            name: true,
            id: true,
          },
        },
      },
    });

    return data;
  }
}
