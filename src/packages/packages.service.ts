import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';
import { PackageDto } from './dto/packages.dto';

@Injectable()
export class PackagesService {
  constructor(private prisma: PrismaService) {}

  async create(dto: PackageDto) {
    const packages = this.prisma.packages.create({
      data: dto,
    });

    return packages;
  }
  async update(id: string, dto: PackageDto) {
    const packages = this.prisma.packages.update({
      where: {
        id,
      },
      data: dto,
    });

    return packages;
  }

  async remove(id: string) {
    const packages = this.prisma.packages.delete({
      where: {
        id,
      },
    });

    return packages;
  }

  async getAll() {
    const packages = this.prisma.packages.findMany({
      orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
    });

    return packages;
  }
}
