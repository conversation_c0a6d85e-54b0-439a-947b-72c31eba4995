import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';
import { IJWTPayload } from 'src/types/auth';
import { ReviewDto } from './dto/review.dto';

@Injectable()
export class ReviewService {
  constructor(private prisma: PrismaService) {}

  async create(user: IJWTPayload, dto: ReviewDto) {
    const review = await this.prisma.review.create({
      data: {
        ...dto,
        userId: user.id,
      },
    });

    return review;
  }
  async remove(id: string) {
    const review = await this.prisma.review.delete({
      where: {
        id,
      },
    });

    return review;
  }

  async getReviews(id: string, page: number, limit: number) {
    const isPagination = page > 0 && limit > 0;

    const reviews = await this.prisma.review.findMany({
      skip: isPagination ? (page - 1) * limit : undefined,
      take: isPagination ? limit : undefined,
      where: {
        mentorId: id,
        userId: { not: null },
      },
      select: {
        id: true,
        message: true,
        rating: true,
        createdAt: true,
        updatedAt: true,
        user: {
          select: {
            name: true,
            image: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return reviews;
  }
}
