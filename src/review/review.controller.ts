import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ReviewService } from './review.service';
import { JwtGuard } from 'src/guards/jwt.guard';
import { ReviewDto } from './dto/review.dto';
import { GetUser } from 'src/decorator/user.decorator';
import { IJWTPayload } from 'src/types/auth';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';

@ApiTags('review')
@Controller('review')
export class ReviewController {
  constructor(private review: ReviewService) {}

  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Post()
  @ApiOperation({
    summary: '(User only)',
    description:
      'This API is restricted to  users and requires a Bearer token for authentication.',
  })
  async create(@GetUser() user: IJWTPayload, @Body() dto: ReviewDto) {
    return await this.review.create(user, dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Delete(':reviewId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin  users and requires a Bearer token for authentication.',
  })
  async remove(@Param('reviewId') id: string) {
    return await this.review.remove(id);
  }

  @Get(':mentorId')
  async getBlogs(
    @Param('mentorId') id: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.review.getReviews(id, page, limit);
  }
}
