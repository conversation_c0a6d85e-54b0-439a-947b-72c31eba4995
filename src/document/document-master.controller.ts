/**
 * Document Master Controller
 *
 * This controller provides REST API endpoints for document master CRUD operations.
 * All endpoints are restricted to admin users only using JwtAdmin guard.
 *
 * Key Features:
 * - Admin-only access control
 * - Full CRUD operations
 * - Comprehensive API documentation
 * - Input validation and error handling
 * - Pagination and filtering support
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { DocumentMasterService } from './document-master.service';
import {
  CreateDocumentMasterDto,
  UpdateDocumentMasterDto,
  DocumentMasterFiltersDto,
  DocumentMasterResponseDto,
  PaginatedDocumentMasterResponseDto,
} from './dto/document-master.dto';
import { JwtAdmin } from '../guards/jwt.admin.guard';
import { GetUser } from '../decorator/user.decorator';
import { IJWTPayload } from '../types/auth';

@ApiTags('Document Master')
@Controller('document-master')
@UseGuards(JwtAdmin)
@ApiBearerAuth()
export class DocumentMasterController {
  private readonly logger = new Logger(DocumentMasterController.name);

  constructor(private readonly documentMasterService: DocumentMasterService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new document master',
    description:
      'Creates a new document master template. Admin access required.',
  })
  @ApiResponse({
    status: 201,
    description: 'Document master created successfully',
    type: DocumentMasterResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 409,
    description: 'Document master with this name already exists',
  })
  async create(
    @Body() createDocumentMasterDto: CreateDocumentMasterDto,
    @GetUser() user: IJWTPayload,
  ): Promise<DocumentMasterResponseDto> {
    try {
      this.logger.log(
        `Admin ${user.email} creating document master: ${createDocumentMasterDto.name}`,
      );

      const result = await this.documentMasterService.create(
        createDocumentMasterDto,
        user.id,
      );

      this.logger.log(`Document master created successfully: ${result.id}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to create document master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all document masters',
    description:
      'Retrieves a paginated list of document masters with optional filtering. Admin access required.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page (default: 10, max: 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'category',
    required: false,
    description: 'Filter by category',
    example: 'Identity Documents',
  })
  // Task 4: Removed document_type query parameter - following non-destructive patterns
  // @ApiQuery({
  //   name: 'document_type',
  //   required: false,
  //   description: 'Filter by document type',
  //   example: 'Government ID',
  // })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search in name and description',
    example: 'passport',
  })
  @ApiResponse({
    status: 200,
    description: 'Document masters retrieved successfully',
    type: PaginatedDocumentMasterResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  async findAll(
    @Query() filters: DocumentMasterFiltersDto,
  ): Promise<PaginatedDocumentMasterResponseDto> {
    try {
      this.logger.log('Fetching document masters with filters', filters);

      const result = await this.documentMasterService.findAll(filters);

      this.logger.log(`Retrieved ${result.data.length} document masters`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to fetch document masters: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to retrieve document masters',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get a specific document master',
    description:
      'Retrieves a document master by its ID. Admin access required.',
  })
  @ApiParam({
    name: 'id',
    description: 'Document master ID',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Document master retrieved successfully',
    type: DocumentMasterResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Document master not found',
  })
  async findOne(@Param('id') id: string): Promise<DocumentMasterResponseDto> {
    try {
      this.logger.log(`Fetching document master: ${id}`);

      const result = await this.documentMasterService.findOne(id);

      this.logger.log(`Document master retrieved: ${result.name}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to fetch document master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update a document master',
    description: 'Updates an existing document master. Admin access required.',
  })
  @ApiParam({
    name: 'id',
    description: 'Document master ID',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Document master updated successfully',
    type: DocumentMasterResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Document master not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Document master with this name already exists',
  })
  async update(
    @Param('id') id: string,
    @Body() updateDocumentMasterDto: UpdateDocumentMasterDto,
    @GetUser() user: IJWTPayload,
  ): Promise<DocumentMasterResponseDto> {
    try {
      this.logger.log(`Admin ${user.email} updating document master: ${id}`);

      const result = await this.documentMasterService.update(
        id,
        updateDocumentMasterDto,
        user.id,
      );

      this.logger.log(`Document master updated successfully: ${result.id}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to update document master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete a document master',
    description:
      'Deletes a document master if it is not in use. Admin access required.',
  })
  @ApiParam({
    name: 'id',
    description: 'Document master ID',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Document master deleted successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Document master not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Cannot delete document master as it is in use',
  })
  async remove(
    @Param('id') id: string,
    @GetUser() user: IJWTPayload,
  ): Promise<{ message: string }> {
    try {
      this.logger.log(`Admin ${user.email} deleting document master: ${id}`);

      await this.documentMasterService.remove(id);

      this.logger.log(`Document master deleted successfully: ${id}`);
      return { message: 'Document master deleted successfully' };
    } catch (error) {
      this.logger.error(
        `Failed to delete document master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
