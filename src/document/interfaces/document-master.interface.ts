/**
 * Document Master Interfaces
 *
 * This file contains TypeScript interfaces for document master operations.
 * These interfaces provide type safety and structure for document master
 * management across the application.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

/**
 * Document Master Entity Interface
 *
 * Represents the structure of a document master in the database.
 */
export interface IDocumentMaster {
  id: string;
  name: string;
  description?: string;
  category: string;
  // Task 4: Removed document_type field - following non-destructive patterns
  // document_type: string;
  instructions?: string;
  created_by?: string;
  updated_by?: string;
  created_at: Date;
  updated_at: Date;
}

/**
 * Document Master Creation Data Interface
 *
 * Represents the data required to create a new document master.
 */
export interface ICreateDocumentMaster {
  name: string;
  description?: string;
  category: string;
  // Task 4: Removed document_type field - following non-destructive patterns
  // document_type: string;
  instructions?: string;
  created_by?: string;
}

/**
 * Document Master Update Data Interface
 *
 * Represents the data that can be updated for a document master.
 */
export interface IUpdateDocumentMaster {
  name?: string;
  description?: string;
  category?: string;
  // Task 4: Removed document_type field - following non-destructive patterns
  // document_type?: string;
  instructions?: string;
  updated_by?: string;
}

/**
 * Document Master Filter Options Interface
 *
 * Represents the available filter options for querying document masters.
 */
export interface IDocumentMasterFilters {
  page?: number;
  limit?: number;
  category?: string;
  // Task 4: Removed document_type field - following non-destructive patterns
  // document_type?: string;
  search?: string;
}

/**
 * Paginated Document Master Result Interface
 *
 * Represents the structure of paginated document master query results.
 */
export interface IPaginatedDocumentMasters {
  data: IDocumentMaster[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Document Master Usage Information Interface
 *
 * Represents information about document master usage across applications.
 */
export interface IDocumentMasterUsage {
  inUse: boolean;
  usageCount: number;
  usageDetails?: string[];
}

/**
 * Document Master Service Interface
 *
 * Defines the contract for document master service operations.
 */
export interface IDocumentMasterService {
  /**
   * Create a new document master
   */
  create(data: ICreateDocumentMaster): Promise<IDocumentMaster>;

  /**
   * Get all document masters with optional filtering and pagination
   */
  findAll(filters: IDocumentMasterFilters): Promise<IPaginatedDocumentMasters>;

  /**
   * Get a specific document master by ID
   */
  findOne(id: string): Promise<IDocumentMaster | null>;

  /**
   * Update a document master
   */
  update(id: string, data: IUpdateDocumentMaster): Promise<IDocumentMaster>;

  /**
   * Delete a document master (with usage validation)
   */
  remove(id: string): Promise<void>;

  /**
   * Check if a document master is in use
   */
  checkUsage(id: string): Promise<IDocumentMasterUsage>;
}

/**
 * Document Master Repository Interface
 *
 * Defines the contract for document master data access operations.
 */
export interface IDocumentMasterRepository {
  /**
   * Create a new document master
   */
  create(data: ICreateDocumentMaster): Promise<IDocumentMaster>;

  /**
   * Find document masters with filters
   */
  findMany(filters: IDocumentMasterFilters): Promise<{
    data: IDocumentMaster[];
    total: number;
  }>;

  /**
   * Find a document master by ID
   */
  findById(id: string): Promise<IDocumentMaster | null>;

  /**
   * Update a document master
   */
  update(id: string, data: IUpdateDocumentMaster): Promise<IDocumentMaster>;

  /**
   * Delete a document master
   */
  delete(id: string): Promise<void>;

  /**
   * Check if document master exists
   */
  exists(id: string): Promise<boolean>;

  /**
   * Count usage of document master in applications
   */
  countUsage(id: string): Promise<number>;

  /**
   * Get usage details for document master
   */
  getUsageDetails(id: string): Promise<string[]>;

  /**
   * Get distinct categories
   */
  getDistinctCategories(): Promise<string[]>;

  // Task 4: Removed getDistinctDocumentTypes method - following non-destructive patterns
  // /**
  //  * Get distinct document types
  //  */
  // getDistinctDocumentTypes(): Promise<string[]>;
}

/**
 * Audit Log Entry Interface
 *
 * Represents an audit log entry for document master operations.
 */
export interface IDocumentMasterAuditLog {
  id: string;
  document_master_id: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE';
  user_id?: string;
  changes?: Record<string, any>;
  timestamp: Date;
}
