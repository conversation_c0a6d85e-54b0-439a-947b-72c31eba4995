/**
 * Document Master Data Transfer Objects (DTOs)
 *
 * This file contains all the DTOs used for document master CRUD operations.
 * These DTOs provide input validation, API documentation, and type safety
 * for document master management operations.
 *
 * Key Features:
 * - Input validation using class-validator decorators
 * - API documentation using Swagger decorators
 * - Type safety for document master operations
 * - Admin-only access control
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  Length,
  MaxLength,
} from 'class-validator';

/**
 * Create Document Master DTO
 *
 * DTO for creating a new document master template.
 * Contains all required fields for document master creation.
 */
export class CreateDocumentMasterDto {
  @ApiProperty({
    description: 'Document template name',
    example: 'Passport Copy',
    minLength: 1,
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 255)
  name: string;

  @ApiPropertyOptional({
    description: 'Document description',
    example: 'A clear copy of the passport bio-data page',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiProperty({
    description: 'Document category for grouping',
    example: 'Identity Documents',
    minLength: 1,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 100)
  category: string;

  // Task 4: Removed document_type field - following non-destructive patterns
  // @ApiProperty({
  //   description: 'Document type classification',
  //   example: 'Government ID',
  //   minLength: 1,
  //   maxLength: 100,
  // })
  // @IsString()
  // @IsNotEmpty()
  // @Length(1, 100)
  // document_type: string;

  @ApiPropertyOptional({
    description: 'User-facing instructions for document submission',
    example:
      'Please upload a clear, colored scan of your passport bio-data page. Ensure all text is readable and the image is not blurry.',
    maxLength: 2000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  instructions?: string;
}

/**
 * Update Document Master DTO
 *
 * DTO for updating an existing document master template.
 * All fields are optional to allow partial updates.
 */
export class UpdateDocumentMasterDto extends PartialType(
  CreateDocumentMasterDto,
) {}

/**
 * Document Master Response DTO
 *
 * DTO for document master API responses.
 * Includes all document master fields plus audit information.
 */
export class DocumentMasterResponseDto {
  @ApiProperty({
    description: 'Document master unique identifier',
    example: 'clx1234567890abcdef',
  })
  id: string;

  @ApiProperty({
    description: 'Document template name',
    example: 'Passport Copy',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Document description',
    example: 'A clear copy of the passport bio-data page',
  })
  description?: string;

  @ApiProperty({
    description: 'Document category for grouping',
    example: 'Identity Documents',
  })
  category: string;

  // Task 4: Removed document_type field - following non-destructive patterns
  // @ApiProperty({
  //   description: 'Document type classification',
  //   example: 'Government ID',
  // })
  // document_type: string;

  @ApiPropertyOptional({
    description: 'User-facing instructions for document submission',
    example:
      'Please upload a clear, colored scan of your passport bio-data page.',
  })
  instructions?: string;

  @ApiPropertyOptional({
    description: 'ID of user who created this document master',
    example: 'clx0987654321fedcba',
  })
  created_by?: string;

  @ApiPropertyOptional({
    description: 'ID of user who last updated this document master',
    example: 'clx0987654321fedcba',
  })
  updated_by?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2025-01-06T10:30:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2025-01-06T15:45:00.000Z',
  })
  updated_at: Date;
}

/**
 * Document Master Filters DTO
 *
 * DTO for filtering and pagination of document masters.
 */
export class DocumentMasterFiltersDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  page?: number;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional({
    description: 'Filter by document category',
    example: 'Identity Documents',
  })
  @IsOptional()
  @IsString()
  category?: string;

  // Task 4: Removed document_type filter - following non-destructive patterns
  // @ApiPropertyOptional({
  //   description: 'Filter by document type',
  //   example: 'Government ID',
  // })
  // @IsOptional()
  // @IsString()
  // document_type?: string;

  @ApiPropertyOptional({
    description: 'Search by name (partial match)',
    example: 'passport',
  })
  @IsOptional()
  @IsString()
  search?: string;
}

/**
 * Paginated Document Master Response DTO
 *
 * DTO for paginated document master list responses.
 */
export class PaginatedDocumentMasterResponseDto {
  @ApiProperty({
    description: 'Array of document masters',
    type: [DocumentMasterResponseDto],
  })
  data: DocumentMasterResponseDto[];

  @ApiProperty({
    description: 'Total number of document masters',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;
}

/**
 * Document Master Usage Check Response DTO
 *
 * DTO for checking if a document master is in use.
 */
export class DocumentMasterUsageResponseDto {
  @ApiProperty({
    description: 'Whether the document master is currently in use',
    example: true,
  })
  inUse: boolean;

  @ApiProperty({
    description: 'Number of applications using this document master',
    example: 5,
  })
  usageCount: number;

  @ApiPropertyOptional({
    description: 'Details about where the document master is being used',
    example: ['Immigration Application #12345', 'Service Application #67890'],
  })
  usageDetails?: string[];
}
