/**
 * Rate Limiting Middleware
 * Task 8: API Implementation and Documentation
 *
 * Implements rate limiting for API endpoints to prevent abuse
 */

import {
  Injectable,
  NestMiddleware,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

interface RateLimitOptions {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  message?: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

/**
 * Rate Limiting Middleware
 */
@Injectable()
export class RateLimitMiddleware implements NestMiddleware {
  private store: RateLimitStore = {};
  private readonly defaultOptions: RateLimitOptions = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // 100 requests per window
    message: 'Too many requests, please try again later',
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
  };

  constructor(private readonly configService: ConfigService) {}

  use(req: Request, res: Response, next: NextFunction) {
    const options = this.getOptionsForRoute(req.path);
    const key = this.generateKey(req);
    const now = Date.now();

    // Clean up expired entries
    this.cleanup();

    // Get or create rate limit entry
    let entry = this.store[key];
    if (!entry || now > entry.resetTime) {
      entry = {
        count: 0,
        resetTime: now + options.windowMs,
      };
      this.store[key] = entry;
    }

    // Check if limit exceeded
    if (entry.count >= options.maxRequests) {
      const resetTimeSeconds = Math.ceil((entry.resetTime - now) / 1000);

      res.set({
        'X-RateLimit-Limit': options.maxRequests.toString(),
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': entry.resetTime.toString(),
        'Retry-After': resetTimeSeconds.toString(),
      });

      throw new HttpException(
        {
          status: 'error',
          message: options.message,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            details: {
              limit: options.maxRequests,
              windowMs: options.windowMs,
              retryAfter: resetTimeSeconds,
            },
            timestamp: new Date().toISOString(),
          },
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    // Increment counter
    entry.count++;

    // Set rate limit headers
    res.set({
      'X-RateLimit-Limit': options.maxRequests.toString(),
      'X-RateLimit-Remaining': (options.maxRequests - entry.count).toString(),
      'X-RateLimit-Reset': entry.resetTime.toString(),
    });

    next();
  }

  /**
   * Generate unique key for rate limiting
   */
  private generateKey(req: Request): string {
    // Use IP address and user ID (if available) for more granular rate limiting
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userId = (req as any).user?.id || 'anonymous';
    const userAgent = req.get('User-Agent') || 'unknown';

    // Create a hash-like key
    return `${ip}:${userId}:${this.hashString(userAgent)}`;
  }

  /**
   * Get rate limit options based on route
   */
  private getOptionsForRoute(path: string): RateLimitOptions {
    // Different limits for different endpoints
    const routeConfigs: { [pattern: string]: Partial<RateLimitOptions> } = {
      '/auth/login': {
        windowMs: 15 * 60 * 1000, // 15 minutes
        maxRequests: 5, // 5 login attempts per 15 minutes
        message: 'Too many login attempts, please try again later',
      },
      '/auth/register': {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: 3, // 3 registration attempts per hour
        message: 'Too many registration attempts, please try again later',
      },
      '/documents/upload': {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: 10, // 10 uploads per minute
        message: 'Too many upload requests, please try again later',
      },
      '/admin/notifications': {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: 20, // 20 notifications per minute
        message: 'Too many notification requests, please try again later',
      },
      '/payment': {
        windowMs: 5 * 60 * 1000, // 5 minutes
        maxRequests: 10, // 10 payment attempts per 5 minutes
        message: 'Too many payment requests, please try again later',
      },
    };

    // Find matching route configuration
    for (const [pattern, config] of Object.entries(routeConfigs)) {
      if (path.includes(pattern)) {
        return { ...this.defaultOptions, ...config };
      }
    }

    // Return default options for unmatched routes
    return this.defaultOptions;
  }

  /**
   * Clean up expired entries from store
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of Object.entries(this.store)) {
      if (now > entry.resetTime) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach((key) => delete this.store[key]);
  }

  /**
   * Simple string hash function
   */
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}

/**
 * Rate Limit Decorator for specific endpoints
 */
export function RateLimit(options: Partial<RateLimitOptions>) {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor,
  ) {
    // Store rate limit options in metadata
    Reflect.defineMetadata('rateLimit', options, target, propertyName);
    return descriptor;
  };
}

/**
 * Rate Limit Guard for method-level rate limiting
 */
@Injectable()
export class RateLimitGuard {
  private store: RateLimitStore = {};

  canActivate(context: any): boolean {
    const request = context.switchToHttp().getRequest();
    const handler = context.getHandler();

    // Get rate limit options from metadata
    const options = Reflect.getMetadata('rateLimit', handler) || {};
    const mergedOptions = { ...this.getDefaultOptions(), ...options };

    const key = this.generateKey(request);
    const now = Date.now();

    // Clean up expired entries
    this.cleanup();

    // Get or create rate limit entry
    let entry = this.store[key];
    if (!entry || now > entry.resetTime) {
      entry = {
        count: 0,
        resetTime: now + mergedOptions.windowMs,
      };
      this.store[key] = entry;
    }

    // Check if limit exceeded
    if (entry.count >= mergedOptions.maxRequests) {
      throw new HttpException(
        {
          status: 'error',
          message: mergedOptions.message || 'Rate limit exceeded',
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            timestamp: new Date().toISOString(),
          },
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    // Increment counter
    entry.count++;
    return true;
  }

  private getDefaultOptions(): RateLimitOptions {
    return {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 100,
      message: 'Too many requests, please try again later',
    };
  }

  private generateKey(req: any): string {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userId = req.user?.id || 'anonymous';
    return `${ip}:${userId}`;
  }

  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of Object.entries(this.store)) {
      if (now > entry.resetTime) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach((key) => delete this.store[key]);
  }
}
