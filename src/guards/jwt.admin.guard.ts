import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

@Injectable()
export class JwtAdmin implements CanActivate {
  constructor(private jwtService: JwtService) {}
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) throw new UnauthorizedException('There is no bearer token');

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.jwtAdminSecretKey,
      });

      // Ensure the token is specifically for admins
      if (payload.tokenType && payload.tokenType !== 'admin') {
        throw new UnauthorizedException('Invalid token type for admin access');
      }

      request['user'] = payload;
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired admin token');
    }

    return true;
  }

  private extractTokenFromHeader(request: Request) {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
