import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';
import { TrainingDto } from './dto/training.dto';

@Injectable()
export class TrainingService {
  constructor(private prisma: PrismaService) {}

  async create(dto: TrainingDto) {
    const packages = this.prisma.training.create({
      data: dto,
    });

    return packages;
  }
  async update(id: string, dto: TrainingDto) {
    const packages = this.prisma.training.update({
      where: {
        id,
      },
      data: dto,
    });

    return packages;
  }

  async remove(id: string) {
    const packages = this.prisma.training.delete({
      where: {
        id,
      },
    });

    return packages;
  }

  async getAll() {
    const packages = this.prisma.training.findMany({
      orderBy: [{ order: 'asc' }, { updatedAt: 'asc' }],
    });

    return packages;
  }
}
