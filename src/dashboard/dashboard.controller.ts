import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
// import { DashboardService } from './dashboard.service';
import { DashboardRefactoredService } from './dashboard-refactored.service';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';

@ApiTags('dashboard')
@Controller('dashboard')
export class DashboardController {
  constructor(
    private dashboardRefactored: DashboardRefactoredService,
    // private dashboard: DashboardService
  ) {}

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Get('')
  async getAll() {
    // return await this.dashboard.get();
    return await this.dashboardRefactored.get();
  }
}
