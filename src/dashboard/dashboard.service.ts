import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';

@Injectable()
export class DashboardService {
  constructor(private prisma: PrismaService) {}

  async get() {
    const data = await this.prisma.$queryRawUnsafe(`
          WITH mentor_ratings AS (
            SELECT 
              m.id,
              m.name,
              m.image,
              COALESCE(AVG(r.rating)::numeric(10,2), 0) as average_rating,
              COUNT(r.id)::text as review_count,
              (
                SELECT COALESCE(SUM(ums.amount)::text, '0')
                FROM user_mentor_service ums
                JOIN service s ON s.id = ums."serviceId"
                WHERE s."mentorId" = m.id AND ums.status = 'paid'
              ) as revenue_generated,
              (
                SELECT COUNT(DISTINCT ums."userId")::text
                FROM user_mentor_service ums
                JOIN service s ON s.id = ums."serviceId"
                WHERE s."mentorId" = m.id AND ums.status = 'paid'
              ) as total_clients
            FROM mentor m
            LEFT JOIN review r ON r."mentorId" = m.id
            GROUP BY m.id, m.name, m.image
            HAVING COUNT(r.id) > 0
            ORDER BY average_rating DESC
            LIMIT 5
          ),
          latest_users AS (
            SELECT 
              id,
              name,
              email,
              image,
              "createdAt"
            FROM "user"
            ORDER BY "createdAt" DESC
            LIMIT 5
          ),
          latest_contacts AS (
            SELECT 
              id,
              name,
              email,
              message,
              "createdAt"
            FROM contact_us
            ORDER BY "createdAt" DESC
            LIMIT 5
          )
          SELECT 
            (SELECT COUNT(*)::text FROM mentor) AS total_mentors,
            (SELECT COUNT(*)::text FROM "user") AS total_users,
            (
              SELECT COALESCE(SUM(amount)::text, '0') 
              FROM (
                SELECT amount FROM user_mentor_service WHERE status = 'paid'
                UNION ALL
                SELECT amount FROM guest_mentor_service WHERE status = 'paid'
              ) AS mentor_service_total
            ) AS mentor_service_revenue,
            (
              SELECT COALESCE(SUM(amount)::text, '0') 
              FROM (
                SELECT amount FROM user_package WHERE status = 'paid'
                UNION ALL
                SELECT amount FROM guest_package WHERE status = 'paid'
              ) AS package_total
            ) AS package_revenue,
            (
              SELECT COALESCE(SUM(amount)::text, '0') 
              FROM (
                SELECT amount FROM user_immigration_service WHERE status = 'paid'
                UNION ALL
                SELECT amount FROM guest_immigration_service WHERE status = 'paid'
              ) AS immigration_service_total
            ) AS immigration_service_revenue,
            (
              SELECT COALESCE(SUM(amount)::text, '0') 
              FROM (
                SELECT amount FROM user_training WHERE status = 'paid'
                UNION ALL
                SELECT amount FROM guest_training WHERE status = 'paid'
              ) AS immigration_service_total
            ) AS training_revenue,
            (
              SELECT COALESCE(SUM(amount)::text, '0') 
              FROM (
                SELECT amount FROM user_mentor_service WHERE status = 'paid'
                UNION ALL
                SELECT amount FROM guest_mentor_service WHERE status = 'paid'
                UNION ALL
                SELECT amount FROM user_package WHERE status = 'paid'
                UNION ALL
                SELECT amount FROM guest_package WHERE status = 'paid'
                UNION ALL
                SELECT amount FROM user_immigration_service WHERE status = 'paid'
                UNION ALL
                SELECT amount FROM guest_immigration_service WHERE status = 'paid'
                UNION ALL
                SELECT amount FROM user_training WHERE status = 'paid'
                UNION ALL
                SELECT amount FROM guest_training WHERE status = 'paid'
              ) AS total
            ) AS total_revenue,
            (
              SELECT json_agg(row_to_json(mentor_ratings))
              FROM mentor_ratings
            ) AS top_rated_mentors,
            (
              SELECT json_agg(row_to_json(latest_users))
              FROM latest_users
            ) AS latest_users,
            (
              SELECT json_agg(row_to_json(latest_contacts))
              FROM latest_contacts
            ) AS latest_contacts
      `);

    return {
      ...data[0],
      top_rated_mentors:
        data[0].top_rated_mentors === null ? [] : data[0].top_rated_mentors,
      latest_users: data[0].latest_users === null ? [] : data[0].latest_users,
      latest_contacts:
        data[0].latest_contacts === null ? [] : data[0].latest_contacts,
    };
  }
}
