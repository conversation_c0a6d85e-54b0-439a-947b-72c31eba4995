/**
 * Workflow Template Interfaces
 *
 * This file contains TypeScript interfaces for workflow template operations.
 * These interfaces provide type safety and structure for workflow template
 * management across the application.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { IJWTPayload } from '../../types/auth';

/**
 * Service Type Enum
 * Defines the available service types for workflow templates
 */
export type ServiceType =
  | 'immigration'
  | 'training'
  | 'packages'
  | 'consulting';

/**
 * Document Configuration Interface
 * Defines the structure for document requirements in workflow stages
 */
export interface IDocumentConfig {
  documentName: string;
  required: boolean;
}

/**
 * Custom Form Field Interface
 * Defines the structure for custom form fields in workflow stages
 */
export interface ICustomFormField {
  fieldName: string;
  fieldType: string; // Allow any string to match DTO validation
  required: boolean;
  options?: string[]; // For select fields
  validation?: {
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: string;
  };
}

/**
 * Workflow Stage Interface
 * Defines the structure of a single workflow stage
 */
export interface IWorkflowStage {
  stageName: string;
  stageOrder: number;
  description?: string;
  estimatedDuration?: number; // in days
  documentsRequired?: boolean;
  documents?: IDocumentConfig[];
  customFormRequired?: boolean;
  customForm?: ICustomFormField[];
  assigneeRole?: 'admin' | 'applicant' | 'reviewer';
  autoAdvance?: boolean;
  completionCriteria?: Record<string, any>;
  showToClient?: boolean; // Controls whether this stage is visible to clients
}

/**
 * Workflow Template Entity Interface
 * Represents the structure of a workflow template in the database
 */
export interface IWorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  serviceType: ServiceType;
  serviceId?: string;
  isActive: boolean;
  workflowTemplate: IWorkflowStage[];
  createdBy?: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Workflow Template Usage Interface
 * Represents usage statistics for a workflow template
 */
export interface IWorkflowTemplateUsage {
  templateId: string;
  activeApplications: number;
  totalApplications: number;
  canDelete: boolean;
  usageDetails: {
    serviceType: string;
    count: number;
  }[];
}

/**
 * Paginated Workflow Templates Interface
 * Represents paginated results for workflow template queries
 */
export interface IPaginatedWorkflowTemplates {
  data: IWorkflowTemplate[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Workflow Template Service Interface
 * Defines the contract for workflow template service operations
 */
export interface IWorkflowTemplateService {
  create(
    dto: any,
    adminUser?: IJWTPayload | string,
  ): Promise<IWorkflowTemplate>;
  findAll(filters?: any): Promise<IPaginatedWorkflowTemplates>;
  findOne(id: string): Promise<IWorkflowTemplate>;
  update(
    id: string,
    dto: any,
    adminUser?: IJWTPayload | string,
  ): Promise<IWorkflowTemplate>;
  remove(id: string, adminUser?: IJWTPayload | string): Promise<void>;
  checkUsage(id: string): Promise<IWorkflowTemplateUsage>;

  findByServiceId(serviceId: string): Promise<IWorkflowTemplate[]>;
}
