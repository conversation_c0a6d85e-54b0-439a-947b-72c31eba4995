import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PasswordService } from './password.service';
import { ForgotPasswordDto, ResetPasswordDto } from './dto/password.dto';

@ApiTags('password')
@Controller('password')
export class PasswordController {
  constructor(private password: PasswordService) {}

  @Post('forgot-password')
  async forgot_password(@Body() dto: ForgotPasswordDto) {
    return await this.password.forgotPassword(dto);
  }
  @Post('reset-password')
  async reset_password(@Body() dto: ResetPasswordDto) {
    return await this.password.resetPassword(dto);
  }
}
