# Notification Configuration System

## Overview

The Career Ireland API uses a unified notification configuration system that manages all notification settings through a single configuration file. This approach simplifies notification management and provides consistent default behavior across the platform.

## Configuration File Structure

### Location
```
config/notification-settings.json
```

### File Format
```json
{
  "id": "default-settings",
  "user_id": "system",
  "agent_assigned": true,
  "case_status_update": true,
  "agent_query": true,
  "document_rejection": true,
  "missing_document_reminder_days": 7,
  "system_maintenance": true,
  "final_decision_issued": true,
  "created_at": "2025-07-18T00:00:00.000Z",
  "updated_at": "2025-07-18T00:00:00.000Z"
}
```

### Field Descriptions

| Field | Type | Description | Default |
|-------|------|-------------|---------|
| `id` | string | Unique identifier for the settings | "default-settings" |
| `user_id` | string | User ID who last updated settings | "system" |
| `agent_assigned` | boolean | Send notifications when agent is assigned | true |
| `case_status_update` | boolean | Send notifications for case status changes | true |
| `agent_query` | boolean | Send notifications for agent queries | true |
| `document_rejection` | boolean | Send notifications for document rejections | true |
| `missing_document_reminder_days` | number | Days between missing document reminders | 7 |
| `system_maintenance` | boolean | Send system maintenance notifications | true |
| `final_decision_issued` | boolean | Send notifications for final decisions | true |
| `created_at` | string | ISO timestamp when settings were created | Current time |
| `updated_at` | string | ISO timestamp when settings were last updated | Current time |

## API Endpoints

### Get Notification Settings
```http
GET /notifications/settings
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "agent_assigned": true,
  "case_status_update": true,
  "agent_query": true,
  "document_rejection": true,
  "missing_document_reminder_days": 7,
  "system_maintenance": true,
  "final_decision_issued": true
}
```

### Update Notification Settings
```http
PUT /notifications/settings
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "agent_assigned": false,
  "missing_document_reminder_days": 14
}
```

**Response:**
```json
{
  "agent_assigned": false,
  "case_status_update": true,
  "agent_query": true,
  "document_rejection": true,
  "missing_document_reminder_days": 14,
  "system_maintenance": true,
  "final_decision_issued": true
}
```

## Service Implementation

### NotificationSettingsStorageService

The `NotificationSettingsStorageService` handles all file operations for the unified configuration:

#### Key Methods

- `readSettings(userId: string)`: Reads unified config and returns user-specific settings
- `writeSettings(userId: string, data: NotificationSettingsData)`: Updates system defaults
- `updateSettings(userId: string, updates: Partial<NotificationSettingsData>)`: Merges updates with existing settings
- `settingsExist(userId: string)`: Checks if unified config file exists

#### Usage Example

```typescript
// Read settings for a user
const settings = await notificationSettingsStorage.readSettings('user123');

// Update settings
const updatedSettings = await notificationSettingsStorage.updateSettings('user123', {
  agent_assigned: false,
  missing_document_reminder_days: 14
});
```

## Migration from Individual Files

### Previous Approach
- Individual files: `config/{userId}.json`
- Separate file per user
- Complex file management

### Current Approach
- Single file: `config/notification-settings.json`
- Unified system defaults
- Simplified management

### Migration Steps
1. Individual user files are no longer used
2. All users inherit from unified system defaults
3. User-specific preferences handled in memory
4. Old individual files can be safely removed

## Error Handling

The system includes comprehensive error handling:

- **File Not Found**: Returns default settings if config file doesn't exist
- **Invalid JSON**: Logs error and returns null
- **Permission Errors**: Logs error and throws exception
- **Validation Errors**: Validates required fields before processing

## Logging

All notification configuration operations are logged:

- File read/write operations
- Error conditions
- Settings updates
- User access patterns

## Security Considerations

- JWT authentication required for all endpoints
- User-specific access control
- Input validation on all updates
- Secure file operations with proper error handling

## Performance Benefits

- Single file read instead of multiple individual files
- Reduced I/O operations
- Simplified caching strategies
- Better resource utilization

## Future Enhancements

- User-specific override capabilities
- Role-based notification defaults
- Advanced notification scheduling
- Integration with external notification services
