# Test Commands Reference

This document provides a comprehensive list of all available test commands for the Career Ireland API project.

## 📋 General Test Commands

### Basic Test Commands
```bash
# Run all tests
npm test

# Run tests in watch mode (re-runs on file changes)
npm run test:watch

# Run tests with coverage report
npm run test:cov

# Run tests in debug mode
npm run test:debug

# Note: For specific test files, use the module-specific commands below
# The general test commands only work with files in the src directory

# Run end-to-end tests
npm run test:e2e
```

## 💳 Payment Module Test Commands

### All Payment Tests
```bash
# Run all payment tests
npm run test:payment

# Run payment tests in watch mode
npm run test:payment:watch

# Run payment tests with coverage
npm run test:payment:cov

# Run payment tests in debug mode
npm run test:payment:debug
```

### Specific Payment Test Files
```bash
# Run unified payment service tests
npm run test:payment:service

# Run payment controller tests
npm run test:payment:controller

# Run email template tests (includes mentor logic tests)
npm run test:payment:email

# Run payment integration tests
npm run test:payment:integration
```

## 🎯 Examples

### Running Specific Test Files
```bash
# Example: Run only the email template tests (includes mentor logic tests)
npm run test:payment:email

# Example: Run only the service tests
npm run test:payment:service

# Example: Run only the controller tests
npm run test:payment:controller

# Example: Run only the integration tests
npm run test:payment:integration

# Note: For files outside the payment module, use the full Jest command:
# jest --config ./test/[module]/jest.config.js test/[module]/[file].spec.ts
```

### Running Tests with Filters
```bash
# Run tests matching a pattern
npm test -- --testNamePattern="mentor"

# Run tests in a specific directory
npm test -- test/payment/

# Run tests with verbose output
npm test -- --verbose
```

### Watch Mode Examples
```bash
# Watch all payment tests
npm run test:payment:watch

# Watch specific test file
npm run test:file:watch test/payment/email-template.spec.ts
```

## 🔍 Test Coverage

### Generate Coverage Reports
```bash
# Generate coverage for all tests
npm run test:cov

# Generate coverage for payment module only
npm run test:payment:cov
```

## 🐛 Debugging Tests

### Debug Mode
```bash
# Debug all tests
npm run test:debug

# Debug payment tests
npm run test:payment:debug
```

## 📊 Test Results Summary

### Current Test Status
- **Total Test Suites**: 4 passed
- **Total Tests**: 73 passed
- **Payment Module Tests**: 
  - Email Template Tests: 13 tests ✅
  - Service Tests: 33 tests ✅
  - Controller Tests: 20 tests ✅
  - Integration Tests: 7 tests ✅

### Key Test Categories
1. **Mentor Logic Tests** - Validates conditional mentor inclusion in emails
2. **Payment Flow Tests** - Tests complete payment workflows
3. **Email Template Tests** - Tests email rendering and fallback templates
4. **Integration Tests** - Tests database operations and API endpoints

## 🚀 Quick Commands for Development

```bash
# Most commonly used commands during development:

# Run email template tests (for mentor logic validation)
npm run test:payment:email

# Run all payment tests
npm run test:payment

# Run tests in watch mode while developing
npm run test:payment:watch

# Run specific test with pattern matching
npm test -- --testNamePattern="mentor logic"
```

## 📝 Notes

- All payment tests use the Jest configuration in `./test/payment/jest.config.js`
- Test files are located in the `test/payment/` directory
- Email template tests include comprehensive mentor logic validation
- Integration tests connect to a test database (not production)
- Coverage reports are generated in the `coverage/` directory
