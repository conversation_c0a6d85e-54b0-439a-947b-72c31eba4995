# CareerIreland API - Comprehensive Endpoint Documentation
**Complete API Reference with Request/Response Examples**

## Overview

The CareerIreland API is a comprehensive NestJS-based backend platform providing complete career services infrastructure. This documentation covers all 150+ implemented endpoints across 19 major feature modules with detailed request/response examples, authentication requirements, and error handling.

## Table of Contents

1. [Authentication Endpoints](#authentication-endpoints) (8 endpoints)
2. [User Management](#user-management) (15 endpoints)
3. [Admin Management](#admin-management) (10 endpoints)
4. [Mentor Management](#mentor-management) (12 endpoints)
5. [Agent Management](#agent-management) (8 endpoints)
6. [Payment Processing](#payment-processing) (20 endpoints)
7. [Document Management](#document-management) (18 endpoints)
8. [Application Processing](#application-processing) (25 endpoints)
9. [Immigration Services](#immigration-services) (8 endpoints)
10. [Training Programs](#training-programs) (8 endpoints)
11. [Package Management](#package-management) (8 endpoints)
12. [Blog & Content](#blog-content) (10 endpoints)
13. [Review & Rating](#review-rating) (8 endpoints)
14. [Contact & Communication](#contact-communication) (5 endpoints)
15. [Media & File Management](#media-file-management) (5 endpoints)

## Base URL
```
Production: https://api.careerireland.com
Development: http://localhost:3000
```

## Authentication

All endpoints require JWT authentication unless specified otherwise. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Authentication Levels
- **User**: Regular authenticated users (`UserGuard`)
- **Admin**: Administrative users with elevated permissions (`AdminGuard`)
- **Mentor**: Mentor users for service management (`MentorGuard`)
- **Agent**: Agent users for application processing (`AgentGuard`)
- **Public**: No authentication required

## Standard Response Format

All API responses follow a consistent format:

```json
{
  "status": "success" | "error",
  "message": "Human-readable message",
  "data": {}, // Response data (optional)
  "pagination": {}, // Pagination info (for paginated responses)
  "meta": {
    "timestamp": "2024-01-15T10:00:00Z",
    "version": "1.0"
  }
}
```

## Error Response Format

```json
{
  "status": "error",
  "message": "Error description",
  "error": {
    "code": "ERROR_CODE",
    "details": "Additional error information"
  },
  "meta": {
    "timestamp": "2024-01-15T10:00:00Z",
    "correlationId": "req-123456"
  }
}
```

---

# Authentication Endpoints

## 1. User Authentication

### POST /user/register
**Description:** Register a new user account
**Authentication:** Public
**Rate Limit:** 5 requests per minute

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "confirmPassword": "SecurePassword123!"
}
```

**Response (201 Created):**
```json
{
  "status": "success",
  "message": "User registered successfully. Please verify your email.",
  "data": {
    "user": {
      "id": "user_123456789",
      "name": "John Doe",
      "email": "<EMAIL>",
      "emailVerified": false,
      "createdAt": "2024-01-15T10:00:00Z"
    }
  }
}
```

### POST /user/login
**Description:** Authenticate user and get access tokens
**Authentication:** Public
**Rate Limit:** 10 requests per minute

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Login successful",
  "data": {
    "user": {
      "id": "user_123456789",
      "name": "John Doe",
      "email": "<EMAIL>",
      "emailVerified": true
    },
    "backendTokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 1642248000000
    }
  }
}
```

### POST /user/google
**Description:** Google OAuth authentication
**Authentication:** Public

**Request Body:**
```json
{
  "token": "google_oauth_token_here"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Google authentication successful",
  "data": {
    "user": {
      "id": "user_123456789",
      "name": "John Doe",
      "email": "<EMAIL>",
      "emailVerified": true,
      "provider": "google"
    },
    "backendTokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 1642248000000
    }
  }
}
```

### POST /user/verify
**Description:** Verify user email with OTP
**Authentication:** Public

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Email verified successfully",
  "data": {
    "emailVerified": true
  }
}
```

### POST /user/refresh
**Description:** Refresh access token
**Authentication:** Public

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Token refreshed successfully",
  "data": {
    "backendTokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 1642248000000
    }
  }
}
```

## 2. Admin Authentication

### POST /admin/login
**Description:** Admin user authentication
**Authentication:** Public

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "AdminPassword123!"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Admin login successful",
  "data": {
    "admin": {
      "id": "admin_123456789",
      "name": "Admin User",
      "email": "<EMAIL>",
      "role": "admin"
    },
    "backendTokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 1642248000000
    }
  }
}
```

## 3. Mentor Authentication

### POST /mentor/login
**Description:** Mentor user authentication
**Authentication:** Public

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "MentorPassword123!"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Mentor login successful",
  "data": {
    "user": {
      "id": "mentor_123456789",
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "role": "mentor",
      "specialization": "Immigration Law"
    },
    "backendTokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 1642248000000
    }
  }
}
```

## 4. Agent Authentication

### POST /agent/login
**Description:** Agent user authentication
**Authentication:** Public

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "AgentPassword123!"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Agent login successful",
  "data": {
    "user": {
      "id": "agent_123456789",
      "name": "Mike Johnson",
      "email": "<EMAIL>",
      "status": "Active",
      "department": "Immigration Processing"
    },
    "backendTokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 1642248000000
    }
  }
}
```

---

# User Management

## GET /user
**Description:** Get current user profile
**Authentication:** User Guard

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "User profile retrieved successfully",
  "data": {
    "user": {
      "id": "user_123456789",
      "name": "John Doe",
      "email": "<EMAIL>",
      "emailVerified": true,
      "phone": "+353-1-234-5678",
      "address": "123 Main St, Dublin, Ireland",
      "createdAt": "2024-01-15T10:00:00Z",
      "updatedAt": "2024-01-15T10:00:00Z"
    }
  }
}
```

## PATCH /user
**Description:** Update user profile
**Authentication:** User Guard

**Request Body:**
```json
{
  "name": "John Updated Doe",
  "phone": "+353-1-234-5679",
  "address": "456 New St, Dublin, Ireland"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "User profile updated successfully",
  "data": {
    "user": {
      "id": "user_123456789",
      "name": "John Updated Doe",
      "email": "<EMAIL>",
      "phone": "+353-1-234-5679",
      "address": "456 New St, Dublin, Ireland",
      "updatedAt": "2024-01-15T11:00:00Z"
    }
  }
}
```

## DELETE /user
**Description:** Delete user account
**Authentication:** User Guard

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "User account deleted successfully",
  "data": {
    "deletedAt": "2024-01-15T12:00:00Z"
  }
}
```

---

# Payment Processing

## 1. Unified Payment System (v2)

### POST /v2/payment/create
**Description:** Create payment for any service type
**Authentication:** User Guard (for user payments) / Public (for guest payments)

**Request Body (User Payment):**
```json
{
  "service_type": "immigration",
  "service_id": "imm_service_123",
  "payment_type": "user",
  "payment_method": "card",
  "amount": 150.00,
  "actual_amount": 200.00,
  "discount_amount": 50.00,
  "currency": "EUR",
  "transaction_id": null
}
```

**Request Body (Guest Payment):**
```json
{
  "service_type": "immigration",
  "service_id": "imm_service_123",
  "payment_type": "guest",
  "payment_method": "card",
  "amount": 150.00,
  "currency": "EUR",
  "guest_info": {
    "name": "Guest User",
    "email": "<EMAIL>",
    "phone": "+353-1-234-5678"
  }
}
```

**Response (201 Created) - Stripe Payment:**
```json
{
  "status": "success",
  "message": "Payment created successfully",
  "data": {
    "payment_id": "pay_123456789",
    "stripe_link": "https://checkout.stripe.com/pay/cs_test_123456789",
    "status": "pending"
  }
}
```

**Response (201 Created) - Non-Stripe Payment:**
```json
{
  "status": "success",
  "message": "Payment created successfully",
  "data": {
    "payment_id": "pay_123456789",
    "status": "paid"
  }
}
```

### POST /payment/webhook
**Description:** Stripe webhook handler
**Authentication:** Public (Stripe signature verification)

**Request Body (Stripe Event):**
```json
{
  "id": "evt_123456789",
  "object": "event",
  "type": "checkout.session.completed",
  "data": {
    "object": {
      "id": "cs_test_123456789",
      "payment_status": "paid",
      "metadata": {
        "payment_id": "pay_123456789",
        "service_type": "immigration"
      }
    }
  }
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Webhook processed successfully"
}
```

## 2. Legacy Payment System (v1)

### POST /payment/mentor-service
**Description:** Create payment for mentor service
**Authentication:** User Guard

**Request Body:**
```json
{
  "serviceId": "service_123456789"
}
```

**Response (200 OK):**
```json
{
  "status": "OK",
  "url": "https://checkout.stripe.com/pay/cs_test_123456789"
}
```

---

# Document Management

## POST /document/upload
**Description:** Upload documents to document vault
**Authentication:** User Guard

**Request (Multipart Form Data):**
```
Content-Type: multipart/form-data

files: [File1.pdf, File2.jpg]
document_type: "passport"
application_id: "app_123456789"
```

**Response (201 Created):**
```json
{
  "status": "success",
  "message": "Documents uploaded successfully",
  "data": {
    "uploaded_documents": [
      {
        "id": "doc_123456789",
        "filename": "File1.pdf",
        "file_url": "https://storage.supabase.co/object/public/documents/File1.pdf",
        "file_size": 1024000,
        "document_type": "passport",
        "status": "pending_verification"
      },
      {
        "id": "doc_123456790",
        "filename": "File2.jpg",
        "file_url": "https://storage.supabase.co/object/public/documents/File2.jpg",
        "file_size": 512000,
        "document_type": "passport",
        "status": "pending_verification"
      }
    ]
  }
}
```

## GET /document/vault
**Description:** Get user's document vault
**Authentication:** User Guard

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Document vault retrieved successfully",
  "data": {
    "documents": [
      {
        "id": "doc_123456789",
        "filename": "passport.pdf",
        "document_type": "passport",
        "file_size": 1024000,
        "status": "verified",
        "uploaded_at": "2024-01-15T10:00:00Z",
        "verified_at": "2024-01-15T11:00:00Z",
        "expires_at": "2025-01-15T10:00:00Z"
      }
    ],
    "total_documents": 1,
    "storage_used": "1.02 MB",
    "storage_limit": "100 MB"
  }
}
```

---

# Application Processing

## POST /application/create
**Description:** Create new application
**Authentication:** User Guard

**Request Body:**
```json
{
  "workflow_template_id": "wf_template_123",
  "service_type": "immigration",
  "service_id": "imm_service_123",
  "form_data": {
    "personal_info": {
      "full_name": "John Doe",
      "date_of_birth": "1990-01-15",
      "nationality": "Irish"
    },
    "contact_info": {
      "email": "<EMAIL>",
      "phone": "+353-1-234-5678"
    }
  }
}
```

**Response (201 Created):**
```json
{
  "status": "success",
  "message": "Application created successfully",
  "data": {
    "application": {
      "id": "app_123456789",
      "application_number": "CI-2024-001234",
      "workflow_template_id": "wf_template_123",
      "service_type": "immigration",
      "status": "submitted",
      "current_step": 1,
      "total_steps": 5,
      "assigned_agent_id": "agent_123456789",
      "created_at": "2024-01-15T10:00:00Z",
      "estimated_completion": "2024-02-15T10:00:00Z"
    }
  }
}
```

## GET /application/:id/status
**Description:** Get application status and progress
**Authentication:** User Guard

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Application status retrieved successfully",
  "data": {
    "application": {
      "id": "app_123456789",
      "application_number": "CI-2024-001234",
      "status": "in_progress",
      "current_step": 3,
      "total_steps": 5,
      "progress_percentage": 60,
      "assigned_agent": {
        "id": "agent_123456789",
        "name": "Mike Johnson",
        "email": "<EMAIL>"
      },
      "status_history": [
        {
          "status": "submitted",
          "timestamp": "2024-01-15T10:00:00Z",
          "note": "Application submitted successfully"
        },
        {
          "status": "under_review",
          "timestamp": "2024-01-16T09:00:00Z",
          "note": "Initial review completed"
        },
        {
          "status": "in_progress",
          "timestamp": "2024-01-17T14:00:00Z",
          "note": "Processing documents"
        }
      ],
      "next_steps": [
        "Document verification",
        "Final review",
        "Approval"
      ]
    }
  }
}
```
    "details": {},
    "timestamp": "2024-01-15T10:00:00Z"
  }
}
```

## Rate Limiting

API endpoints are rate-limited to prevent abuse:

- **Default**: 100 requests per 15 minutes
- **Authentication**: 5 requests per 15 minutes
- **File Upload**: 10 requests per minute
- **Admin Operations**: 20 requests per minute

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248000
```

## Application Management Endpoints

### Get User Applications
```http
GET /immigration/applications
GET /packages/applications
GET /services/applications
GET /training/applications
```

**Authentication**: User

**Query Parameters**:
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 10, max: 100)
- `status` (string, optional): Filter by application status
- `service_type` (string, optional): Filter by service type
- `priority_level` (string, optional): Filter by priority level

**Response**:
```json
{
  "status": "success",
  "message": "Applications retrieved successfully",
  "applications": [
    {
      "id": "app-123",
      "application_number": "IMM-2024-001",
      "application_type": "immigration",
      "service_type": "work_permit",
      "status": "In_Progress",
      "priority_level": "Medium",
      "user": {
        "id": "user-123",
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "current_step": {
        "id": "step-1",
        "step_name": "Document Upload",
        "status": "In_Progress",
        "due_date": "2024-01-20T00:00:00Z"
      },
      "steps": [...],
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-15T12:00:00Z"
    }
  ],
  "total": 25,
  "page": 1,
  "limit": 10,
  "totalPages": 3
}
```

### Get Application Details
```http
GET /immigration/applications/:id
```

**Authentication**: User

**Parameters**:
- `id` (string): Application ID

**Response**: Single application object with full details

### Update Application Step Status
```http
PATCH /immigration/applications/:id/steps/:stepNumber
```

**Authentication**: User

**Parameters**:
- `id` (string): Application ID
- `stepNumber` (number): Step number to update

**Request Body**:
```json
{
  "status": "Completed" | "In_Progress" | "Pending" | "Rejected",
  "completion_notes": "Optional completion notes",
  "completion_data": {} // Optional step-specific data
}
```

### Get All Applications (Admin)
```http
GET /immigration/applications/admin/all
```

**Authentication**: Admin

**Query Parameters**: Same as user endpoint plus:
- `user_id` (string, optional): Filter by user ID
- `assigned_to` (string, optional): Filter by assigned admin

### Update Application Status (Admin)
```http
PATCH /immigration/applications/admin/:id/status
```

**Authentication**: Admin

**Request Body**:
```json
{
  "status": "Submitted" | "In_Progress" | "Under_Review" | "Approved" | "Rejected",
  "assigned_to": "admin-user-id",
  "sla_deadline": "2024-01-25T00:00:00Z",
  "estimated_completion": "2024-01-30T00:00:00Z"
}
```

## Document Management Endpoints

### Upload Document
```http
POST /documents/upload
```

**Authentication**: User

**Content-Type**: `multipart/form-data`

**Request Body**:
- `file` (file): Document file (max 25MB)
- `document_name` (string): Name for the document
- `document_type` (string): Document type (Passport, Visa, etc.)
- `document_category` (string, optional): Custom category
- `expiry_date` (string, optional): ISO date string
- `tags` (array, optional): Array of tags
- `application_id` (string, optional): Link to application

**Response**:
```json
{
  "status": "success",
  "message": "Document uploaded successfully",
  "data": {
    "id": "doc-123",
    "document_name": "Passport",
    "document_type": "Passport",
    "file_path": "https://storage.com/doc.pdf",
    "status": "Pending",
    "created_at": "2024-01-15T10:00:00Z"
  }
}
```

### Get User Documents
```http
GET /documents
```

**Authentication**: User

**Query Parameters**:
- `page`, `limit`: Pagination
- `document_type` (string, optional): Filter by type
- `status` (string, optional): Filter by status
- `tags` (string, optional): Comma-separated tags
- `search` (string, optional): Search in names and content

### Verify Document (Admin)
```http
PATCH /documents/:id/verify
```

**Authentication**: Admin

**Request Body**:
```json
{
  "verification_status": "verified" | "rejected",
  "verification_notes": "Optional verification notes"
}
```

### Delete Application Document
```http
DELETE /applications/{application_id}/documents/{document_id}
```

**Description:** Delete a document from an application with role-based permissions and proper cleanup

**Authentication:** User (JWT Guard)

**Parameters:**
- `application_id` (string): Application ID
- `document_id` (string): Application document ID

**Authorization Rules:**
- **Regular Users**: Can only delete non-approved documents from their own applications or documents they uploaded
- **Admin/Agent Users**: Can delete any document including approved documents

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Document deleted successfully",
  "data": {
    "documentId": "ad_123456789",
    "applicationId": "app_123456789",
    "deletedFiles": [
      "documents/app_123456789/passport.pdf"
    ]
  }
}
```

**Error Responses:**

**400 Bad Request** - Document not uploaded:
```json
{
  "statusCode": 400,
  "message": "Cannot delete document that has not been uploaded",
  "error": "Bad Request"
}
```

**403 Forbidden** - Insufficient permissions:
```json
{
  "statusCode": 403,
  "message": "Regular users cannot delete approved documents. Please contact an administrator.",
  "error": "Forbidden"
}
```

**404 Not Found** - Document not found:
```json
{
  "statusCode": 404,
  "message": "Document with ID ad_123456789 not found in application app_123456789",
  "error": "Not Found"
}
```

**Features:**
- **Role-Based Access Control**: Different permissions for regular users vs admin/agent users
- **Ownership Validation**: Users can only delete documents from their own applications
- **File Cleanup**: Automatically removes files from Supabase storage
- **Complete Database Cleanup**: Updates `application_document` status and completely removes `document_vault` entries
- **Audit Logging**: Comprehensive logging for audit trail
- **Transaction Safety**: Uses database transactions with automatic rollback on failures
- **Graceful Error Handling**: Handles missing files in storage without failing the operation

## Admin Notification Endpoints

### Send Notification
```http
POST /admin/notifications
```

**Authentication**: Admin

**Request Body**:
```json
{
  "notification_type": "Email" | "SMS" | "In_App",
  "recipient_email": "<EMAIL>",
  "recipient_mobile": "+353123456789", // For SMS
  "subject": "Notification Subject",
  "message_body": "<p>HTML content for email or plain text for SMS</p>",
  "application_id": "app-123", // Optional
  "document_id": "doc-123", // Optional
  "metadata": {} // Optional additional data
}
```

### Schedule Notification
```http
POST /admin/notifications/schedule
```

**Authentication**: Admin

**Request Body**: Same as send notification plus:
```json
{
  "scheduled_at": "2024-01-20T10:00:00Z"
}
```

### Send Notification from Template
```http
POST /admin/notifications/template
```

**Authentication**: Admin

**Request Body**:
```json
{
  "template_id": "template-123",
  "context": {
    "name": "John Doe",
    "application_number": "IMM-2024-001",
    "status": "Approved"
  }
}
```

### Get Notification Statistics
```http
GET /admin/notifications/stats
```

**Authentication**: Admin

**Query Parameters**:
- `application_id` (string, optional): Filter by application

**Response**:
```json
{
  "status": "success",
  "data": {
    "Sent": 150,
    "Pending": 25,
    "Failed": 5
  }
}
```

## Workflow Template Endpoints

### Create Workflow Template
```http
POST /admin/workflow-templates
```

**Authentication**: Admin

**Request Body**:
```json
{
  "template_name": "Immigration Work Permit Process",
  "application_type": "immigration",
  "service_type": "work_permit",
  "description": "Standard workflow for work permit applications",
  "is_active": true,
  "estimated_duration": 168, // hours
  "sla_threshold": 240, // hours
  "steps": [
    {
      "step_order": 1,
      "step_name": "Document Upload",
      "required_fields": ["passport", "visa_application"],
      "validation_rules": {
        "file_types": ["pdf", "jpg", "png"],
        "max_size": "10MB"
      },
      "completion_criteria": {
        "all_documents_uploaded": true
      },
      "estimated_duration": 24,
      "assignee_role": "applicant"
    }
  ]
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `UNAUTHORIZED` | Authentication required |
| `FORBIDDEN` | Insufficient permissions |
| `NOT_FOUND` | Resource not found |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `FILE_TOO_LARGE` | Uploaded file exceeds size limit |
| `INVALID_FILE_TYPE` | Unsupported file type |
| `NOTIFICATION_FAILED` | Failed to send notification |
| `TEMPLATE_NOT_FOUND` | Workflow template not found |

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Unprocessable Entity
- `429` - Too Many Requests
- `500` - Internal Server Error

## Testing

### Running Tests
```bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# API endpoint tests
npm run test:api

# All tests with coverage
npm run test:coverage
```

### Test Coverage Requirements
- Minimum 80% overall coverage
- Minimum 90% coverage for controllers
- All endpoints must have integration tests
- Authentication and authorization tests required

## Changelog

### Version 1.0.0 (Task 8)
- ✅ Implemented concrete application controllers
- ✅ Added admin notification endpoints
- ✅ Added document verification endpoint
- ✅ Implemented standardized response formats
- ✅ Added rate limiting middleware
- ✅ Created comprehensive test suite
- ✅ Updated OpenAPI documentation
