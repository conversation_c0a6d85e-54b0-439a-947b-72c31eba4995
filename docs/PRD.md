I want to implement a comprehensive Dynamic Immigration Workflow System that extends the existing unified payment system architecture without modifying core database tables or stable production code, following the established non-destructive patterns from our previous implementations.

**Context Files to Reference:**
- `src/payment/unified-payment.service.ts` (primary extension point)
- `src/payment/unified-payment.controller.ts` (controller patterns to follow)
- `prisma/schema/payment.prisma` (schema extension reference)
- Previous polymorphic implementations with JSONB metadata patterns

**System Architecture Requirements:**

1. **Configurable Immigration Package Workflows**
   - Create master workflow registry with standardized workflow names and identifiers
   - Implement workflow engine that triggers when `paymentType` includes immigration packages on successful payment completion
   - Store admin-configurable workflow steps as JSON metadata in polymorphic database tables using discriminator columns
   - Each workflow step must include: `step_name`, `required_fields`, `validation_rules`, `completion_criteria`, `step_order`, `estimated_duration`, `assignee_role`, `sla_threshold`
   - Implement the following 15-step immigration workflow template with configurable step ordering:
     * Step 1: Personal Details Collection (name, country, DOB, contact info, emergency contact)
     * Step 2: Document Upload Phase (passport, resume, offer letter, educational certificates)
     * Step 3: Additional Information (work experience, references, medical history if required)
     * Step 4: Review & Submission (applicant confirmation, terms acceptance)
     * Step 5: Checkpoint Call (scheduled consultation with calendar integration)
     * Step 6: Application Filing (internal processing with automated form generation)
     * Step 7: Application Review (internal quality check with checklist validation)
     * Step 8: Application Submission (to DETE/authorities with tracking reference)
     * Step 9: Timeline Communication (expected outcome dates with milestone tracking)
     * Step 10: DETE Queries Handling (respond to authority requests with deadline tracking)
     * Step 11: Application Review (post-query assessment with impact analysis)
     * Step 12: Query Response Submission (answers to authorities with confirmation receipts)
     * Step 13: Work Permit Decision (approved/rejected notification with next steps)
     * Step 14: Appeal Process (if rejection occurs, with appeal deadline tracking)
     * Step 15: Final Appeal Decision (ultimate outcome with case closure procedures)

2. **Dynamic Document Requirements Management**
   - Create master document registry with predefined categories: Identity, Education, Employment, Medical, Legal, Financial, Immigration_Specific
   - Implement package-specific document configuration via admin panel with drag-and-drop interface and real-time preview
   - Document metadata structure: `document_type`, `format_requirements` (PDF/JPG/PNG), `max_file_size` (configurable per type), `validation_rules` (regex patterns), `is_mandatory`, `expiry_applicable`, `template_url`, `help_text`, `example_url`
   - Provide document templates and examples for applicant guidance with downloadable samples
   - Implement document requirement inheritance system (base package requirements + add-on requirements + conditional requirements based on applicant profile)
   - Support for conditional document requirements based on applicant answers (e.g., medical documents only if health issues declared)

3. **Individual Applicant Custom Document Requests**
   - Create case-specific document request system independent of package defaults with workflow integration
   - Admin/agent interface to add custom document requests with fields: `document_name`, `reason_for_request`, `deadline`, `priority_level` (Low/Medium/High/Critical), `applicant_id`, `requesting_agent_id`, `internal_notes`
   - Integrate custom requests into existing workflow without disrupting standard flow using event-driven architecture
   - Maintain complete audit trail: `who_requested`, `when_requested`, `reason`, `applicant_response_time`, `status_changes`, `reminder_history`, `escalation_log`
   - Support bulk custom document requests for multiple applicants with CSV import functionality and validation
   - Implement approval workflow for custom document requests to prevent unauthorized requests

4. **Document Verification & Status Management**
   - Implement document lifecycle with states: `Pending`, `Under_Review`, `Approved`, `Rejected`, `Requires_Revision`, `Expired`, `Superseded`, `Archived`
   - Admin dashboard with document queue management, advanced filtering (by status, date, applicant, document type), and bulk action capabilities
   - Mandatory fields for status changes: `reviewer_id`, `review_date`, `status_reason`, `internal_notes`, `next_action_required`, `estimated_resolution_time`
   - Document comparison tools for detecting duplicates and inconsistencies using file hash comparison
   - Integration with existing email notification system for automated status updates with customizable templates
   - Configurable auto-approval rules for standard document types with risk assessment scoring
   - Document annotation system for reviewers to mark specific areas requiring attention

5. **Document Vault System with Cross-Application Reusability**
   - Implement secure document storage with user-specific vaults that persist across multiple applications using Supabase storage
   - Document categorization with intelligent tagging: `document_category`, `upload_date`, `associated_applications`, `verification_status`, `tags`, `access_permissions`, `sharing_settings`
   - Version control system: maintain document history, track updates, allow rollback to previous versions with change logs
   - Cross-application document sharing with granular permission controls (view/download/share/edit)
   - Advanced document search and filtering capabilities within user vault with full-text search
   - Automatic duplicate detection and consolidation with user confirmation and merge capabilities
   - Document expiry tracking with automated renewal reminders and replacement workflows

6. **Document Expiry Management System**
   - Support configurable expiry dates for document types (passports: 10 years, certificates: varies by issuing authority, medical reports: 6 months)
   - Automated expiry detection with color-coded dashboard indicators (green: >90 days, yellow: 30-90 days, red: <30 days, expired: gray)
   - Prevention logic: automatically block expired documents from new application submissions with clear error messages
   - Proactive notification system: alerts at 90 days, 30 days, 7 days, and day of expiry with escalating urgency
   - Bulk expiry reporting dashboard for administrative oversight with export capabilities (CSV/PDF)
   - Integration with document renewal workflow triggers and automatic case flagging for urgent renewals

7. **Automated Document Reminder & Escalation System**
   - Admin-configurable reminder schedules per document type and immigration package with business rule engine
   - Multi-channel notifications: email (primary), in-app notifications, SMS (if configured), and dashboard alerts
   - Escalation matrix with configurable timeframes: initial reminder (Day 1) → follow-up reminder (Day 3) → supervisor notification (Day 7) → case flagging for manual intervention (Day 10)
   - Dashboard analytics: submission rates, average response times, bottleneck identification, completion metrics, reminder effectiveness tracking
   - Customizable reminder templates with merge fields for personalization (applicant name, document type, deadline, case reference)
   - Integration with existing winston logging system for comprehensive audit trails and compliance reporting
   - Smart reminder frequency adjustment based on applicant response patterns and historical data

**Technical Implementation Constraints:**

- **Non-Destructive Architecture**: Extend existing unified payment system using polymorphic patterns, feature flags, and separate NestJS modules without modifying existing payment tables or services
- **Database Design**: Use JSONB metadata fields for workflow configuration while maintaining all existing table structures unchanged, implement new tables with foreign key relationships to existing payment records
- **Code Patterns**: Implement `AbstractImmigrationService<T>` and `BaseImmigrationController<T>` following established abstract base class patterns from unified payment system
- **Backward Compatibility**: All existing payment flows must remain 100% unchanged and functional with comprehensive regression testing
- **Modular NestJS Structure**: Follow `src/[feature]/[feature].module.ts` organization with proper dependency injection, guards, and interceptors
- **Error Handling**: Implement comprehensive error handling with user-friendly messages, detailed structured logging, and graceful degradation
- **Security**: Ensure document encryption at rest using AES-256, secure file upload validation with virus scanning, role-based access controls (RBAC), and audit logging for all document operations
- **Performance**: Implement caching strategies for workflow configurations, document metadata, and frequently accessed data using Redis
- **Scalability**: Design for horizontal scaling with stateless services and database connection pooling

**Quality Assurance Requirements:**

- **Test Coverage**: Minimum 95% code coverage with unit tests (Jest), integration tests (Supertest), and end-to-end tests (Cypress)
- **Test Organization**: Feature-based test structure (`test/immigration/`, `test/workflow/`, `test/documents/`) with proper mocking using jest.fn() and @InjectRepository() stubs
- **Performance Requirements**: Document upload/processing under 30 seconds, workflow step transitions under 2 seconds, dashboard loading under 3 seconds
- **Logging**: Structured logging with correlation IDs for request tracing across all workflow steps using winston with JSON format
- **Documentation**: Comprehensive API documentation (OpenAPI/Swagger), admin user guides, developer setup instructions, and troubleshooting guides
- **Security Testing**: Penetration testing for file upload vulnerabilities, access control validation, and data encryption verification

**Expected Deliverables with Specific Outputs:**

1. **Architecture Design Document**: 15+ page comprehensive document with system diagrams (Mermaid), data flow charts, integration points, and security architecture
2. **Database Schema Extensions**: Non-destructive Prisma migrations with complete rollback capabilities, migration scripts, and data seeding for development
3. **Service Layer Implementation**: Abstract base classes, factory patterns, concrete service implementations, and dependency injection configuration
4. **Admin Panel API**: Complete RESTful API endpoints for workflow configuration, document management, reporting, and user management
5. **Email Integration**: Template-based notification system with configurable triggers, merge field support, and provider abstraction (SendGrid/AWS SES)
6. **Comprehensive Test Suite**: Full test coverage with realistic mock data, CI/CD pipeline integration (GitHub Actions), and automated testing reports
7. **API Documentation**: Complete OpenAPI/Swagger documentation with example requests, responses, error codes, and authentication requirements
8. **Production Migration Guide**: Step-by-step deployment procedures, rollback strategies, monitoring setup, and performance benchmarks

**Success Criteria:**
- Zero disruption to existing payment flows during implementation and after deployment (verified through automated regression tests)
- Admin users can configure complete immigration workflows in under 10 minutes with intuitive drag-and-drop interface
- Applicants can complete document submission workflows with clear step-by-step guidance and progress indicators
- Document processing time reduced by 40% through automation and streamlined workflows (measured against current baseline)
- 100% audit trail compliance for all document operations and workflow state changes with immutable logs
- System handles 100+ concurrent users without performance degradation (load tested)
- All existing unified payment system functionality remains intact and operational (verified through comprehensive regression testing)
- Document upload success rate >99% with proper error handling and retry mechanisms
- Email notification delivery rate >95% with fallback mechanisms for failed deliveries

**Implementation Priority with Specific Milestones:**

### Phase 1: Foundation & Database Architecture (Week 1)
1. **Database schema extensions and migrations**
   - Design polymorphic schema with JSONB metadata
   - Create migration scripts with rollback capabilities
   - Implement data seeding for development environment
   - Add new enums for immigration workflow states
   - Create indexes for performance optimization

### Phase 2: Core Service Architecture (Week 2-3)
2. **Core workflow engine and service layer**
   - Implement AbstractImmigrationService and BaseImmigrationController
   - Create workflow state machine with event-driven transitions
   - Develop factory patterns for service instantiation
   - Integrate with existing unified payment system
   - Implement repository pattern with generic interfaces

### Phase 3: Document Management System (Week 4-5)
3. **Document management system**
   - Implement secure file upload with validation and virus scanning
   - Create document vault with version control using Supabase storage
   - Develop expiry management and automated reminders
   - Build document categorization and tagging system
   - Implement cross-application document sharing

### Phase 4: Admin Configuration Interface (Week 6)
4. **Admin configuration interface**
   - Build drag-and-drop workflow configuration UI
   - Implement document requirement management interface
   - Create reporting and analytics dashboard
   - Develop bulk operations for document requests
   - Build approval workflow for custom document requests

### Phase 5: Notification & Communication System (Week 7)
5. **Email notifications and reminders**
   - Integrate with existing email system (Resend)
   - Implement template-based notifications with merge fields
   - Create escalation and reminder scheduling system
   - Build multi-channel notification support (email, in-app, SMS)
   - Implement smart reminder frequency adjustment

### Phase 6: Quality Assurance & Documentation (Week 8)
6. **Testing and documentation**
   - Achieve 95% test coverage with comprehensive test suites
   - Complete API documentation with OpenAPI/Swagger
   - Create user guides and troubleshooting documentation
   - Implement performance testing and load testing
   - Create migration guides and rollback procedures

### Phase 7: Production Deployment & Monitoring (Week 9)
7. **Production deployment and monitoring**
   - Deploy with feature flags for gradual rollout
   - Implement monitoring and alerting with structured logging
   - Conduct performance testing and optimization
   - Set up automated backup and disaster recovery
   - Create production support documentation

**Additional Requirements Based on Conversation History:**
- Follow Extreme Programming (XP) methodology with test-first development and continuous integration
- Implement proper memory management for AI interactions using mem0ai patterns
- Use Model Context Protocol (MCP) for any AI-enhanced document processing features
- Maintain GDPR compliance for all document storage and processing
- Implement proper logging with winston using structured JSON format with date-based log files
- Follow the established pattern of non-destructive schema extensions with feature flags
- Ensure all new endpoints follow the strict payload validation patterns established in unified payment system
- Implement intelligent session validation logic to prevent duplicate workflow initiations
- Use polymorphic database relationships with discriminator columns as established in previous implementations