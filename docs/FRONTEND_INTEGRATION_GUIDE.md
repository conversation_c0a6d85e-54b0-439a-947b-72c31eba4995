# Frontend Integration Guide - Career Ireland Payment System

## 🚀 Overview

This guide provides comprehensive documentation for integrating the new unified payment system with the Career Ireland frontend application. The system has been completely refactored to support a service-agnostic architecture with enhanced payment flows.

## 📋 Table of Contents

1. [API Changes Summary](#api-changes-summary)
2. [Payment Flow Integration](#payment-flow-integration)
3. [Service-Agnostic Architecture](#service-agnostic-architecture)
4. [Authentication & Security](#authentication--security)
5. [Error Handling](#error-handling)
6. [Testing & Validation](#testing--validation)
7. [Migration Timeline](#migration-timeline)

## 🔄 API Changes Summary

### New Unified Payment Endpoints (`/v2/payment/*`)

All new payment functionality is available under the `/v2/payment/` prefix:

```typescript
// Base URL: http://localhost:4242/v2/payment

// Core Payment Endpoints
POST   /v2/payment/create     // Authenticated user payments
POST   /v2/payment/guest      // Guest user payments
POST   /v2/payment/webhook    // Stripe webhook handler
GET    /v2/payment/history    // Payment history with pagination
GET    /v2/payment/analytics  // Payment analytics
PATCH  /v2/payment/progress   // Admin: Update payment progress
GET    /v2/payment/health     // Service health check
```

### Legacy Endpoints (Deprecated)

The following legacy endpoints are **deprecated** and will be removed in **Q2 2025**:

```typescript
// ⚠️ DEPRECATED - Use /v2/payment/* instead
POST /payment/mentor-service
POST /payment/guest-service
POST /payment/package
POST /payment/guest-package
POST /payment/immigration-service
POST /payment/guest-immigration
POST /payment/training
POST /payment/guest-training
```

### Request/Response Schemas

#### Authenticated User Payment Request

```typescript
interface CreatePaymentRequest {
  serviceType: 'Service' | 'Package' | 'Immigration' | 'Training';
  serviceId: string;
  paymentType: 'user'; // Forced by endpoint
}

// Example Request
POST /v2/payment/create
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "serviceType": "Service",
  "serviceId": "service-123"
}
```

#### Guest User Payment Request

```typescript
interface CreateGuestPaymentRequest {
  serviceType: 'Service' | 'Package' | 'Immigration' | 'Training';
  serviceId: string;
  paymentType: 'guest'; // Forced by endpoint
  name: string;
  email: string;
  mobile: string;
}

// Example Request
POST /v2/payment/guest
Content-Type: application/json

{
  "serviceType": "Immigration",
  "serviceId": "immigration-456",
  "name": "John Doe",
  "email": "<EMAIL>",
  "mobile": "+353871234567"
}
```

#### Payment Response

```typescript
interface PaymentResponse {
  success: boolean;
  message: string;
  data: {
    paymentId: string;
    stripeSessionId: string;
    stripeSessionUrl: string;
    amount: number;
    currency: string;
    serviceType: string;
    serviceId: string;
    paymentType: 'user' | 'guest';
    status: 'pending' | 'paid' | 'failed';
    expiresAt: string; // ISO date
  };
}
```

## 💳 Payment Flow Integration

### 8-Step Customer Workflow

The new system implements a comprehensive 8-step workflow:

```mermaid
graph TD
    A[1. Plan Selection] --> B[2. Payment Processing]
    B --> C[3. User Login/Registration]
    C --> D[4. Application ID Generation]
    D --> E[5. Document Upload]
    E --> F[6. Admin Workflow Processing]
    F --> G[7. Status Tracking]
    G --> H[8. Email Notifications]
```

### Frontend Implementation Examples

#### Step 1-2: Plan Selection & Payment

```typescript
// React Component Example
import { useState } from 'react';
import { useAuth } from './hooks/useAuth';

interface PaymentFormProps {
  serviceType: 'Service' | 'Package' | 'Immigration' | 'Training';
  serviceId: string;
}

const PaymentForm: React.FC<PaymentFormProps> = ({ serviceType, serviceId }) => {
  const { user, token } = useAuth();
  const [loading, setLoading] = useState(false);
  const [guestInfo, setGuestInfo] = useState({
    name: '',
    email: '',
    mobile: ''
  });

  const handlePayment = async () => {
    setLoading(true);
    
    try {
      const endpoint = user ? '/v2/payment/create' : '/v2/payment/guest';
      const payload = user 
        ? { serviceType, serviceId }
        : { serviceType, serviceId, ...guestInfo };

      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(user && { 'Authorization': `Bearer ${token}` })
        },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      
      if (result.success) {
        // Redirect to Stripe Checkout
        window.location.href = result.data.stripeSessionUrl;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('Payment failed:', error);
      // Handle error (show toast, etc.)
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="payment-form">
      {!user && (
        <div className="guest-info">
          <input
            type="text"
            placeholder="Full Name"
            value={guestInfo.name}
            onChange={(e) => setGuestInfo({...guestInfo, name: e.target.value})}
            required
          />
          <input
            type="email"
            placeholder="Email Address"
            value={guestInfo.email}
            onChange={(e) => setGuestInfo({...guestInfo, email: e.target.value})}
            required
          />
          <input
            type="tel"
            placeholder="Mobile Number"
            value={guestInfo.mobile}
            onChange={(e) => setGuestInfo({...guestInfo, mobile: e.target.value})}
            required
          />
        </div>
      )}
      
      <button 
        onClick={handlePayment} 
        disabled={loading}
        className="payment-button"
      >
        {loading ? 'Processing...' : 'Proceed to Payment'}
      </button>
    </div>
  );
};
```

#### Step 3: Payment Success Handling

```typescript
// Payment Success Page Component
const PaymentSuccessPage: React.FC = () => {
  const { searchParams } = useSearchParams();
  const sessionId = searchParams.get('session_id');
  
  useEffect(() => {
    if (sessionId) {
      // Payment is automatically processed by webhook
      // Application ID is generated automatically
      // Redirect to dashboard or next step
      router.push('/dashboard');
    }
  }, [sessionId]);

  return (
    <div className="payment-success">
      <h1>Payment Successful!</h1>
      <p>Your application is being processed...</p>
      <p>You will receive an email with your application ID shortly.</p>
    </div>
  );
};
```

#### Step 4-7: Payment History & Status Tracking

```typescript
// Payment History Component
const PaymentHistory: React.FC = () => {
  const [payments, setPayments] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0
  });

  const fetchPayments = async (page = 1) => {
    try {
      const response = await fetch(
        `/v2/payment/history?page=${page}&limit=${pagination.limit}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      
      const result = await response.json();
      setPayments(result.data.payments);
      setPagination(result.data.pagination);
    } catch (error) {
      console.error('Failed to fetch payment history:', error);
    }
  };

  return (
    <div className="payment-history">
      {payments.map((payment) => (
        <div key={payment.id} className="payment-item">
          <div className="payment-info">
            <h3>{payment.service_type} - {payment.service_name}</h3>
            <p>Amount: €{(payment.amount / 100).toFixed(2)}</p>
            <p>Status: <span className={`status-${payment.status}`}>
              {payment.status.toUpperCase()}
            </span></p>
            <p>Date: {new Date(payment.created_at).toLocaleDateString()}</p>
          </div>
          
          {payment.application_id && (
            <div className="application-link">
              <a href={`/applications/${payment.application_id}`}>
                View Application
              </a>
            </div>
          )}
        </div>
      ))}
      
      <Pagination 
        current={pagination.page}
        total={pagination.total}
        pageSize={pagination.limit}
        onChange={fetchPayments}
      />
    </div>
  );
};
```

## 🏗️ Service-Agnostic Architecture Changes

### Unified Service Type System

The new architecture supports four main service types:

```typescript
type ServiceType = 'Service' | 'Package' | 'Immigration' | 'Training';

interface ServiceConfig {
  type: ServiceType;
  tableName: string;
  idField: string;
  emailTemplate: string;
  includesMentor: boolean;
}

const SERVICE_CONFIGS: Record<ServiceType, ServiceConfig> = {
  Service: {
    type: 'Service',
    tableName: 'service',
    idField: 'serviceId',
    emailTemplate: 'service-payment-success',
    includesMentor: true
  },
  Package: {
    type: 'Package',
    tableName: 'package',
    idField: 'packageId',
    emailTemplate: 'package-payment-success',
    includesMentor: false
  },
  Immigration: {
    type: 'Immigration',
    tableName: 'immigration_service',
    idField: 'immigration_serviceId',
    emailTemplate: 'immigration-payment-success',
    includesMentor: false
  },
  Training: {
    type: 'Training',
    tableName: 'training',
    idField: 'trainingId',
    emailTemplate: 'training-payment-success',
    includesMentor: false
  }
};
```

### TypeScript Interfaces

```typescript
// Core Payment Types
interface PaymentData {
  id: string;
  amount: number;
  status: 'pending' | 'paid' | 'failed';
  payment_type: 'user' | 'guest';
  service_type: ServiceType;
  service_id: string;
  stripe_session_id: string;
  stripe_payment_intent_id?: string;
  payment_method?: string;
  transaction_id?: string;
  created_at: string;
  updated_at: string;
  
  // User payments
  userId?: string;
  
  // Guest payments
  guest_name?: string;
  guest_email?: string;
  guest_mobile?: string;
  
  // Service relationships
  service?: ServiceData;
  package?: PackageData;
  immigration_service?: ImmigrationData;
  training?: TrainingData;
  
  // Application integration
  application_id?: string;
}

// Service Data Types
interface ServiceData {
  id: string;
  name: string;
  price: number;
  description: string;
  mentorId?: string;
  mentor?: {
    id: string;
    name: string;
    email: string;
  };
}

interface PackageData {
  id: string;
  name: string;
  price: number;
  description: string;
  features: string[];
}

interface ImmigrationData {
  id: string;
  name: string;
  price: number;
  description: string;
  processing_time: string;
  requirements: string[];
}

interface TrainingData {
  id: string;
  name: string;
  price: number;
  description: string;
  duration: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
}
```

## 🔐 Authentication & Security

### JWT Token Handling

```typescript
// Auth Hook Example
const useAuth = () => {
  const [token, setToken] = useState<string | null>(
    localStorage.getItem('auth_token')
  );
  
  const login = async (credentials: LoginCredentials) => {
    const response = await fetch('/user/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    });
    
    const result = await response.json();
    if (result.success) {
      setToken(result.data.token);
      localStorage.setItem('auth_token', result.data.token);
    }
  };
  
  const logout = () => {
    setToken(null);
    localStorage.removeItem('auth_token');
  };
  
  return { token, login, logout };
};
```

### API Request Interceptor

```typescript
// Axios Interceptor Example
import axios from 'axios';

const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:4242'
});

apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired, redirect to login
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

## ⚠️ Error Handling

### Error Response Format

```typescript
interface ErrorResponse {
  success: false;
  message: string;
  error?: {
    code: string;
    details?: any;
  };
}

// Common Error Codes
const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  SERVICE_NOT_FOUND: 'SERVICE_NOT_FOUND',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  UNAUTHORIZED: 'UNAUTHORIZED',
  RATE_LIMITED: 'RATE_LIMITED'
} as const;
```

### Error Handling Patterns

```typescript
// Error Handler Hook
const useErrorHandler = () => {
  const showError = (error: any) => {
    let message = 'An unexpected error occurred';
    
    if (error.response?.data?.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    
    // Show toast notification
    toast.error(message);
  };
  
  return { showError };
};

// Usage in Components
const PaymentComponent = () => {
  const { showError } = useErrorHandler();
  
  const handlePayment = async () => {
    try {
      // Payment logic
    } catch (error) {
      showError(error);
    }
  };
};
```

## 🧪 Testing & Validation

### Frontend Testing Requirements

1. **Unit Tests**: Test payment form validation and state management
2. **Integration Tests**: Test API integration with mock responses
3. **E2E Tests**: Test complete payment flows

### Mock Data for Development

```typescript
// Mock Payment Response
export const mockPaymentResponse: PaymentResponse = {
  success: true,
  message: 'Payment session created successfully',
  data: {
    paymentId: 'pay_test_123',
    stripeSessionId: 'cs_test_123',
    stripeSessionUrl: 'https://checkout.stripe.com/pay/cs_test_123',
    amount: 20000,
    currency: 'eur',
    serviceType: 'Service',
    serviceId: 'service-123',
    paymentType: 'user',
    status: 'pending',
    expiresAt: '2025-06-05T12:00:00.000Z'
  }
};

// Mock Service Data
export const mockServices = [
  {
    id: 'service-1',
    name: 'CV Review Service',
    price: 5000, // €50.00
    description: 'Professional CV review and feedback',
    mentorId: 'mentor-1'
  },
  {
    id: 'package-1',
    name: 'Career Starter Package',
    price: 15000, // €150.00
    description: 'Complete career guidance package'
  }
];
```

### Test Scenarios

```typescript
// Jest Test Example
describe('Payment Integration', () => {
  test('should create user payment successfully', async () => {
    const mockResponse = mockPaymentResponse;
    
    jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    } as Response);
    
    const result = await createPayment({
      serviceType: 'Service',
      serviceId: 'service-123'
    });
    
    expect(result.success).toBe(true);
    expect(result.data.paymentId).toBeDefined();
  });
  
  test('should handle payment validation errors', async () => {
    const errorResponse = {
      success: false,
      message: 'Service not found'
    };
    
    jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: false,
      json: () => Promise.resolve(errorResponse)
    } as Response);
    
    await expect(createPayment({
      serviceType: 'Service',
      serviceId: 'invalid-id'
    })).rejects.toThrow('Service not found');
  });
});
```

## 📅 Migration Timeline

### Phase 1: Immediate (Current)
- ✅ New `/v2/payment/*` endpoints are live
- ✅ Legacy endpoints still functional
- ✅ Comprehensive testing completed

### Phase 2: Q1 2025
- 🔄 Frontend migration to new endpoints
- 🔄 User acceptance testing
- 🔄 Performance optimization

### Phase 3: Q2 2025
- ⚠️ Legacy endpoint deprecation warnings
- 🗑️ Legacy endpoint removal
- 🎯 Full migration completion

### Migration Checklist

- [ ] Update API endpoints from `/payment/*` to `/v2/payment/*`
- [ ] Implement new request/response schemas
- [ ] Add proper error handling for new error formats
- [ ] Update authentication headers
- [ ] Test guest and authenticated user flows
- [ ] Implement payment history pagination
- [ ] Add service-agnostic type handling
- [ ] Update TypeScript interfaces
- [ ] Add comprehensive error boundaries
- [ ] Test webhook integration (payment success/failure)

## 🚀 Getting Started

1. **Update API Base URL**: Change from `/payment` to `/v2/payment`
2. **Install Dependencies**: Ensure you have the latest TypeScript definitions
3. **Update Authentication**: Use Bearer token authentication
4. **Test Integration**: Start with the health endpoint: `GET /v2/payment/health`
5. **Implement Error Handling**: Use the new error response format
6. **Test Payment Flows**: Test both guest and authenticated user payments

## 📞 Support

For technical support during integration:
- **API Documentation**: Available at `/api/docs` when the server is running
- **Test Environment**: Use the development server for testing
- **Error Logs**: Check browser console and network tab for detailed error information

## 🔧 Advanced Integration Examples

### Stripe Integration with React

```typescript
// Stripe Payment Component
import { loadStripe } from '@stripe/stripe-js';

const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY!);

const StripePaymentButton: React.FC<{
  serviceType: ServiceType;
  serviceId: string;
  amount: number;
}> = ({ serviceType, serviceId, amount }) => {
  const handlePayment = async () => {
    const stripe = await stripePromise;

    // Create payment session
    const response = await fetch('/v2/payment/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ serviceType, serviceId })
    });

    const session = await response.json();

    if (session.success) {
      // Redirect to Stripe Checkout
      const result = await stripe?.redirectToCheckout({
        sessionId: session.data.stripeSessionId
      });

      if (result?.error) {
        console.error('Stripe error:', result.error.message);
      }
    }
  };

  return (
    <button
      onClick={handlePayment}
      className="stripe-payment-button"
    >
      Pay €{(amount / 100).toFixed(2)}
    </button>
  );
};
```

### Real-time Payment Status Updates

```typescript
// WebSocket integration for real-time updates
const usePaymentStatus = (paymentId: string) => {
  const [status, setStatus] = useState<string>('pending');

  useEffect(() => {
    const ws = new WebSocket(`ws://localhost:4242/payment-status/${paymentId}`);

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setStatus(data.status);

      if (data.status === 'paid') {
        // Payment successful, redirect or update UI
        toast.success('Payment successful!');
      } else if (data.status === 'failed') {
        toast.error('Payment failed. Please try again.');
      }
    };

    return () => ws.close();
  }, [paymentId]);

  return status;
};
```

### Payment Analytics Dashboard

```typescript
// Analytics Component
const PaymentAnalytics: React.FC = () => {
  const [analytics, setAnalytics] = useState(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        const response = await fetch('/v2/payment/analytics', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const result = await response.json();
        setAnalytics(result.data);
      } catch (error) {
        console.error('Failed to fetch analytics:', error);
      }
    };

    fetchAnalytics();
  }, []);

  if (!analytics) return <div>Loading...</div>;

  return (
    <div className="payment-analytics">
      <div className="metric-card">
        <h3>Total Revenue</h3>
        <p>€{(analytics.totalRevenue / 100).toFixed(2)}</p>
      </div>

      <div className="metric-card">
        <h3>Total Payments</h3>
        <p>{analytics.totalPayments}</p>
      </div>

      <div className="metric-card">
        <h3>Success Rate</h3>
        <p>{((analytics.successfulPayments / analytics.totalPayments) * 100).toFixed(1)}%</p>
      </div>

      <div className="service-breakdown">
        <h3>Revenue by Service Type</h3>
        {Object.entries(analytics.revenueByServiceType).map(([type, revenue]) => (
          <div key={type} className="service-metric">
            <span>{type}</span>
            <span>€{(revenue / 100).toFixed(2)}</span>
          </div>
        ))}
      </div>
    </div>
  );
};
```

## 🎯 Performance Optimization

### Caching Strategies

```typescript
// React Query integration for caching
import { useQuery } from 'react-query';

const usePaymentHistory = (page: number = 1) => {
  return useQuery(
    ['paymentHistory', page],
    () => fetchPaymentHistory(page),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      keepPreviousData: true
    }
  );
};

// Service data caching
const useServiceData = (serviceType: ServiceType, serviceId: string) => {
  return useQuery(
    ['service', serviceType, serviceId],
    () => fetchServiceData(serviceType, serviceId),
    {
      staleTime: 30 * 60 * 1000, // 30 minutes
      cacheTime: 60 * 60 * 1000  // 1 hour
    }
  );
};
```

### Lazy Loading and Code Splitting

```typescript
// Lazy load payment components
const PaymentForm = lazy(() => import('./components/PaymentForm'));
const PaymentHistory = lazy(() => import('./components/PaymentHistory'));
const PaymentAnalytics = lazy(() => import('./components/PaymentAnalytics'));

// Route-based code splitting
const PaymentRoutes = () => (
  <Routes>
    <Route
      path="/payment"
      element={
        <Suspense fallback={<div>Loading...</div>}>
          <PaymentForm />
        </Suspense>
      }
    />
    <Route
      path="/payment/history"
      element={
        <Suspense fallback={<div>Loading...</div>}>
          <PaymentHistory />
        </Suspense>
      }
    />
  </Routes>
);
```

## 🔍 Debugging and Troubleshooting

### Common Issues and Solutions

1. **CORS Issues**
   ```typescript
   // Ensure proper CORS configuration
   const apiClient = axios.create({
     baseURL: process.env.REACT_APP_API_URL,
     withCredentials: true,
     headers: {
       'Content-Type': 'application/json'
     }
   });
   ```

2. **Token Expiration**
   ```typescript
   // Automatic token refresh
   const refreshToken = async () => {
     try {
       const response = await fetch('/user/refresh', {
         method: 'POST',
         headers: {
           'Authorization': `Bearer ${refreshToken}`
         }
       });

       const result = await response.json();
       if (result.success) {
         localStorage.setItem('auth_token', result.data.token);
         return result.data.token;
       }
     } catch (error) {
       // Redirect to login
       window.location.href = '/login';
     }
   };
   ```

3. **Payment Session Expiration**
   ```typescript
   // Handle expired payment sessions
   const handleExpiredSession = () => {
     toast.warning('Payment session expired. Please try again.');
     // Redirect back to service selection
     router.push('/services');
   };
   ```

### Debug Mode

```typescript
// Enable debug mode for development
const DEBUG_MODE = process.env.NODE_ENV === 'development';

const debugLog = (message: string, data?: any) => {
  if (DEBUG_MODE) {
    console.log(`[Payment Debug] ${message}`, data);
  }
};

// Usage in payment functions
const createPayment = async (data: PaymentRequest) => {
  debugLog('Creating payment', data);

  try {
    const response = await apiClient.post('/v2/payment/create', data);
    debugLog('Payment response', response.data);
    return response.data;
  } catch (error) {
    debugLog('Payment error', error);
    throw error;
  }
};
```

---

**Last Updated**: June 5, 2025
**API Version**: 2.0.0
**Compatibility**: Node.js 18+, React 18+, TypeScript 4.9+
