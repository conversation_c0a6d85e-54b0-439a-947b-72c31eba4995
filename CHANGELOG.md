# Changelog

All notable changes to the Career Ireland API project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [2.0.0] - 2025-08-10

### 🚀 Major API Changes

#### **BREAKING CHANGE: Modified GET /notifications/pending/{applicationId} Endpoint**
- **Updated Response Structure**:
  - **Added**: `notification_queue_id` field to identify specific notification queue entries
  - **Removed**: `pendingCount`, `recipientEmail`, `recipientName`, `applicationId`, `documentChanges` fields
  - **Retained**: `applicationNumber`, `emailPreview` fields
- **New Response Format**:
  ```json
  {
    "notification_queue_id": "clm123abc456def789",
    "applicationNumber": "IMM-2025-000001",
    "emailPreview": {
      "subject": "Document Status Updates - Application IMM-2025-000001",
      "htmlContent": "<h2>Document Status Updates</h2>..."
    }
  }
  ```

#### **BREAKING CHANGE: notification_queue Database Schema Updates**
- **Modified Fields**:
  - `recipient_user` now stores email addresses (previously stored user IDs)
  - **Removed**: `recipient_user_id`, `recipient_email`, `is_sent` fields
  - **Removed**: User relationship foreign key constraint
- **Updated Indexes**:
  - Removed indexes for `recipient_email`, `recipient_user_id`, `is_sent`
  - Added new index for `recipient_user` field
- **Data Migration**: Existing `recipient_email` data automatically migrated to `recipient_user` field

### 🔧 Technical Improvements

#### **Service Layer Updates**
- Updated `NotificationService.getPendingNotifications()` to return simplified response structure
- Modified email generation methods to use `recipient_name` instead of user relationship
- Updated all database queries to use new field names
- Removed dependencies on user relationship for notification processing

#### **Interface and DTO Updates**
- Updated `PendingNotificationResponse` interface with new structure
- Modified `PendingNotificationResponseDto` with proper API documentation
- Maintained backward compatibility for error responses

### 📋 Migration Notes

#### **For API Consumers**
1. **Update API Integration**: Modify code expecting the old response structure
2. **New Fields**: Use `notification_queue_id` for notification identification
3. **Removed Fields**: Remove dependencies on `pendingCount`, `recipientEmail`, `recipientName`, `applicationId`, `documentChanges`

#### **For Database**
1. **Automatic Migration**: Run `npx prisma migrate dev` to apply schema changes
2. **Data Preservation**: Existing notification data is automatically migrated
3. **Index Updates**: Database indexes are automatically updated for optimal performance

#### **Breaking Changes Summary**
- **API Response Structure**: GET `/notifications/pending/{applicationId}` returns different payload
- **Database Schema**: notification_queue table structure modified
- **Field Mappings**: Email addresses now stored in `recipient_user` instead of `recipient_email`

## [1.1.0] - 2025-08-09

### 🚀 Features

#### **Enhanced Email Notification System**
- **Created Group Document Status Notification Template** (`src/template/group-document-status.tsx`)
  - Professional React TSX template for batch document status notifications
  - Follows established email template patterns with consistent styling
  - Supports multiple document status changes in a single email
  - Includes status badges, review comments, and formatted dates
  - Replaces basic HTML generation with professional template rendering

#### **Improved Notification Send Endpoint**
- **Enhanced POST /notifications/send endpoint** with notification_queue_id parameter
  - Added `SendNotificationByIdDto` for concrete notification identification
  - Enables sending specific emails using notification queue ID
  - Provides better error handling and validation
  - Maintains backward compatibility with legacy batch endpoint (`POST /notifications/send-batch`)

#### **Simplified Application Creation Response**
- **Streamlined application creation endpoint response** to return only `applicationId`
  - Updated `CreateApplicationResponseDto` to simplified structure
  - Maintains backward compatibility for existing API consumers
  - Reduces response payload size and improves performance
  - Follows RESTful API best practices for resource creation

#### **Payment Rollback Mechanism**
- **Implemented automatic payment rollback** for failed application creation
  - Uses database transactions with proper rollback handling
  - Automatically deletes payment records if application creation fails
  - Prevents duplicate payment records in manual application workflow
  - Ensures data consistency across payment and application tables
  - Comprehensive error logging for debugging and monitoring

### 🔧 Technical Improvements

#### **Email Template Integration**
- **Added GroupDocumentStatusEmail template** to EmailTemplateIntegrationService
- **Enhanced generateEmailPreview method** to use React templates instead of basic HTML
- **Added renderGroupDocumentStatusEmail method** for template rendering
- **Improved error handling** with fallback to basic HTML if template fails

#### **Database Transaction Safety**
- **Wrapped application creation** in Prisma database transactions
- **Added payment rollback logic** to prevent orphaned payment records
- **Enhanced error logging** for transaction failures and rollbacks
- **Maintained data integrity** across related database operations

### 🔄 API Changes

#### **Breaking Changes**
- **POST /applications response structure changed**:
  - **Before**: Returns full application object with all fields
  - **After**: Returns only `{ data: { applicationId: string } }`
  - **Migration**: Update API consumers to use `data.applicationId` instead of `data.id`

#### **New Endpoints**
- **POST /notifications/send** (Enhanced):
  - **Request Body**: `{ "notification_queue_id": "string" }`
  - **Purpose**: Send specific notification by queue ID
  - **Authentication**: Admin required

#### **Legacy Endpoints**
- **POST /notifications/send-batch** (Maintained for backward compatibility):
  - **Request Body**: `{ "applicationId": "string", "force": boolean }`
  - **Purpose**: Send batch notification by application ID
  - **Authentication**: Admin required

## [1.0.3] - 2025-08-09

### 🔄 Refactoring

#### **Batch Notification System Consolidation**
- **Consolidated batch notification functionality** into existing notification files for better maintainability
- **Merged separate batch-notification files** into unified notification system architecture
- **Eliminated code duplication** and improved service organization
- **Maintained all existing functionality** while simplifying the codebase structure

#### **Controller Consolidation**
- **Moved batch notification endpoints** from `BatchNotificationController` to `NotificationController`
- **Consolidated routes under `/notifications`**:
  - `GET /notifications/pending/:applicationId` - Get pending notifications with email preview
  - `POST /notifications/send` - Send batch notification for an application
- **Removed duplicate controller file** `src/application/controllers/batch-notification.controller.ts`
- **Maintained existing notification settings endpoints** without conflicts

#### **DTO and Interface Consolidation**
- **Moved all batch notification DTOs** from `batch-notification.dto.ts` to `notification.dto.ts`:
  - `DocumentStatusChangeDto` - Document status change data structure
  - `SendBatchNotificationDto` - Request DTO for sending batch notifications
  - `PendingNotificationResponseDto` - Response DTO for pending notification retrieval
  - `SendBatchNotificationResponseDto` - Response DTO for batch notification sending
  - `BatchNotificationErrorDto` - Error response DTO for batch operations
- **Updated TypeScript interfaces** to match consolidated structure
- **Removed duplicate DTO file** `src/application/dto/batch-notification.dto.ts`

#### **Service Consolidation**
- **Moved batch notification logic** from `BatchNotificationService` to existing `NotificationService`
- **Added new methods to NotificationService**:
  - `getPendingNotifications()` - Retrieve pending notifications for an application
  - `sendBatchNotification()` - Send batch notification email
  - `addDocumentStatusChange()` - Add document changes to notification queue
  - `formatChangeDate()` - Safe date formatting (maintains recent TypeError fix)
- **Removed duplicate service file** `src/application/services/batch-notification.service.ts`
- **Maintained all recent improvements** including safe date handling

### 🔧 Technical Improvements

#### **Code Organization**
- **Reduced file count** by 3 files (controller, service, DTO)
- **Improved maintainability** with unified notification system
- **Eliminated import complexity** with consolidated structure
- **Maintained TypeScript type safety** throughout refactoring

#### **Dependency Management**
- **Updated module registrations** in `ApplicationModule`
- **Removed duplicate service providers** and controller registrations
- **Fixed dependency injection** for consolidated services
- **Maintained all existing service dependencies**

#### **Database Integration**
- **Fixed Prisma query issues** with proper type casting for JSONB fields
- **Updated user field references** to use correct schema field names (`name` instead of `first_name`/`last_name`)
- **Maintained safe date handling** for `changedAt` fields stored as strings in JSONB
- **Preserved all database functionality** without schema changes

#### **Error Handling and Logging**
- **Maintained comprehensive error logging** throughout consolidated services
- **Preserved recent TypeError fixes** for date formatting
- **Updated service names in log messages** to reflect new structure
- **Kept all functions under 15 lines** as per established standards

### 🧪 Testing

#### **Test Updates**
- **Updated test descriptions** to reflect consolidated service structure
- **Maintained all existing test coverage** for batch notification functionality
- **Verified interface compatibility** with consolidated DTOs
- **Added tests for consolidated service methods**

#### **Quality Assurance**
- **Build verification**: `npm run build` - Success
- **Development server**: `npm run start:dev` - Success (all routes properly registered)
- **Unit tests**: 3/3 tests passing for consolidated notification service
- **TypeScript compilation**: No errors after consolidation

### 📋 API Functionality

#### **Endpoint Consolidation**
- **All notification endpoints** now under single `/notifications` controller:
  - `GET /notifications/settings` - Get user notification settings
  - `PUT /notifications/settings` - Update user notification settings
  - `GET /notifications/pending/:applicationId` - Get pending notifications (moved from batch)
  - `POST /notifications/send` - Send batch notification (moved from batch)
- **No breaking changes** to API endpoints or response structures
- **Maintained backward compatibility** with existing API consumers

#### **Service Integration**
- **Unified notification service** handles both individual and batch notifications
- **Consistent error handling** across all notification operations
- **Shared logging and monitoring** for all notification functionality
- **Streamlined dependency injection** with single notification service

### 🔄 Migration Guide

#### **For Developers**
1. **Import Updates**: Update any imports from batch-notification files to notification files
2. **Service Injection**: Use `NotificationService` instead of `BatchNotificationService`
3. **No API Changes**: All endpoints remain the same under `/notifications`
4. **Testing**: Run tests to verify consolidated functionality

#### **For Administrators**
1. **No Action Required**: All functionality preserved during consolidation
2. **API Endpoints**: Same endpoints available under `/notifications` controller
3. **Monitoring**: Check logs for updated service names in notification operations

### 📚 Documentation Updates

#### **File Structure**
- **Removed Files**:
  - `src/application/controllers/batch-notification.controller.ts`
  - `src/application/dto/batch-notification.dto.ts`
  - `src/application/services/batch-notification.service.ts`
- **Updated Files**:
  - `src/application/controllers/notification.controller.ts` - Added batch endpoints
  - `src/application/dto/notification.dto.ts` - Added batch DTOs
  - `src/application/services/notification.service.ts` - Added batch methods
  - `src/application/application.module.ts` - Updated registrations

#### **API Documentation**
- **Consolidated Swagger documentation** under single notification controller
- **Updated endpoint descriptions** to reflect unified structure
- **Maintained all request/response examples** and validation rules

---

### 🔄 Upgrade Instructions

1. **Automatic Consolidation**: No manual intervention required
2. **Verify Endpoints**: Test both notification settings and batch notification endpoints
3. **Check Imports**: Update any custom imports from old batch-notification files
4. **Run Tests**: Execute `npm run test` to verify all functionality

### 📈 Performance Impact
- **Reduced Memory Footprint**: Fewer service instances and controllers
- **Improved Code Maintainability**: Single source of truth for notification logic
- **Better Developer Experience**: Unified notification system with consistent patterns
- **No Performance Degradation**: All functionality preserved with optimized structure

## [1.0.2] - 2025-08-09

### 🐛 Bug Fixes

#### **Batch Notification Date Formatting Error Resolution**
- **Fixed TypeError in GET /notifications/pending/:applicationId endpoint** that prevented email preview generation
- **Root Cause**: `changedAt` field in `DocumentStatusChange` objects was being stored as string in JSONB but code expected Date object
- **Error**: `TypeError: change.changedAt.toLocaleDateString is not a function` at line 276 in `BatchNotificationService.generateEmailPreview()`
- **Solution**: Added safe date conversion method `formatChangeDate()` to handle both string and Date objects

#### **Date Handling Improvements**
- **Added `formatChangeDate()` method** in `BatchNotificationService` to safely convert dates
- **Enhanced error handling** with try-catch blocks around date operations
- **Updated TypeScript interfaces** to reflect that `changedAt` can be either `Date | string`
- **Improved data type safety** for JSONB field date handling

#### **Interface and DTO Updates**
- **Updated `DocumentStatusChange` interface** to accept `changedAt: Date | string`
- **Updated `DocumentStatusChangeDto`** to reflect flexible date type
- **Maintained backward compatibility** with existing data in database
- **Added proper TypeScript type safety** for date handling

### 🔧 Technical Improvements

#### **Error Handling**
- **Safe date conversion** with fallback to "Unknown date" for invalid dates
- **Comprehensive error logging** for date formatting failures
- **Graceful degradation** when date parsing fails
- **Try-catch blocks** around all date operations

#### **Code Quality**
- **Function kept under 15 lines** as per established standards
- **Proper error logging** to files for debugging
- **TypeScript type safety** maintained throughout
- **Clean code patterns** following existing batch notification system

#### **Data Storage Compatibility**
- **Handles existing database data** where dates may be stored as strings
- **Works with new data** where dates are stored as Date objects
- **No data migration required** - backward compatible solution
- **JSONB field handling** optimized for mixed data types

### 🧪 Testing

#### **Test Coverage**
- **Added unit test** for date handling with both Date and string types
- **Verified interface compatibility** with mixed date types
- **Tested error scenarios** with invalid date formats
- **Confirmed backward compatibility** with existing notification data

#### **Quality Assurance**
- **Build verification**: `npm run build` - Success
- **Development server**: `npm run start:dev` - Success
- **Unit tests**: 3/3 tests passing
- **TypeScript compilation**: No errors

### 📋 API Functionality

#### **GET /notifications/pending/:applicationId**
- **Fixed email preview generation** with proper date formatting
- **Handles mixed date formats** in existing database records
- **Returns properly formatted dates** in email preview HTML
- **Maintains API response structure** without breaking changes

#### **Email Preview Enhancement**
- **Consistent date formatting** using `toLocaleDateString()`
- **Error resilience** for malformed dates
- **Professional email formatting** with proper date display
- **User-friendly date presentation** in notification emails

### 🔄 Migration Guide

#### **For Developers**
1. **No Code Changes Required**: Existing code continues to work
2. **Date Handling**: New date formatting method handles both string and Date objects automatically
3. **Testing**: Run tests to verify date handling: `npm run test:file test/application/batch-notification.service.spec.ts`

#### **For Administrators**
1. **No Action Required**: Fix is backward compatible with existing data
2. **Email Previews**: Now work correctly with all notification queue entries
3. **Date Display**: Consistent formatting across all batch notification emails

### 📚 Documentation Updates

#### **Interface Documentation**
- **Updated `DocumentStatusChange` interface** to document flexible date type
- **Added JSDoc comments** for new `formatChangeDate()` method
- **Error handling documentation** for date conversion failures

#### **API Documentation**
- **Confirmed GET endpoint functionality** with proper date handling
- **Updated error handling documentation** for date formatting
- **Maintained API response examples** with corrected date formatting

---

### 🔄 Upgrade Instructions

1. **Automatic Fix**: No manual intervention required
2. **Verify Functionality**: Test GET /notifications/pending/:applicationId endpoint
3. **Check Email Previews**: Ensure dates display correctly in notification previews
4. **Run Tests**: Execute `npm run test` to verify all functionality

### 📈 Performance Impact
- **Minimal Performance Impact**: Simple date conversion with caching
- **Improved Reliability**: Eliminates TypeError crashes in email preview generation
- **Better User Experience**: Consistent date formatting in all notifications

## [1.0.1] - 2025-08-09

### 🐛 Bug Fixes

#### **PostgreSQL Enum Validation Error Resolution**
- **Fixed ApplicationStatus enum validation error** that prevented application creation
- **Root Cause**: "Pending" status was missing from the database ApplicationStatus enum but was being used in application creation code
- **Solution**: Added "Pending" back to the ApplicationStatus enum in both Prisma schema and database

#### **Database Schema Updates**
- **Added "Pending" to ApplicationStatus enum** in `prisma/schema/schema.prisma`
- **Created and applied migration** `20250809191429_add_pending_to_application_status` to update PostgreSQL enum
- **Maintained backward compatibility** with existing enum values and order

#### **Application Creation Process**
- **Restored "Pending" status** for new applications created via:
  - Payment webhook (`createApplicationFromPayment`) - uses `ApplicationStatus.Pending`
  - Direct POST /applications endpoint (`createNewApplication`) - uses `ApplicationStatus.Pending`
- **Fixed enum validation** that was causing "invalid input value for enum" PostgreSQL errors

#### **Code Quality and Testing**
- **Added comprehensive unit tests** for ApplicationStatus enum validation
- **Verified enum functionality** with `test/application/application-status-enum.spec.ts`
- **Updated existing tests** to use correct "Pending" status expectations
- **Maintained API response structure** with minimal data containing application ID

### 🔧 Technical Improvements

#### **Error Handling**
- **Resolved PostgreSQL constraint violations** when creating applications
- **Improved error logging** for enum validation issues
- **Maintained graceful error handling** for invalid status values

#### **Database Migration**
- **Safe enum modification** using Prisma migrate dev
- **Preserved existing data** during enum update
- **Added proper indexing** for performance optimization

#### **Testing Coverage**
- **4 new unit tests** for ApplicationStatus enum validation
- **Verified all enum values** are properly defined and accessible
- **Tested application creation** with Pending status
- **Confirmed TypeScript compilation** with updated enum

### 📋 Migration Guide

#### **For Developers**
1. **Database Update**: Migration automatically applied with `npx prisma migrate dev`
2. **No Code Changes Required**: Existing code using `ApplicationStatus.Pending` now works correctly
3. **Testing**: Run tests to verify enum functionality: `npm run test:file test/application/application-status-enum.spec.ts`

#### **For Administrators**
1. **No Action Required**: Applications can now be created successfully with "Pending" status
2. **Monitoring**: Check application creation logs to verify no more enum validation errors
3. **Verification**: New applications should appear with "Pending" status in the database

### 🧪 Testing

#### **Test Coverage**
- **Unit Tests**: Added comprehensive ApplicationStatus enum validation tests
- **Integration Tests**: Verified application creation process works correctly
- **Build Verification**: Confirmed TypeScript compilation succeeds
- **Migration Testing**: Verified database migration applies successfully

#### **Quality Assurance**
- **No TypeScript Errors**: Clean compilation with updated enum
- **Database Integrity**: Enum values properly synchronized between schema and database
- **API Functionality**: Application creation endpoints work without errors

### 📚 Documentation Updates

#### **Database Schema**
- **Updated Prisma schema** to include "Pending" in ApplicationStatus enum
- **Migration documentation** for enum modification process
- **Enum value reference** updated with complete list of valid statuses

#### **API Documentation**
- **Confirmed API endpoints** work with "Pending" status
- **Updated validation rules** to include "Pending" as valid input
- **Error handling documentation** for enum validation

---

### 🔄 Upgrade Instructions

1. **Automatic Migration**: Run `npx prisma migrate dev` (already applied)
2. **Verify Application Creation**: Test creating new applications via API
3. **Check Logs**: Ensure no more PostgreSQL enum validation errors
4. **Run Tests**: Execute `npm run test` to verify all functionality

### 📈 Performance Impact
- **No Performance Degradation**: Enum modification has minimal impact
- **Improved Reliability**: Eliminates application creation failures
- **Better User Experience**: Applications can be created without errors

## [1.0.0] - 2025-08-09

### 🚀 Major Features

#### **Batch Notification System Implementation**
- **BREAKING CHANGE**: Replaced automatic email sending with manual batch notification system
- **New Architecture**: Document status changes are now grouped by application_id and sent manually by admin/agents
- **Enhanced Control**: Administrators can preview and manually send notification emails instead of automatic sending

### 📊 Database Schema Changes

#### **notification_queue Table Updates**
- **Removed Columns**:
  - `document_id`, `step_id`, `reference_type`, `reference_id`, `priority`
  - `error_code`, `opened_at`, `clicked_at`, `tracking_id`, `metadata`, `merge_data`, `template_id`
- **Added Columns**:
  - `document_changes` (JSONB) - Array of document status changes
  - `is_sent` (Boolean) - Tracks if notification has been sent
  - `sent_at` (Timestamp) - When notification was sent
- **New Indexes**: Added performance indexes for `application_id` and `is_sent` fields

#### **notification_template Table Removal**
- **BREAKING CHANGE**: Completely removed `notification_template` table
- **Migration**: All template-based functionality replaced with dynamic email generation
- **Cleanup**: Removed all related code, migrations, and references

### 🔧 API Changes

#### **New Endpoints**

##### **GET /notifications/pending/:applicationId**
- **Purpose**: Retrieve pending batch notifications for an application
- **Response**: Returns notification data with email preview
- **Example Response**:
```json
{
  "applicationId": "clm123abc456def789",
  "hasNotifications": true,
  "notificationData": {
    "id": "notification-queue-id",
    "recipientEmail": "<EMAIL>",
    "recipientName": "John Doe",
    "documentChanges": [
      {
        "documentId": "doc-123",
        "documentName": "Passport Copy",
        "previousStatus": "PENDING",
        "currentStatus": "APPROVED",
        "reason": "Document approved",
        "reviewComments": "Clear and valid document",
        "changedAt": "2025-08-09T10:30:00.000Z",
        "changedBy": "<EMAIL>"
      }
    ],
    "createdAt": "2025-08-09T10:00:00.000Z",
    "updatedAt": "2025-08-09T10:30:00.000Z"
  },
  "emailPreview": {
    "subject": "Document Status Updates - Application APP-2025-001",
    "htmlContent": "<h2>Document Status Updates</h2>..."
  }
}
```

##### **POST /notifications/send**
- **Purpose**: Manually send batch notification email
- **Request Body**:
```json
{
  "notification_queue_id": "clm123abc456def789"
}
```
- **Response**:
```json
{
  "success": true,
  "message": "Batch notification sent successfully",
  "sentAt": "2025-08-09T10:45:00.000Z"
}
```
- **Error Handling**: Returns user-friendly errors for already-sent notifications or invalid IDs

### 🔄 Behavior Changes

#### **Document Status Updates**
- **Before**: Automatic email sent immediately when document status changed
- **After**: Document changes added to batch queue, emails sent manually via API
- **Grouping Logic**:
  - All document changes for an application accumulate in single queue entry
  - Subsequent changes update existing queue entry until email is sent
  - After email is sent, new queue entry created for future changes

#### **Email Content**
- **Dynamic Generation**: Email content generated dynamically based on accumulated document changes
- **Comprehensive Updates**: Single email contains all pending document status changes
- **Professional Format**: Structured HTML email with clear status change information

### 🛠️ Technical Improvements

#### **New Services**
- **BatchNotificationService**: Core service managing batch notification logic
- **BatchNotificationController**: REST API endpoints for batch notifications
- **Type Safety**: Comprehensive TypeScript interfaces for all batch notification operations

#### **Code Quality**
- **Function Size**: All new functions kept under 15 lines as requested
- **Error Handling**: Comprehensive error logging and user-friendly error messages
- **Testing**: Unit tests for batch notification functionality
- **Documentation**: Complete API documentation with Swagger annotations

### 🧹 Code Cleanup

#### **Removed Functionality**
- **Automatic Email Sending**: Removed from `ApplicationService.updateDocumentStatus()`
- **Template System**: Removed `createNotificationFromTemplate()` method and related code
- **Orphaned Code**: Cleaned up unused imports and references
- **Test Updates**: Updated tests to reflect new batch notification system

#### **Updated Documentation**
- **README Updates**: Updated application service documentation
- **API Documentation**: Added comprehensive Swagger documentation for new endpoints
- **Code Comments**: Added detailed comments explaining batch notification logic

### 🔒 Security & Performance

#### **Database Optimization**
- **Indexes**: Added strategic indexes for batch notification queries
- **Transaction Safety**: Proper transaction handling for batch operations
- **Memory Management**: Efficient JSON handling for document changes array

#### **Error Resilience**
- **Graceful Degradation**: Document status updates succeed even if batch notification fails
- **Retry Logic**: Maintains existing retry mechanisms for email sending
- **Logging**: Comprehensive error logging for debugging and monitoring

### 📋 Migration Guide

#### **For Administrators**
1. **New Workflow**: Document status changes no longer send automatic emails
2. **Manual Sending**: Use new API endpoints to preview and send batch notifications
3. **Monitoring**: Check pending notifications regularly using GET endpoint

#### **For Developers**
1. **API Integration**: Update any code expecting automatic email notifications
2. **Database**: Run migration to update notification_queue table structure
3. **Testing**: Update tests that relied on automatic email sending behavior

#### **Breaking Changes**
- **Automatic Emails**: No longer sent on document status changes
- **Template System**: notification_template table and related functionality removed
- **API Responses**: Some notification-related API responses may have changed structure

### 🧪 Testing

#### **Test Coverage**
- **Unit Tests**: Added tests for BatchNotificationService core functionality
- **Integration Tests**: Verified API endpoints work correctly
- **Build Verification**: Confirmed application builds and starts without errors
- **Migration Testing**: Verified database migration runs successfully

#### **Quality Assurance**
- **TypeScript Compilation**: No compilation errors
- **Development Server**: Starts successfully with new endpoints registered
- **Database Integrity**: Migration preserves existing data while updating schema

### 📚 Documentation Updates

#### **API Documentation**
- **Swagger Annotations**: Complete API documentation for new endpoints
- **Request/Response Examples**: Detailed examples for all new endpoints
- **Error Codes**: Documented all possible error responses

#### **Technical Documentation**
- **Architecture**: Updated to reflect batch notification system
- **Database Schema**: Updated to reflect table changes
- **Integration Guide**: Instructions for using new batch notification system

### 🔄 Upgrade Instructions

1. **Database Migration**: Run `npx prisma migrate dev` to apply schema changes
2. **Code Updates**: Update any code that relied on automatic email notifications
3. **Admin Training**: Train administrators on new manual notification sending process
4. **Monitoring**: Set up monitoring for pending notifications to ensure timely sending

### 🐛 Bug Fixes
- Fixed TypeScript compilation issues with JSON type casting
- Resolved import path issues in new services
- Updated test files to remove references to deleted template system

### 📈 Performance Improvements
- **Batch Processing**: More efficient than sending individual emails
- **Database Queries**: Optimized with new indexes for batch notification queries
- **Memory Usage**: Efficient handling of document changes array in JSON format

## [0.14.2] - 2025-08-07

### Fixed
- **Password Reset Token Expiration Extended**: Extended password reset token validity from 15 minutes to 1 hour for improved user experience
  - **Agent Password Reset**: Updated `AgentService.resetPassword()` to generate tokens with `expiresIn: '1h'` instead of `'15m'`
    - Changed token expiration from 15 minutes to 1 hour for better user experience
    - Updated agent password reset email template to inform users of 1-hour validity period
    - Maintained same security practices and token structure
    - Updated unit tests to reflect new token expiration time
  - **Admin Password Reset**: Fixed inconsistent comment in `AdminService.resetPassword()` method
    - Corrected comment from "Token expires in 15 minutes" to "Token expires in 1 hour"
    - Standardized token format to use `'1h'` instead of `'60m'` for consistency
    - Updated admin password reset email template to inform users of 1-hour validity period
  - **Email Template Updates**: Updated both agent and admin password reset email templates
    - Changed user-facing text from "This password reset link will expire in **15 minutes**" to "This password reset link will expire in **1 hour**"
    - Ensured consistency between actual token expiration and what users are told in emails
    - Maintained same email template structure and styling

- **Service Name Resolution in Stage Change Emails**: Fixed "Service: undefined" issue in stage change email notifications
  - **Root Cause Analysis**: Identified that `serviceResult.serviceName` could be undefined, null, or empty string in certain edge cases
    - Issue occurred when service records existed but had missing or invalid name fields
    - Affected all email notifications that display service names (stage changes, status changes, document requests)
    - Problem was in service name extraction logic across multiple methods in ApplicationService
  - **Comprehensive Fix**: Enhanced service name extraction logic with proper fallback handling
    - Updated 8 locations in `ApplicationService` where service names are extracted for email templates
    - Added fallback logic: `(serviceResult.serviceName || \`\${serviceType} service\`)`
    - Fixed methods: `getApplicationDetails()`, `getApplicationsList()`, `sendDocumentRejectionNotification()`, `sendDocumentRequestNotification()`, `sendStatusChangeNotification()`, `sendStageChangeNotification()`, `sendApplicationRequirementsEmail()`, `sendWorkflowTemplateAssignmentEmail()`
  - **Email Template Impact**: Resolved undefined service names in all email notifications
    - **Before Fix**: Email templates showed "Service: undefined", "Service: null", or "Service: " (empty)
    - **After Fix**: Email templates now show "Service: immigration service", "Service: packages service", etc. with appropriate service type
    - Improved user experience with clear, readable service names in all email communications
  - **Testing and Verification**: Added comprehensive testing to verify the fix works correctly
    - Created test scenarios covering undefined, null, empty string, and valid service names
    - Verified all edge cases are handled properly with appropriate fallback values
    - Ensured no regression in existing functionality for valid service names

### Technical Details
- **Token Expiration Changes**:
  - Agent service: `expiresIn: '15m'` → `expiresIn: '1h'`
  - Admin service: `expiresIn: '60m'` → `expiresIn: '1h'` (standardized format)
  - Email templates: "15 minutes" → "1 hour"
- **Service Name Resolution**:
  - Enhanced extraction logic with fallback: `serviceResult.serviceName || \`\${serviceType} service\``
  - Applied fix to 8 methods across ApplicationService
  - Maintains backward compatibility with existing valid service names

## [0.14.1] - 2025-08-07

### Fixed
- **Password Reset Email Validation Enhancement**: Enhanced password reset confirmation endpoints to properly validate email existence and return appropriate error messages
  - **Enhanced Email Validation**: Both agent and admin password reset confirmation endpoints now properly validate email existence in the database
    - Added comprehensive email validation in `AgentService.confirmResetPassword()` and `AdminService.confirmResetPassword()` methods
    - Enhanced error messages to provide user-friendly feedback when email addresses are not found
    - Improved security logging to monitor password reset attempts for non-existent accounts
    - Added validation for inactive agent accounts to prevent password resets for disabled users
  - **Improved Error Handling**: Enhanced controller error handling to return appropriate HTTP status codes and messages
    - `POST /agents/reset-password/confirm` now returns HTTP 404 with clear error message for non-existent email addresses
    - `POST /admin/reset-password/confirm` now returns HTTP 404 with clear error message for non-existent email addresses
    - Enhanced error messages: "Email address not found in our records. Please verify the email address and try again."
    - Proper HTTP status code mapping for different error scenarios (404 for not found, 401 for invalid tokens)
  - **Enhanced Security Features**: Added additional security validations and monitoring
    - Validation of JWT token payload to ensure email field exists
    - Enhanced token type validation with more specific error messages
    - Security logging for monitoring password reset attempts on non-existent accounts
    - Added validation for inactive agent accounts with appropriate error messages
  - **Comprehensive Testing**: Added comprehensive unit tests for password reset confirmation functionality
    - Added tests for `AgentService.confirmResetPassword()` covering all error scenarios
    - Tests for non-existent email validation, invalid tokens, expired tokens, and successful resets
    - Enhanced test coverage for edge cases and security scenarios
    - Fixed jest configuration for proper module resolution and test execution
  - **Code Quality Improvements**: Enhanced code documentation and maintainability
    - Added detailed code comments explaining validation logic and security considerations
    - Improved error message consistency across both agent and admin endpoints
    - Enhanced logging messages for better debugging and monitoring
    - Updated API documentation to reflect new error responses and validation behavior

## [0.14.0] - 2025-08-07

### Added
- **Admin Password Reset Functionality**: Implemented comprehensive password reset system for admin users
  - **Password Reset Request Endpoint**: `POST /admin/reset-password` - Sends password reset email to admin users
    - Accepts admin email address and generates secure JWT reset token
    - Sends professional password reset email using custom admin template
    - Security-focused implementation that doesn't reveal admin account existence
    - 15-minute token expiration for enhanced security
  - **Password Reset Confirmation Endpoint**: `POST /admin/reset-password/confirm` - Resets admin password using token
    - Validates reset token and updates admin password in database
    - Comprehensive error handling for invalid/expired tokens
    - Secure password hashing using bcrypt with salt rounds
    - Proper HTTP status codes and error responses
  - **Admin Password Reset Email Template**: Professional email template for admin password reset notifications
    - Consistent branding with CareerIreland design system
    - Security notices and best practices information
    - Admin-specific messaging and call-to-action buttons
    - Responsive design compatible with all email clients
  - **Enhanced Admin Service**: Extended AdminService with password reset methods
    - `resetPassword()` - Generates reset token and sends email
    - `confirmResetPassword()` - Validates token and updates password
    - Comprehensive error handling and security logging
    - Integration with existing MailerService and LoggerService
  - **Admin Module Dependencies**: Updated AdminModule with required services
    - Added MailerService for email functionality
    - Added LoggerService for comprehensive audit logging
    - Proper dependency injection and service integration

### Enhanced
- **Agent Password Reset Verification**: Comprehensive verification and validation of existing agent password reset functionality
  - **Endpoint Verification**: Confirmed POST /agents/reset-password and POST /agents/reset-password/confirm endpoints are properly implemented
    - Both endpoints follow identical patterns to admin implementation
    - Proper API documentation with Swagger/OpenAPI integration
    - Consistent error handling and HTTP status codes
    - Security-focused response messages that don't reveal account existence
  - **Component Verification**: Validated all agent password reset components are properly implemented
    - `ResetAgentPasswordDto` and `ConfirmResetAgentPasswordDto` with proper validation decorators
    - `agent-password-reset.tsx` email template with professional branding and security notices
    - `AgentService.resetPassword()` and `AgentService.confirmResetPassword()` methods with comprehensive error handling
    - Proper integration with MailerService and LoggerService for email sending and audit logging
  - **Implementation Consistency**: Ensured perfect alignment between admin and agent password reset systems
    - Both use identical 15-minute token expiration for enhanced security
    - Both use same JWT token structure with `type: 'password_reset'` validation
    - Both use same JWT secret (`RESET_PASSWORD_SECRET`) for token generation and verification
    - Both follow identical security practices including email enumeration protection
    - Both use same error handling patterns with proper exception types and logging
    - Both use bcrypt with 10 salt rounds for secure password hashing
    - Both have consistent API response formats and success/error messages
  - **Database Schema Alignment**: Confirmed proper database field usage
    - Admin uses `password` field as per admin table schema
    - Agent uses `password_hash` field as per agent table schema
    - Both implementations correctly align with their respective database designs
  - **Email Template Consistency**: Verified both templates maintain consistent branding and security messaging
    - Same 15-minute expiration notice for user awareness
    - Identical security notices and best practices information
    - Consistent CareerIreland branding and professional styling
    - Both templates include proper fallback links and contact information
  - **End-to-End Functionality**: Confirmed complete password reset flow works correctly
    - Server startup verification shows both endpoints properly mapped
    - No compilation errors or runtime issues detected
    - Proper route registration and dependency injection confirmed
    - Integration with existing authentication and authorization systems verified

### Security Improvements
- **Password Reset Security**: Implemented industry-standard security practices
  - **Token-Based Authentication**: Secure JWT tokens with type validation and expiration
  - **Email Enumeration Protection**: Consistent responses regardless of admin account existence
  - **Comprehensive Audit Logging**: Detailed security event logging for monitoring
    - Password reset requests logged with email addresses
    - Failed attempts logged for security monitoring
    - Successful password changes logged with timestamps
  - **Secure Token Generation**: Cryptographically secure reset tokens with proper payload validation
  - **Rate Limiting Ready**: Implementation supports future rate limiting integration

### Testing Infrastructure
- **Comprehensive Unit Tests**: Added complete test coverage for admin password reset functionality
  - **AdminService Tests**: 100% coverage of password reset methods with success/error scenarios
    - Valid password reset flow testing
    - Invalid token handling and error cases
    - Email service integration testing
    - Database error handling scenarios
  - **AdminController Tests**: Complete API endpoint testing with proper HTTP responses
    - Request validation and response format testing
    - Error handling and security response testing
    - Integration with service layer validation
  - **Test Configuration**: Added Jest configuration for admin module testing
    - Proper test environment setup and mocking
    - Coverage reporting and threshold configuration
    - Integration with existing test infrastructure
  - **Package Scripts**: Added admin test commands to npm scripts
    - `npm run test:admin` - Run admin module tests
    - `npm run test:admin:cov` - Run with coverage reporting
    - `npm run test:admin:watch` - Watch mode for development

### API Documentation
- **Swagger/OpenAPI Integration**: Complete API documentation for new endpoints
  - **Request/Response Schemas**: Detailed DTO documentation with validation rules
    - `ResetAdminPasswordDto` - Email validation and format requirements
    - `ConfirmResetAdminPasswordDto` - Token and password validation rules
  - **API Response Documentation**: Comprehensive response examples and status codes
    - Success responses with proper message formatting
    - Error responses with detailed error information
    - Security-focused response documentation
  - **Authentication Requirements**: Clear documentation of endpoint security requirements

### Developer Experience
- **Code Quality**: Maintained high code quality standards throughout implementation
  - TypeScript strict mode compliance with proper type definitions
  - ESLint and Prettier formatting consistency
  - Comprehensive error handling with user-friendly messages
  - Clean, maintainable code following established patterns
- **Documentation**: Updated README and technical documentation
  - API endpoint documentation with request/response examples
  - Security considerations and best practices
  - Testing instructions and coverage requirements

## [0.13.2] - 2025-08-07

### Enhanced
- **Email Service Name Resolution**: Updated all application email notifications to use dynamically resolved service names instead of workflow template names
  - **Dynamic Service Name Resolution**: All email sending methods now use `application.service_name` (resolved via `resolveServiceName`) instead of `workflow_template.name`
    - **Manual Application Creation**: `sendApplicationCreationEmail` now resolves service name based on `service_type` and `service_id`
    - **Automatic Application Creation**: `sendWorkflowTemplateAssignmentEmail` now uses resolved service name instead of workflow template name
    - **Document-Related Emails**: Document rejection and request emails now use resolved service names
    - **Status and Stage Change Emails**: Application status and stage change emails now use resolved service names
  - **Comprehensive Service Type Support**: Enhanced service name resolution to support all service types with proper database queries
    - Immigration services: Queries `immigration_service` table for specific service names
    - Regular services: Queries `service` table for mentor-provided services
    - Packages: Queries `packages` table for career development packages
    - Training: Queries `training` table for training programs
    - Fallback handling: Returns appropriate default names when service information is unavailable
  - **Centralized Resolution Logic**: Unified service name resolution through existing `resolveServiceName` method
    - Eliminates redundant workflow template queries across multiple email methods
    - Consistent service name resolution patterns throughout the application
    - Better performance through direct service table queries instead of workflow template lookups
  - **Enhanced Error Handling and Logging**: Added comprehensive logging for service name resolution debugging
    - Logs resolved service names for each email type: "Resolved service name for application {id}: {serviceName}"
    - Specific logging for different email contexts (document rejection, request, status change, stage change)
    - Graceful handling of missing or invalid service information with appropriate fallbacks
    - Enhanced error messages for better debugging and monitoring experience

### User Experience Improvements
- **More Meaningful Email Communications**: Email serviceName field now displays actual service names instead of generic workflow template names
  - **Before**: Email subjects like "Application Requirements - Immigration Application Template"
  - **After**: Email subjects like "Application Requirements - Work Permit Application"
  - Users see the actual service names they purchased, reducing confusion and improving clarity
  - Consistent service identification across application details and email communications
- **Professional Email Content**: Enhanced email template data with user-friendly service names
  - Immigration services: "Student Visa", "Work Permit Application", "Family Reunification"
  - Career packages: "Career Development Package", "Premium Career Consultation"
  - Training programs: "Interview Skills Training", "CV Writing Workshop"
  - Mentor services: "Career Consultation", "Resume Review", "Mock Interview Session"

### Technical Improvements
- **Database Query Optimization**: Improved email sending performance through optimized service name resolution
  - Eliminated redundant workflow template queries in favor of direct service table queries
  - Reduced database load by centralizing service name resolution logic
  - Better query patterns with targeted service-specific table lookups
- **Code Maintainability**: Enhanced code organization and maintainability
  - Centralized service name resolution logic reduces code duplication
  - Consistent error handling patterns across all email sending methods
  - Clear separation of concerns between workflow template data and service information
- **Comprehensive Testing and Documentation**: Added thorough testing and documentation for service name resolution changes
  - Created comprehensive test suite documenting all changes made to email methods
  - Verified TypeScript compilation and development server startup
  - Documented backward compatibility and user experience improvements
  - Clear audit trail for service name resolution implementation

## [0.13.1] - 2025-08-07

### Enhanced
- **Email Stage Change Display Enhancement**: Improved email notifications to show actual descriptive stage names instead of generic "Step X" format
  - **Workflow Template Integration**: Enhanced stage name extraction to correctly access `stageName` field from workflow template data structure
    - Fixed issue where email notifications displayed "Previous stage: Step 1" and "Current Stage: Step 2"
    - Now displays meaningful stage names like "Document Upload", "Application Review", "Final Decision" etc.
    - Properly extracts stage names from workflow template `stageName` field instead of incorrect `name` field
  - **Enhanced Stage Name Extraction Logic**: Added robust `extractStageNamesFromTemplate` helper method for reliable stage information retrieval
    - Safely handles workflow template JSON structure parsing with proper error handling
    - Extracts stage names, descriptions, and estimated durations from workflow template data
    - Provides fallback to generic "Step X" names when descriptive names are not available
    - Handles malformed or invalid workflow template data gracefully with comprehensive logging
  - **Improved Error Handling and Logging**: Enhanced error handling for stage name extraction with detailed logging
    - Logs warnings when stage name extraction fails with application context for debugging
    - Graceful degradation ensures email notifications continue to work even when stage names cannot be extracted
    - Comprehensive error logging includes application ID, workflow template ID, and step information
  - **Before/After Email Content Improvement**:
    - **Before**: "Previous stage: Step 1" → "Current Stage: Step 2"
    - **After**: "Previous stage: Document Upload" → "Current Stage: Application Review"
    - Includes actual stage descriptions and estimated durations in email content
    - Maintains professional email template formatting with enhanced contextual information

### Technical Improvements
- **Workflow Template Data Structure Analysis**: Comprehensive investigation and documentation of workflow template JSON structure
  - Identified correct data path: `workflowTemplate[index].stageName` for stage names
  - Documented relationship between step numbers (`stageOrder`) and descriptive stage names (`stageName`)
  - Added validation for workflow template array structure and stage data integrity
- **Helper Method Implementation**: Added focused, single-responsibility helper method for stage name extraction
  - `extractStageNamesFromTemplate` method handles all stage information extraction logic (10 lines, focused implementation)
  - Proper TypeScript typing with clear return interface for stage information
  - Handles edge cases: missing stage names, out-of-bounds step numbers, invalid template structures
- **Database Query Optimization**: Enhanced database queries to properly fetch workflow template information
  - Fixed separate workflow template query in `sendStageChangeNotification` method
  - Ensures stage name extraction has access to complete workflow template data
  - Maintains efficient database access patterns with selective field retrieval

### Testing
- **Comprehensive Unit Test Coverage**: Added extensive unit tests for stage name extraction functionality
  - **Stage Name Extraction Tests**: Tests for successful extraction of actual stage names from workflow templates
  - **Fallback Scenario Tests**: Tests for graceful fallback to generic names when stage names are missing
  - **Error Handling Tests**: Tests for malformed workflow template handling and error logging
  - **Edge Case Coverage**: Tests for out-of-bounds step numbers, missing workflow templates, and guest applications
  - **Helper Method Tests**: Dedicated tests for `extractStageNamesFromTemplate` method with various input scenarios
  - **17 passing tests** covering all stage name enhancement scenarios and error conditions

### Code Quality
- **TypeScript Compilation**: All changes pass TypeScript compilation without errors
- **Development Server Compatibility**: Verified successful startup of development server with stage name enhancements
- **Memory Management**: Implemented proper memory management in stage name extraction to prevent leaks
- **Clean Code Practices**: Used simple, focused logic with minimal complexity following single responsibility principle
- **Comprehensive Documentation**: Added inline documentation explaining stage name extraction workflow and error handling

## [0.13.0] - 2025-08-07

### Added
- **Automated Email Notification System**: Comprehensive email notification system for application users with automatic status and stage change notifications
  - **Application Status Change Notifications**: Automatically sends email notifications when application status changes (e.g., pending → approved, approved → rejected)
    - Includes previous status and new status information in email content
    - Professional email templates with application reference numbers and next steps
    - Proper error handling that doesn't break main application flow when emails fail
  - **Application Stage Change Notifications**: Sends email notifications when applications move between workflow stages
    - Clearly specifies current stage name and previous stage information
    - Includes application ID and stage-specific information relevant to users
    - Extracts stage details from workflow templates including descriptions and estimated durations
    - Generates contextual next steps based on current stage
  - **Enhanced Document Status Notifications**: Improved document status change notifications with comprehensive status information
    - Includes old status and new status information in email content
    - Enhanced error handling with graceful failure recovery
    - Detailed logging for audit purposes and debugging

### Enhanced
- **Application Service Email Integration**: Enhanced `updateCurrentStep` method in ApplicationService with automatic stage change notifications
  - **Stage Change Detection**: Automatically detects when application stages change and triggers email notifications
  - **Workflow Template Integration**: Extracts stage names, descriptions, and estimated durations from workflow templates
  - **User Information Handling**: Supports both registered users and guest applications with appropriate email addressing
  - **Error Resilience**: Email notification failures don't interrupt the main stage update process
- **Document Status Update Enhancement**: Enhanced `updateDocumentStatus` method with improved notification capabilities
  - **Status Change Tracking**: Captures previous status information for comprehensive change notifications
  - **Enhanced Email Content**: Includes status transition information (e.g., "changed from PENDING to APPROVED")
  - **Graceful Error Handling**: Email notification failures are logged but don't prevent document status updates
- **Email Template Service Integration**: Leverages existing professional email templates for consistent user experience
  - **Status Change Templates**: Uses ApplicationStatusChangeEmail template for status notifications
  - **Stage Change Templates**: Uses ApplicationStageChangeEmail template for stage notifications
  - **Fallback Templates**: Includes fallback email templates when primary templates fail to render

### Technical Improvements
- **Comprehensive Error Handling**: Implemented robust error handling throughout the email notification system
  - **Non-blocking Email Failures**: Email notification failures don't interrupt main application processes
  - **Detailed Error Logging**: Comprehensive logging for email delivery attempts, failures, and successes
  - **Graceful Degradation**: System continues to function normally even when email service is unavailable
- **Email Delivery Logging**: Added comprehensive logging for email notification audit trail
  - **Delivery Attempt Logging**: Logs all email notification attempts with recipient and content details
  - **Success/Failure Tracking**: Tracks successful deliveries and failure reasons for debugging
  - **Application Context**: Includes application ID, user information, and change details in logs
- **Helper Method Implementation**: Added utility methods for generating contextual email content
  - **Stage-specific Next Steps**: `generateNextStepsForStage` method provides relevant guidance based on current stage
  - **Stage Change Messages**: `getStageChangeMessage` method generates appropriate transition messages
  - **Workflow Template Parsing**: Safely extracts stage information from workflow template JSON structures

### Testing
- **Comprehensive Unit Tests**: Added extensive unit test coverage for email notification functionality
  - **Stage Change Notification Tests**: Tests for successful notifications, error handling, and edge cases
  - **Document Status Notification Tests**: Tests for status change notifications and failure scenarios
  - **Mock Integration**: Proper mocking of email services, database operations, and logging systems
  - **Error Scenario Testing**: Tests for graceful handling of email service failures and missing data
  - **Guest Application Testing**: Tests for handling both registered users and guest applications

### Code Quality
- **TypeScript Compilation**: All changes pass TypeScript compilation without errors
- **Development Server Compatibility**: Verified successful startup of development server with new changes
- **Memory Management**: Implemented proper memory management to prevent leaks in email processing
- **Clean Code Practices**: Used simple, clean logic with minimal complexity following single responsibility principle
- **Comprehensive Documentation**: Added inline documentation explaining email notification workflow and error handling

## [0.12.0] - 2025-08-06

### Added
- **File Replacement Functionality**: Comprehensive file replacement system for application document uploads with automatic cleanup of orphaned files
  - **Existing File Detection**: Automatically detects and deletes existing files from Supabase storage before uploading replacement files
  - **Database Transaction Support**: Wraps entire file replacement operation in database transactions to ensure data consistency and rollback on failure
  - **Document Vault Record Updates**: Updates existing document vault records instead of creating new ones, preventing database bloat and maintaining referential integrity
  - **Graceful Error Handling**: Continues upload process even if existing file deletion fails (handles cases where files may not exist in storage)
  - **Comprehensive Logging**: Detailed logging for each step of the file replacement process for debugging and audit purposes
  - **User-Friendly Error Messages**: Provides clear, actionable error messages for different failure scenarios (storage errors, transaction failures, etc.)

### Enhanced
- **Upload Application Document Endpoint**: Enhanced `PUT /applications/{id}/document` endpoint with file replacement capabilities
  - **Transaction Safety**: All database operations are wrapped in transactions with automatic rollback on failure
  - **Storage Cleanup**: Automatically removes orphaned files from Supabase storage when documents are replaced
  - **Data Consistency**: Ensures document vault records are updated rather than duplicated during file replacements
  - **Error Recovery**: Robust error handling that prevents partial updates and maintains system integrity

### Technical Improvements
- **Database Operations**: Improved transaction handling in `uploadApplicationDocument` method with proper rollback mechanisms
- **Storage Management**: Enhanced integration with MediaService for reliable file deletion from Supabase storage
- **Memory Management**: Optimized document vault record handling to prevent memory leaks during file operations
- **Test Coverage**: Updated unit tests to cover new file replacement functionality including edge cases and error scenarios
- **Code Documentation**: Added comprehensive inline documentation explaining the file replacement workflow and transaction handling

### Fixed
- **Orphaned File Prevention**: Eliminates accumulation of orphaned files in Supabase storage when users replace application documents
- **Database Consistency**: Prevents creation of duplicate document vault records during file replacement operations
- **Transaction Integrity**: Ensures all file replacement operations are atomic with proper rollback on any step failure

## [0.11.2] - 2025-08-06

### Fixed
- **Foreign Key Constraint Violation**: Fixed critical bug in document deletion endpoint that caused foreign key constraint violations
  - **Root Cause**: The system was attempting to delete `document_vault` records while they were still referenced by `application_document` table via `document_vault_id` foreign key
  - **Solution**: Corrected deletion order to remove `application_document` record first, then delete the `document_vault` record
  - **Database Operations**: Changed from UPDATE to complete DELETE operations for both `application_document` and `document_vault` tables
  - **Data Integrity**: Ensures proper cleanup without violating database constraints while maintaining transaction safety
  - **Error Resolution**: Eliminates `Foreign key constraint violated: application_document_document_vault_id_fkey` errors during document deletion

### Technical Improvements
- **Database Transaction Order**: Implemented correct sequence for foreign key constraint handling in document deletion
- **Complete Record Cleanup**: Both `application_document` and `document_vault` records are now completely removed instead of just being updated
- **Test Coverage**: Updated unit tests to reflect the corrected deletion logic with proper DELETE operations
- **Error Handling**: Maintained robust error handling and transaction rollback capabilities with the corrected implementation

## [0.11.1] - 2025-08-06

### Changed
- **Document Deletion Cleanup**: Enhanced document deletion process to completely remove document vault entries instead of just clearing file paths
  - **Database Operation**: Changed `document_vault.update()` to `document_vault.delete()` for proper cleanup when deleting application documents
  - **Data Integrity**: Ensures complete removal of document vault records rather than leaving orphaned entries with empty file paths
  - **Storage Optimization**: Prevents accumulation of unused document vault records in the database
  - **Consistency**: Aligns with the principle of complete resource cleanup when documents are deleted from applications

### Technical Improvements
- **Database Cleanup**: Improved data integrity by ensuring complete removal of document vault entries during document deletion
- **Test Coverage**: Updated unit tests to reflect the change from UPDATE to DELETE operations in document vault cleanup
- **Error Handling**: Maintained robust error handling and transaction safety with the new DELETE operation
- **Audit Logging**: Preserved comprehensive logging for the enhanced cleanup process

## [0.11.0] - 2025-08-06

### Added
- **Document Deletion Endpoint**: Comprehensive DELETE endpoint for application document management with role-based permissions and proper cleanup
  - **Endpoint**: `DELETE /applications/{application_id}/documents/{document_id}` - Delete documents from applications with proper validation and permissions
  - **Role-Based Permissions**: Regular users can only delete non-approved documents, while admin/agent users can delete any document including approved ones
  - **Comprehensive Validation**: Validates application_id/document_id combination exists, ensures document has been uploaded, and checks user ownership/upload permissions
  - **File Cleanup Operations**: Automatically deletes primary document files from Supabase storage with graceful handling of missing files
  - **Database Updates**: Updates both `application_document` and `document_vault` tables to reflect deletion with proper status tracking
  - **Audit Logging**: Comprehensive logging of all deletion operations for audit trail and debugging purposes
  - **Transaction Safety**: Uses database transactions to ensure atomic operations with automatic rollback on storage failures
  - **Error Handling**: Detailed error messages for different failure scenarios (not found, forbidden, validation errors, storage failures)

### Enhanced
- **Application Service**: Added `deleteApplicationDocument` method with comprehensive validation, role-based business logic, and file cleanup operations
- **Application Controller**: Added DELETE endpoint with proper OpenAPI documentation, request/response schemas, and error handling
- **Test Coverage**: Added comprehensive unit tests covering all validation scenarios, role-based permission logic, file cleanup operations, and error handling cases
- **Permission System**: Enhanced role-based access control to support document deletion permissions for different user types

### Technical Improvements
- **Storage Integration**: Leveraged existing MediaService for reliable file deletion from Supabase storage with proper error handling
- **Database Schema Utilization**: Properly utilized existing `application_document` and `document_vault` table relationships for deletion tracking
- **Code Quality**: Followed existing codebase patterns and conventions for consistency and maintainability
- **API Documentation**: Added comprehensive OpenAPI/Swagger documentation with detailed request/response examples and error codes

### Security
- **Access Control**: Implemented strict role-based access control preventing unauthorized document deletions
- **Ownership Validation**: Ensures users can only delete documents from their own applications or documents they uploaded
- **Permission Enforcement**: Admin/agent users have elevated permissions while regular users have restricted access to approved documents

### Testing
- **Unit Tests**: Added comprehensive test coverage for both service and controller layers
- **Permission Testing**: Tests cover all role-based permission scenarios and edge cases
- **Error Handling Tests**: Validates proper error handling for all failure scenarios
- **Integration Verification**: Confirmed successful compilation, server startup, and endpoint registration

## [0.10.0] - 2025-07-19

### Enhanced
- **Test Command Infrastructure Consolidation**: Comprehensive audit and consolidation of npm test command structure for improved developer experience and maintainability
  - **Broken Command Removal**: Identified and removed all non-functional test commands including security-related commands that referenced missing jest config files and test files
  - **Main Test Command Optimization**: Updated primary `npm run test` command to exclude broken security tests, ensuring reliable execution across all functional test suites
  - **Consolidated Test Categories**: Maintained existing consolidated test commands for better organization:
    - `npm run test` - Runs all unit tests across modules (payment, document, user, agent, immigration, template, validation, workflow, workflow-template, document-reminder)
    - `npm run test:models` - Runs model-specific service tests for core entities (application, user, agent services)
    - `npm run test:endpoints` - Runs integration tests for all API endpoints across modules
    - `npm run test:functions` - Runs function-specific tests including document reminder and email integration
  - **Granular Test Commands**: Added new granular test commands for specific testing scenarios:
    - `npm run test:file -- <file-path>` - Run tests for specific files with proper path resolution
    - `npm run test:endpoint -- <pattern>` - Run tests matching specific endpoint patterns
    - `npm run test:model -- <pattern>` - Run tests matching specific model patterns
  - **Command Structure Simplification**: Reduced complexity from 90+ individual test commands to streamlined categories while preserving all functional test capabilities

### Fixed
- **Security Test Commands**: Removed broken security test commands that referenced non-existent jest configuration files and empty test directories
  - **Eliminated Commands**: Removed `test:security`, `test:security:watch`, and `test:security:cov` commands that were causing test suite failures
  - **Test Suite Reliability**: Fixed `test:unit` command execution by removing references to broken security tests
  - **Clean Command Structure**: Maintained all functional test commands while removing only broken/non-functional ones

### Verified
- **Build and Runtime Integrity**: Comprehensive verification of application stability after test command consolidation
  - **TypeScript Compilation**: Confirmed zero compilation errors with `npm run build`
  - **Server Startup**: Verified successful application startup with all modules loading correctly
  - **Test Command Functionality**: Validated all consolidated and granular test commands execute properly and find appropriate test files
  - **Module Initialization**: Confirmed proper initialization of all services and 150+ API endpoints after changes

### Technical Improvements
- **Developer Experience**: Enhanced test command structure for better developer productivity and easier debugging
- **Command Reliability**: Eliminated failing test commands that were causing CI/CD pipeline issues
- **Test Organization**: Improved test command organization with clear categories for different testing scenarios
- **Documentation Clarity**: Better command naming and structure for intuitive developer usage

### Code Quality
- **Test Infrastructure Maintenance**: Cleaned up obsolete and broken test command references
- **Version Management**: Updated package version to 0.10.0 following semantic versioning for minor version with new features
- **Error Handling**: Improved error handling in test command execution with proper exit codes

## [0.9.0] - 2025-07-19

### Added
- **Document Reminder System Integration**: Enhanced main README.md with comprehensive documentation for the document reminder system
  - **New Documentation Section**: Added dedicated section with quick start guide, configuration options, and testing instructions
  - **Table of Contents Update**: Included document reminder system in main navigation structure
  - **Essential Links**: Added document reminder documentation to development resources section
  - **Feature Overview**: Updated key features list to include automated missing document reminder emails

### Improved
- **File Management and Organization**: Streamlined documentation structure for better maintainability
  - **File Renaming**: Renamed `scripts/README-document-reminder.md` to `scripts/document-reminder.md` for consistency
  - **Documentation Links**: Updated all references to use the new file name and location
  - **Integration Documentation**: Enhanced README.md with clear instructions for document reminder functionality usage

### Enhanced
- **Testing Infrastructure Consolidation**: Unified and optimized npm test command structure
  - **Unified Test Commands**: Created consolidated test commands for better developer experience
    - `npm run test:unit` - Runs all unit tests across modules (payment, document, user, agent, immigration, template, validation, workflow, workflow-template, security, document-reminder)
    - `npm run test:models` - Runs model-specific service tests for core entities
    - `npm run test:endpoints` - Runs integration tests for all API endpoints
    - `npm run test:functions` - Runs function-specific tests including document reminder and email integration
  - **Command Optimization**: Streamlined test execution by grouping related test suites
  - **Developer Productivity**: Reduced complexity from 63+ individual test commands to 4 main categories plus specific module commands

### Verified
- **Build and Runtime Integrity**: Comprehensive verification of application stability after changes
  - **TypeScript Compilation**: Confirmed zero compilation errors with `npm run build`
  - **Server Startup**: Verified successful application startup with all modules loading correctly
  - **Module Initialization**: Confirmed proper initialization of all services including:
    - JWT authentication system with multiple secret support
    - Supabase client connections for file storage
    - Database connections via Prisma ORM
    - All controller routes mapped successfully (150+ endpoints)
    - Email service integration with React templates

### Code Quality
- **Existing Infrastructure Validation**: Confirmed comprehensive code quality measures already in place
  - **File-based Error Logging**: Validated Winston logger implementation with daily rotation and structured JSON logging
  - **Memory Management**: Verified proper resource management with Prisma OnModuleInit and Supabase retry logic
  - **Error Handling**: Confirmed user-friendly error messages via custom exception filters
  - **Testing Coverage**: Validated extensive unit test coverage across all modules with 150+ test cases

### Technical Improvements
- **Documentation Standards**: Enhanced documentation structure following industry best practices
- **Developer Experience**: Improved onboarding with clearer test command structure and documentation
- **Maintenance Efficiency**: Streamlined file organization and naming conventions
- **Integration Clarity**: Better documentation of document reminder system integration and usage

## [0.8.2] - 2025-07-19

### Added
- **Comprehensive README.md**: Complete overhaul with detailed setup instructions, API documentation, developer guide, and database management procedures
  - **Project Setup**: Prerequisites, environment configuration, database setup, and development server instructions
  - **API Documentation**: Complete endpoint reference with examples for all modules (auth, user, admin, agent, mentor, payment, documents, packages, etc.)
  - **Developer Guide**: Code architecture overview, development workflow, best practices, and debugging instructions
  - **Database Management**: Backup procedures, migration best practices, schema modification guidelines, and monitoring commands
  - **Production Deployment**: PM2 configuration, environment setup, monitoring, and logging procedures
  - **Essential Links**: Documentation references, external resources, and architecture guides

### Improved
- **Codebase Cleanup**: Comprehensive audit and removal of unused files to improve maintainability
  - **Scripts Directory**: Removed 11 unused script files (migration scripts, test files, validation scripts)
  - **Documentation**: Removed 39 outdated documentation files while preserving essential guides
  - **Test Runner**: Updated `scripts/run-all-tests.ts` to reference only existing and functional test scripts
  - **Build Process**: Verified successful TypeScript compilation and server startup after cleanup

### Fixed
- **Build Verification**: Confirmed successful compilation and server startup with all modules loading correctly
  - **TypeScript Compilation**: All files compile without errors
  - **Server Initialization**: All modules, routes, and services initialize properly
  - **Database Connections**: PostgreSQL and Supabase connections established successfully
  - **Authentication**: JWT authentication system working correctly

### Verified
- **Database Schema Integrity**: Confirmed `notification_queue` and `notification_template` tables are actively used and essential
  - **Active Usage**: Found in `src/application/services/notification.service.ts`, tests, and seeding scripts
  - **Core Functionality**: Supports email notification queue management, retry logic, and analytics
  - **Decision**: Tables preserved as they are critical for notification system functionality

### Technical Improvements
- **Documentation Standards**: Established comprehensive documentation structure following industry best practices
- **Code Organization**: Improved codebase structure by removing unused files and updating references
- **Development Workflow**: Enhanced developer onboarding with detailed setup and debugging instructions
- **Production Readiness**: Documented deployment procedures, monitoring, and maintenance practices

## [0.8.1] - 2025-07-19

### Fixed
- **Missing Document Reminder System**: Fixed critical scheduling logic issue
  - **Bug Fix**: Changed reminder logic from `>=` to `===` to send reminders only on exact day intervals instead of every day after threshold
  - **Configuration Update**: Updated default reminder days from 10 to 6 days to match current configuration
  - **Timing Logic**: Reminders now sent exactly every 6 days (or user-configured interval) instead of daily after 6 days
  - **Prevents Spam**: Eliminates daily reminder emails that were being sent after the initial reminder threshold

### Improved
- **PM2 Production Configuration**: Enhanced PM2 ecosystem configuration for better production deployment
  - **Fixed Hard-coded Paths**: Removed hard-coded `/root/api` paths for better portability
  - **Enhanced Error Handling**: Added proper restart policies, memory limits, and error logging
  - **Compiled JavaScript**: Updated scheduler to use compiled JavaScript instead of TypeScript in production
  - **Log Management**: Added structured logging with separate error, output, and combined log files
  - **Process Monitoring**: Improved process monitoring with better restart policies and memory management

### Removed
- **Broken Test File**: Removed `scripts/test-scheduler-timing.ts` that was causing TypeScript compilation errors
  - **Build Fix**: Resolved TypeScript compilation issues preventing successful builds
  - **Clean Codebase**: Eliminated unused test file with broken imports

### Technical Improvements
- **Build Verification**: Confirmed successful TypeScript compilation and server startup
- **API Functionality**: Verified all API endpoints are working correctly
- **Database Schema**: Confirmed notification template tables are actively used and should be retained
- **Configuration Consistency**: Aligned all default values to use 6-day reminder intervals

## [0.8.0] - 2025-07-18

### Major Changes
- **Unified Notification Configuration System**: Migrated from individual user notification files to a single unified configuration approach
  - **Breaking Change**: NotificationSettingsStorageService now uses unified `config/notification-settings.json` instead of individual user files
  - **Architecture Update**: Simplified notification settings management with centralized configuration
  - **File Structure**: Eliminated individual user notification files in favor of unified system defaults

### Added
- **Unified Configuration File**: Created `config/notification-settings.json` with system-wide default notification settings
  - Includes `user_id` field for tracking configuration updates
  - Provides default settings for all notification types
  - Supports centralized notification preference management

### Changed
- **NotificationSettingsStorageService**: Complete refactor to support unified configuration approach
  - `readSettings()`: Now reads from unified config file and returns user-specific settings
  - `writeSettings()`: Updates system defaults in unified configuration
  - `updateSettings()`: Merges updates with existing unified settings
  - `settingsExist()`: Checks for unified configuration file existence
  - `deleteSettings()`: Simplified for unified config approach
- **File Management**: Cleaned up config directory by removing obsolete individual user notification files
- **Documentation**: Updated all references to reflect new unified configuration approach

### Improved
- **System Architecture**: Simplified notification settings management with unified configuration
- **File Organization**: Cleaner config directory structure with single notification configuration file
- **Maintainability**: Reduced complexity by eliminating individual user file management
- **Performance**: Improved efficiency with single file reads instead of multiple individual files

### Technical Details
- **Configuration Storage**: Single JSON file approach for notification settings
- **User Settings**: Individual user preferences handled in memory with fallback to system defaults
- **Backward Compatibility**: Service interface remains the same, only internal implementation changed
- **Error Handling**: Enhanced error logging for unified configuration file operations
- **Testing**: Updated test suites to work with unified configuration approach

### Migration Notes
- **Existing Deployments**: Individual user notification files will be ignored in favor of unified configuration
- **Default Behavior**: All users will use system default notification settings from unified config
- **Customization**: User-specific preferences can be handled at the application layer
- **File Cleanup**: Old individual user notification files can be safely removed

## [0.7.1] - 2025-07-18

### Changed
- **File Management and Organization**: Streamlined notification configuration and document reminder system
  - **Notification Settings**: Created unified `config/notification-settings.json` file with `user_id` field for tracking configuration updates
  - **Config Directory Cleanup**: Removed obsolete notification configuration files to maintain clean directory structure
  - **Script Renaming**: Renamed `scripts/consolidated-document-reminder.ts` to `scripts/document-reminder.ts` for simplified naming convention
  - **Reference Updates**: Updated all import statements, npm scripts, and documentation references to reflect new filename

### Updated
- **Package Scripts**: Updated npm scripts to use new `document-reminder.ts` filename
  - Changed: `reminder:documents`, `reminder:documents:scheduler`, `reminder:documents:status`
  - Changed: `test:document-reminder`, `test:document-reminder:watch`, `test:document-reminder:cov`
- **Ecosystem Configuration**: Updated PM2 ecosystem configuration to use renamed script file
- **Documentation**: Updated README files and inline documentation to reflect new naming conventions
- **Test Files**: Renamed and updated test files to match new script naming

### Improved
- **Code Quality**: Enhanced file-based error logging already implemented in document reminder system
- **Build Verification**: Confirmed TypeScript compilation and development server startup work correctly after changes
- **Maintainability**: Simplified file structure and naming for better developer experience

### Technical Details
- **File Structure**: Maintained existing functionality while improving organization
- **Backward Compatibility**: All existing functionality preserved during renaming process
- **Error Handling**: Comprehensive file-based logging system already in place for debugging
- **Testing**: All existing unit tests updated to work with new file structure

## [0.7.0] - 2025-07-18

### Changed
- **BREAKING CHANGE: Notification Settings Storage Structure**: Refactored notification settings from nested JSON structure to flat structure for improved simplicity and maintainability
  - **Old Structure**: Single file with user IDs as top-level keys: `{"user_id": {"id": "...", "user_id": "...", ...}}`
  - **New Structure**: Individual files per user with flat JSON objects: `config/user_id.json` containing `{"id": "...", "user_id": "...", ...}`
  - **Migration Script**: Created `scripts/migrate-notification-settings.ts` to automatically convert existing data with backup functionality
  - **Rollback Support**: Migration script includes rollback capability to restore from backup if needed

### Added
- **Notification Settings Migration Tool**: Comprehensive migration system for converting notification settings data structure
  - **Automatic Backup**: Creates timestamped backup of original data before migration
  - **Data Validation**: Validates data integrity during migration process
  - **Error Recovery**: Handles migration errors gracefully with detailed error reporting
  - **CLI Interface**: User-friendly command-line interface with help and rollback options
  - **Package Script**: Added `npm run migrate:notification-settings` for easy execution

### Improved
- **NotificationSettingsStorageService**: Completely refactored to work with individual files per user
  - **File-based Storage**: Each user's settings stored in separate JSON file for better isolation
  - **Error Handling**: Enhanced error handling with file-based logging for debugging
  - **Data Integrity**: Improved validation and structure checking for notification settings
  - **Performance**: Reduced memory usage by loading only required user settings instead of all settings

### Removed
- **Obsolete Reminder Files**: Cleaned up legacy reminder system files to maintain only the consolidated system
  - Removed: `scripts/missing-document-reminder.ts`
  - Removed: `scripts/missing-document-reminder.service.ts`
  - Removed: `scripts/scheduler/missing-document-scheduler.ts`
  - Removed: `scripts/scheduler-manager.ts`
  - Removed: `scripts/utils/process-manager.ts`
  - Removed: `scripts/utils/reminder-logger.ts`
  - Removed: Associated test files and documentation for obsolete systems
- **Package Scripts**: Removed obsolete npm scripts related to old reminder system
  - Removed: `script:missing-document-reminder*` scripts
  - Removed: `scheduler:*` scripts
  - Removed: `test:missing-document-reminder*` scripts

### Updated
- **Package Version**: Updated to 0.7.0 following semantic versioning for minor version with breaking changes
- **Ecosystem Configuration**: Updated `ecosystem.config.js` to use consolidated document reminder script
- **Documentation**: Updated system documentation to reflect new notification settings structure

### Technical Details
- **File Structure**: Notification settings now stored as individual JSON files in `config/` directory
- **API Compatibility**: PUT endpoints continue to merge data without deleting existing fields as per user preference
- **Simple Implementation**: Maintains user preference for minimal, clean code without over-engineering
- **Error Logging**: Continues to use file-based error logging for debugging as preferred by user

## [0.6.0] - 2025-07-18

### Added
- **Unified Notification Configuration**: Created centralized `config/notification-settings.json` with default system settings and user tracking
- **Document Reminder System**: Created a single, self-contained document reminder system that automatically sends email notifications to users about missing required documents
  - **Single Script Implementation**: Consolidated all missing document reminder functionality into one script (`scripts/consolidated-document-reminder.ts`) eliminating the need for multiple files and complex command structures
  - **Array Operators for Database Queries**: Implemented proper array operators (has, hasSome, hasEvery) instead of equality operators for filtering applications with missing documents
  - **React Email Template Integration**: Integrated with existing `application-requirements.tsx` template pattern using React email rendering with fallback templates for error scenarios
  - **File-based JSON Configuration**: Uses `config/notification-settings.json` to read `missing_document_reminder_days` values for configurable reminder intervals (currently set to 10 days)
  - **7-Day Completion Timeline**: Includes clear messaging in emails that users have 7 days to complete the application process once all required documents are uploaded
  - **Self-contained Scheduling**: Implements internal scheduling mechanism within the application rather than requiring external cron jobs for easier deployment and management across different environments
  - **Non-blocking Email Sending**: Implements asynchronous email sending with proper error handling and fallback templates
  - **File-based Error Logging**: Comprehensive logging system that writes to `logs/document-reminders/` directory with JSON-formatted log entries for debugging
  - **Comprehensive Unit Tests**: Full test coverage including database queries, date logic, email functionality, error scenarios, and edge cases

### Improved
- **Email Template Consistency**: Enhanced email templates to follow consistent header/footer structure from existing templates with proper branding and user-friendly messaging
- **Database Query Performance**: Optimized database queries to use proper Prisma array operators for better performance and accuracy when filtering applications with missing documents
- **Error Handling**: Implemented robust error handling with fallback mechanisms for email template rendering failures and database connection issues
- **Memory Management**: Designed with minimal, clean code approach without over-engineering to prevent memory leaks and ensure efficient resource usage

### Changed
- **Package Scripts**: Added new npm scripts for the consolidated reminder system:
  - `npm run reminder:documents` - Execute one-time reminder process
  - `npm run reminder:documents -- --scheduler` - Run as long-running scheduler process
  - `npm run reminder:documents -- --status` - Check scheduler status
  - `npm run test:consolidated-reminder` - Run unit tests for the consolidated system
- **Package Version**: Updated to 0.6.0 following semantic versioning for new feature addition

### Technical Features
- **Environment Variable Configuration**: Supports scheduler configuration through environment variables (SCHEDULER_ENABLED, SCHEDULER_HOUR, SCHEDULER_MINUTE, etc.)
- **Graceful Shutdown**: Implements proper signal handling for SIGTERM/SIGINT for clean scheduler shutdown
- **Dynamic Import**: Uses dynamic imports for React email templates to avoid TypeScript compilation issues
- **Cross-platform Compatibility**: Works on both Windows and Unix-like systems with proper path handling

### Quality Assurance
- **TypeScript Compilation**: Verified TypeScript compilation compatibility with existing codebase
- **Development Server**: Confirmed functionality with `npm run start:dev`
- **Unit Test Coverage**: Comprehensive test suite covering all major functionality including database operations, email sending, scheduler functionality, and error scenarios
- **Error Scenario Testing**: Extensive testing of edge cases including missing user data, email service failures, and database connection issues

## [0.5.1] - 2025-07-18

### Fixed
- **TypeScript Build Configuration**: Fixed TypeScript compilation issues by excluding test files from production build
  - **Build Process**: Updated tsconfig.json to exclude test/**/* and *.spec.ts files from compilation
  - **Production Build**: Ensured clean production build without test-related TypeScript errors
  - **Module Resolution**: Fixed import path issues that were preventing successful compilation

### Improved
- **PM2 Ecosystem Configuration**: Enhanced and tested PM2 configuration for production deployment
  - **API Service**: Verified main API service starts correctly with PM2 using dist/src/main.js
  - **Scheduler Service**: Fixed scheduler service configuration to use direct ts-node execution instead of npm scripts
  - **Process Management**: Tested both services start successfully and remain online
  - **Production Ready**: Configured proper working directories and environment variables for server deployment
  - **Cross-Platform**: Ensured PM2 configuration works on both development and production environments

### Changed
- **Package Version**: Updated package version to 0.5.1 following semantic versioning for bug fixes
- **Build Exclusions**: Modified TypeScript configuration to properly separate production code from test code

## [0.5.0] - 2025-07-18

### Fixed
- **Scheduler Service Cross-Platform Compatibility**: Fixed scheduler service to work properly on Windows and Unix-like systems
  - **Process Manager Enhancement**: Updated ProcessManager to use `npx ts-node` on Windows for better compatibility
  - **Path Handling**: Fixed Windows-specific path handling issues in process start time detection
  - **Shell Integration**: Added proper shell integration for Windows process spawning
  - **Error Handling**: Improved error handling for process management across different operating systems

### Added
- **Production Process Management**: Added PM2 ecosystem configuration for production deployment
  - **Ecosystem Configuration**: Created `ecosystem.config.js` with simple, production-ready PM2 configuration
  - **Admin Service**: Configured main API service as "admin" process with proper memory limits and restart policies
  - **Scheduler Service**: Configured scheduler as separate PM2 process with dedicated environment variables
  - **Environment Variables**: Production-ready environment configuration for scheduler service
  - **Auto-restart**: Configured automatic restart policies for both services

### Improved
- **Unit Test Coverage**: Enhanced and fixed comprehensive unit tests for scheduler functionality
  - **Logger Service Mocking**: Fixed LoggerService mock interfaces to match actual service methods
  - **Date Mocking**: Improved Date constructor mocking for better test reliability
  - **Module Resolution**: Fixed TypeScript module resolution issues for React email templates
  - **Jest Configuration**: Updated Jest configuration to handle .tsx files and proper module mapping
  - **Cross-Platform Tests**: Ensured tests work correctly on both Windows and Unix-like systems

### Changed
- **File-Based Error Logging**: Enhanced file-based error logging with improved cross-platform compatibility
- **TypeScript Compilation**: Verified and ensured all TypeScript compilation issues are resolved
- **Package Version**: Updated package version to 0.5.0 following semantic versioning

## [0.4.0] - 2025-07-14

### Added
- **Missing Document Reminder System (v1.0.0)**: Comprehensive self-contained scheduling system for automated document reminder emails
  - **Self-Contained Scheduler**: Built-in scheduler that runs reminder processes at specified intervals without requiring external cron setup
  - **Environment-Based Configuration**: Flexible configuration system supporting development, staging, and production environments with customizable schedules
  - **Process Management**: Complete start/stop/restart capabilities for long-running scheduler processes with health monitoring and automatic restart
  - **Smart Timing Logic**: Sends reminders only when exact number of configured days has passed since last update (NOT daily reminders)
  - **User-Configurable Frequencies**: Each user can set their own reminder frequency (7, 14, 21 days, etc.) via notification settings
  - **30-Day Cutoff**: Automatically stops sending reminders for applications older than 30 days to prevent spam
  - **Database Query Service**: Efficient queries for applications with pending/rejected/missing documents using proper array operators
  - **React Email Template**: Professional missing document reminder template with document categorization and status badges
  - **Email Integration**: Non-blocking email sending with fallback templates and comprehensive error recovery
  - **File-Based Logging**: Dedicated logging system with log rotation, separate error logs, and performance tracking
  - **CLI Management Tools**: Command-line interface for scheduler management (start/stop/restart/status)
  - **Comprehensive Testing**: Complete unit test suite covering database queries, email integration, scheduler logic, and error handling
  - **Documentation**: Detailed README with usage examples, configuration options, and troubleshooting guides

### Added
- **Email Template Integration System (v2.2.0)**: Complete email template integration with application workflow management
  - **Email Template Integration Service**: New dedicated service (`EmailTemplateIntegrationService`) for managing all email template operations with comprehensive error handling and fallback templates
  - **Application Workflow Integration**: Seamless integration of email templates into application creation, status updates, document management, and workflow template assignment flows
  - **Document Request Email Integration**: Automated email sending when documents are specifically requested from applicants with deadline tracking and clear instructions
  - **Application Requirements Email Integration**: Automatic email sending upon application creation and workflow template assignment with required documents list and 7-day completion deadline
  - **Status Change Email Integration**: Dynamic email notifications for application status updates with color-coded status indicators, next steps guidance, and contextual messaging
  - **Document Rejection Email Integration**: Professional email notifications for document rejections with specific feedback, resubmission instructions, and helpful guidelines
  - **Comprehensive Error Handling**: File-based error logging with fallback email templates ensuring email delivery even when React templates fail
  - **Unit Test Suite**: Complete test coverage with 15 test cases covering all email integration scenarios, error handling, and fallback mechanisms
  - **Environment Configuration**: Automatic environment variable integration for website URLs, support emails, and service configuration
- **Email Template System Enhancement (v1.1.0)**: Added four new professional email templates for application workflow management
  - **Document Request Template** (`document-request.tsx`): Professional template for requesting document submissions with clear instructions and deadline information
  - **Application Requirements Template** (`application-requirements.tsx`): Automated template for new applications with required documents list, 7-day completion deadline, and website login link
  - **Application Status Change Template** (`application-status-change.tsx`): Dynamic template for status updates with color-coded status indicators and next steps guidance
  - **Document Rejection Template** (`document-rejection.tsx`): Supportive template for document rejections with specific feedback, resubmission instructions, and helpful guidelines
  - **Consistent Design System**: All templates follow the same header/footer structure as existing `purchase-notification.tsx` template
  - **TypeScript Type Safety**: Comprehensive TypeScript interfaces for all template props with proper validation
  - **Responsive Design**: Mobile-friendly templates with consistent styling and branding
  - **Environment Integration**: Templates automatically use WEBSITE environment variable for login links
  - **Comprehensive Testing**: Added unit test suite with 8 test cases covering template imports and TypeScript validation
  - **Jest Configuration**: Created dedicated test configuration for template testing with proper TSX support
- **Agent Auto-Assignment and Removal System (v0.1.0)**: Implemented comprehensive agent management for applications with role-based auto-assignment
  - **Auto-Assignment Logic**: Agent users are automatically assigned to applications they create, while admin users remain unassigned
  - **Agent Removal Endpoint**: New DELETE /applications/:applicationId/agents/:agentId endpoint for removing agents from applications
  - **Role-Based Authorization**: Agents can only remove themselves, admins can remove any agent from applications
  - **Idempotent Operations**: Agent removal handles non-existent agent IDs gracefully without errors
  - **Comprehensive Testing**: Added extensive unit tests covering all auto-assignment and removal scenarios
  - **Enhanced API Documentation**: Updated Swagger documentation with detailed examples and error codes
  - **Backward Compatibility**: Preserves existing agent_ids array functionality and API response formats
- **File-Based Notification Settings Storage (v2.1)**: Implemented comprehensive file-based storage system for notification settings
  - **NotificationSettingsStorageService**: New service for JSON file operations with atomic writes and file locking
  - **Atomic File Operations**: Implemented atomic write operations to prevent data corruption during concurrent access
  - **File Locking Mechanisms**: Added proper file locking for safe concurrent access scenarios
  - **Comprehensive Error Handling**: Enhanced error logging and user-friendly error messages for file I/O operations
  - **Unit Tests**: Added comprehensive test suite for file-based notification settings functionality
  - **Config Directory Structure**: Created `config/notification-settings/` directory for JSON storage

### Changed
- **BREAKING**: Notification settings now stored in JSON files instead of database tables
- **NotificationService Refactoring**: Updated to use file-based storage for all notification settings operations
- **PUT Endpoint Behavior**: PUT operations now preserve existing fields during updates (merge behavior)
- **Enhanced Logging**: Improved error handling and logging for notification settings operations
- **Module Dependencies**: Updated ApplicationModule and DocumentModule to include NotificationSettingsStorageService

### Removed
- **Database Table**: Removed `notification_settings` table from Prisma schema
- **Database Migration**: Created migration to drop notification_settings table and related constraints
- **Prisma Relationships**: Removed notification_settings relationship from user model
- **Database Dependencies**: Eliminated all database-related code for notification settings

### Technical Implementation Details
- **JSON File Storage**: Each user's notification settings stored as individual JSON files (`{user_id}.json`)
- **Atomic Operations**: Implemented temporary file writes with atomic renames to prevent corruption
- **Backup and Recovery**: Automatic backup creation during updates with rollback capability
- **Concurrent Access Safety**: File locking map prevents race conditions during simultaneous operations
- **Default Settings**: Automatic creation of default settings for new users
- **Data Validation**: Input validation for notification preference ranges (e.g., reminder days 1-365)

### Migration and Compatibility
- **API Compatibility**: Maintained existing API interface - no changes to endpoint signatures
- **Backward Compatibility**: Same response format and behavior for all notification settings endpoints
- **Data Migration**: Existing database records should be migrated to JSON files before deployment
- **Build Verification**: Confirmed successful TypeScript compilation and development server startup

- **Application Status Management Enhancement (v2.0)**: Implemented comprehensive application status logic for payment webhook and direct creation endpoints
  - **Payment Webhook Applications**: Applications created via payment webhook (`createApplicationFromPayment`) now have `status: ApplicationStatus.Pending`
  - **Direct POST Applications**: Applications created via POST /applications endpoint (`createNewApplication`) now have `status: ApplicationStatus.Pending`
  - **ApplicationStatus Enum**: Restored "Pending" status to the ApplicationStatus enum in Prisma schema (prisma/schema/schema.prisma)
  - **Database Migration**: Updated database schema with `npx prisma db push` to include "Pending" status in ApplicationStatus enum
  - **Service Layer Updates**: Modified ApplicationService methods to use proper enum values instead of string literals
  - **Comprehensive Testing**: Added dedicated test suite (`test/application/application-status.spec.ts`) with 8 comprehensive test cases covering:
    - Payment webhook application creation with Pending status
    - Guest payment webhook application creation with Pending status
    - Existing application detection and handling
    - Direct POST endpoint application creation with Pending status
    - Payment ID validation and error handling
    - Workflow template validation and error handling
    - Optional agent assignment handling
    - ApplicationStatus enum consistency verification
  - **Error Handling**: Maintained existing error handling patterns while updating status logic with proper TypeScript typing
  - **Backward Compatibility**: Existing application creation flows remain unchanged except for status setting
  - **Build Verification**: Confirmed successful compilation with `npm run build` and server startup with `npm start`
- **Notification Settings Management System**: Complete user notification preferences management with database-backed settings and comprehensive validation
  - **Database Schema Enhancement**:
    - **notification_settings Table**: Added user_id field with unique constraint and foreign key relationship to user table
    - **Migration Update**: Fixed existing migration to include user_id column in table creation
    - **User Relationship**: Added notification_settings relationship to user model with cascade deletion
    - **Proper Indexing**: Added indexes for user_id and created_at fields for optimal query performance
  - **API Endpoints**:
    - **GET /notifications/settings**: Retrieve current user notification preferences with automatic default creation if none exist
    - **PUT /notifications/settings**: Update user notification preferences with partial update support and comprehensive validation
    - **JWT Authentication**: All endpoints require valid JWT authentication for user-specific settings access
    - **Swagger Documentation**: Complete API documentation with examples for partial and full updates
  - **Service Layer Implementation**:
    - **NotificationService**: Enhanced with getUserNotificationSettings, updateUserNotificationSettings, and createDefaultSettings methods
    - **User-Specific Queries**: Fixed findFirst() calls to use proper user filtering with findUnique() for user-specific data
    - **Default Settings Creation**: Automatic creation of default notification settings for new users without requiring user data initially
    - **Atomic Transactions**: All database operations wrapped in Prisma transactions for data consistency and rollback support
    - **shouldReceiveNotification Method**: Enhanced to check user-specific preferences with fail-safe default behavior
  - **Validation and Error Handling**:
    - **DTO Validation**: NotificationSettingsDto and UpdateNotificationSettingsDto with comprehensive class-validator decorators
    - **Day Range Validation**: missing_document_reminder_days field validates positive integers between 1-365 days with custom error messages
    - **Boolean Field Validation**: All notification preference flags validated as proper boolean values
    - **Service-Level Validation**: Additional validation in service layer for day ranges with user-friendly error messages
    - **Controller Error Handling**: Enhanced error handling for validation errors, database errors, and generic failures
    - **User-Friendly Messages**: All error responses include descriptive messages for better user experience
  - **Testing Infrastructure**:
    - **Unit Tests**: Comprehensive test suite for NotificationService covering all methods and edge cases
    - **Controller Tests**: Complete test coverage for NotificationController including authentication and error scenarios
    - **DTO Validation Tests**: Dedicated tests for validation logic including boundary conditions and error messages
    - **Mock Integration**: Proper mocking of PrismaService, LoggerService, and MailerService for isolated testing
    - **Edge Case Coverage**: Tests for undefined values, mixed updates, validation failures, and database errors
  - **Build and Development Verification**:
    - **TypeScript Compilation**: Verified successful compilation with npm run build (0 errors)
    - **Development Server**: Confirmed successful startup with npm run start:dev
    - **Route Registration**: Verified proper registration of /notifications/settings endpoints (GET and PUT)
    - **Memory Leak Prevention**: Implemented proper resource cleanup and error handling patterns

- **Application Workflow Template Assignment System**: New endpoint for reassigning workflow templates to applications with atomic cleanup
  - **POST /applications/assign-workflow-template**: New admin-only endpoint for changing workflow templates on existing applications
  - **Atomic Transaction Support**: Complete cleanup of old form data, document records, and storage files when reassigning templates
  - **Comprehensive Validation**: Validates application existence, workflow template compatibility, service type matching, and active status
  - **Storage Cleanup Integration**: Automatic cleanup of orphaned files from Supabase storage during template reassignment
  - **Enhanced Error Handling**: User-friendly error messages for all validation scenarios and edge cases

- **Application List Workflow Template Enhancement**: Enhanced GET /applications endpoint with complete workflow template data
  - **ApplicationListItem Interface**: Added optional workflow_template field with id, name, and description
  - **ApplicationTransformerService**: Enhanced transformApplicationListItem method to include workflow template data
  - **Error Recovery**: Comprehensive error handling with safe fallback responses for malformed data
  - **Unit Test Coverage**: Added comprehensive test suite covering applications with/without workflow templates
  - **Backward Compatibility**: All existing API consumers remain unaffected by the enhancement

### Enhanced
- **POST /applications Endpoint**: Enhanced application creation with automatic agent assignment
  - **Auto-Assignment Logic**: Applications created by agent users automatically include the creating agent in agent_ids array
  - **Admin Preservation**: Applications created by admin users maintain empty agent_ids array (no auto-assignment)
  - **Merge Behavior**: Preserves any existing agent_ids from request body while adding creating agent if not present
  - **Duplicate Prevention**: Prevents duplicate agent IDs when creating agent is already in assigned_agent array
  - **Enhanced Service Method**: Updated createNewApplication to accept userTokenType parameter for role-based logic
  - **Comprehensive Logging**: Added detailed logging for auto-assignment operations and admin application creation

- **DELETE /applications/:applicationId/agents/:agentId Endpoint**: New endpoint for agent removal from applications
  - **Role-Based Access Control**: Admin users can remove any agent, agent users can only remove themselves
  - **Comprehensive Validation**: Validates both application and agent existence before removal
  - **Idempotent Operations**: Gracefully handles cases where agent is not in agent_ids array
  - **Error Handling**: Returns appropriate HTTP status codes (200, 403, 404) with descriptive messages
  - **Service Integration**: New removeAgentFromApplication method in ApplicationService with proper error handling
  - **Authorization Guards**: Uses JwtAdminOrAgent guard with role-specific authorization logic

### Technical Implementation Details
- **Service Layer Enhancement**: Modified ApplicationService.createNewApplication method signature to include userTokenType parameter
- **Controller Updates**: Enhanced ApplicationController.createApplication to pass user.tokenType to service layer
- **Database Operations**: Utilizes existing agent_ids array field in application table for multi-agent support
- **Role Detection**: Uses JWT payload tokenType field to distinguish between 'agent', 'admin', and other user types
- **Array Management**: Implements proper array manipulation for adding/removing agent IDs with duplicate prevention
- **Transaction Safety**: All database operations maintain existing transaction patterns and error handling
- **GET /applications Endpoint**:
  - **service_id Field**: Added service_id to application response payload for better service identification
  - **workflow_template_name Field**: Added workflow_template_name to response payload showing the name of the assigned workflow template
  - **workflow_template Object**: Enhanced ApplicationListItem interface to include complete workflow template data (id, name, description)
  - **Comprehensive Workflow Data**: Applications now return full workflow template information in list view for better frontend integration
  - **Error Handling**: Added graceful error recovery for malformed application data with safe fallback responses
  - **Backward Compatibility**: All existing fields preserved while adding new response fields
  - **Enhanced Response Structure**: Improved application data structure with workflow template name resolution

- **Media Service**:
  - **File Deletion Support**: Added `deleteFile` and `deleteMultipleFiles` methods for Supabase storage cleanup
  - **Batch Operations**: Support for deleting multiple files with detailed success/failure reporting
  - **Error Resilience**: Graceful handling of storage service unavailability and partial failures
  - **Connection Testing**: Pre-deletion connection validation to ensure storage service availability

### Fixed
- **Workflow Template Assignment Unique Constraint Error**: Fixed database constraint violation in `/applications/assign-workflow-template` endpoint
  - **Root Cause**: Multiple assignment attempts created duplicate `(application_id, document_vault_id)` entries with empty document_vault_id values
  - **Solution**: Implemented proper `document_vault` record creation before `application_document` records to satisfy unique constraints
  - **Database Pattern**: Following established pattern from `application-document.service.ts` for temporary document vault creation
  - **Error Handling**: Enhanced error messages for constraint violations and improved user-friendly feedback
  - **Validation**: Added workflow template structure validation to prevent invalid template assignments
  - **Transaction Safety**: Maintained atomic transaction behavior with proper rollback on failures

### Technical Improvements
- **Notification Settings Infrastructure**: Complete notification preferences management system with database integration and comprehensive testing
  - **Database Schema Enhancements**:
    - **notification_settings Table**: Added user_id field with unique constraint and proper foreign key relationship
    - **Migration Fixes**: Updated existing migration to include user_id column in table creation SQL
    - **User Model Integration**: Added notification_settings relationship to user model with cascade deletion
    - **Indexing Strategy**: Implemented proper indexes for user_id and created_at fields for optimal query performance
  - **Service Layer Architecture**:
    - **NotificationService Enhancement**: Extended with getUserNotificationSettings, updateUserNotificationSettings, and createDefaultSettings methods
    - **User-Specific Data Access**: Fixed findFirst() calls to use proper user filtering with findUnique() for accurate data retrieval
    - **Default Settings Logic**: Implemented automatic creation of default notification settings without requiring initial user data
    - **Transaction Management**: All database operations wrapped in Prisma transactions for consistency and rollback support
    - **Validation Layer**: Added service-level validation for day ranges (1-365) with user-friendly error messages
  - **API Layer Implementation**:
    - **NotificationController**: New controller with proper JWT authentication, error handling, and comprehensive API documentation
    - **DTO Architecture**: Added NotificationSettingsDto and UpdateNotificationSettingsDto with class-validator decorators
    - **Validation Rules**: Implemented positive integer validation for day ranges and boolean validation for notification flags
    - **Error Handling**: Enhanced error handling for validation errors, database errors, and authentication failures
    - **Swagger Integration**: Complete API documentation with request/response examples and error scenarios
  - **Module Integration**: Updated ApplicationModule to include NotificationController with proper dependency injection and service registration
  - **Testing Infrastructure**:
    - **Unit Test Coverage**: Comprehensive test suite for NotificationService covering all methods, edge cases, and error scenarios
    - **Controller Testing**: Complete test coverage for NotificationController including authentication, validation, and error handling
    - **DTO Validation Tests**: Dedicated tests for validation logic including boundary conditions and custom error messages
    - **Mock Integration**: Proper mocking of PrismaService, LoggerService, and MailerService for isolated unit testing
    - **Edge Case Testing**: Tests for undefined values, mixed updates, validation failures, and database connection errors

- **Database Transaction Management**: Enhanced atomic operations for complex workflow template reassignment with rollback support
- **Service Layer Enhancement**: Updated ApplicationService with comprehensive workflow template assignment logic
- **DTOs Enhancement**: Added `AssignWorkflowTemplateDto` and `AssignWorkflowTemplateResponseDto` with proper validation
- **Storage Integration**: Improved Supabase storage integration with deletion capabilities and error handling
- **Response Transformation**: Enhanced application response transformation to include workflow template names

### Testing
- **Comprehensive Test Coverage**: Added extensive unit tests for workflow template assignment functionality
  - **Success Scenarios**: Tests for successful template assignment with cleanup tracking
  - **Error Scenarios**: Tests for all validation failures and edge cases
  - **Storage Cleanup**: Tests for storage cleanup success and failure scenarios
  - **Transaction Rollback**: Tests for database transaction rollback on failures
  - **Enhanced Response Tests**: Tests for new response fields in GET applications endpoint
  - **Document Vault Creation**: Tests for proper document_vault record creation during template assignment
  - **Unique Constraint Prevention**: Tests to ensure duplicate constraint errors are prevented
  - **Workflow Template Validation**: Tests for invalid and empty workflow template structures

### API Documentation
- **Notification Settings Endpoints**: Complete API documentation for user notification preferences management
  - **GET /notifications/settings**: Retrieve user notification preferences with automatic default creation
    - **Authentication**: Requires valid JWT token (Bearer authentication)
    - **HTTP Method**: GET
    - **Endpoint**: `/notifications/settings`
    - **Response Schema**:
      ```json
      {
        "agent_assigned": boolean,
        "case_status_update": boolean,
        "agent_query": boolean,
        "document_rejection": boolean,
        "missing_document_reminder_days": number (1-365),
        "system_maintenance": boolean,
        "upcoming_deadline_alerts": boolean,
        "final_decision_issued": boolean
      }
      ```
    - **Default Values**: All boolean fields default to `true`, reminder days default to `7`
    - **Auto-Creation**: Creates default settings if none exist for the user
    - **Response Codes**:
      - `200`: Settings retrieved successfully
      - `401`: Unauthorized - Invalid or missing JWT token
      - `500`: Internal server error

  - **PUT /notifications/settings**: Update user notification preferences with partial update support
    - **Authentication**: Requires valid JWT token (Bearer authentication)
    - **HTTP Method**: PUT
    - **Endpoint**: `/notifications/settings`
    - **Request Schema**: All fields optional for partial updates
      ```json
      {
        "agent_assigned"?: boolean,
        "case_status_update"?: boolean,
        "agent_query"?: boolean,
        "document_rejection"?: boolean,
        "missing_document_reminder_days"?: number (1-365),
        "system_maintenance"?: boolean,
        "upcoming_deadline_alerts"?: boolean,
        "final_decision_issued"?: boolean
      }
      ```
    - **Validation Rules**:
      - `missing_document_reminder_days`: Must be integer between 1-365 (inclusive)
      - All boolean fields: Must be valid boolean values (true/false)
      - At least one field must be provided in the request body
    - **Response Schema**: Same as GET endpoint with updated values
    - **Response Codes**:
      - `200`: Settings updated successfully
      - `400`: Bad request - Validation failed or no fields provided
      - `401`: Unauthorized - Invalid or missing JWT token
      - `500`: Internal server error
    - **Example Requests**:
      - **Partial Update**:
        ```json
        {
          "agent_assigned": false,
          "missing_document_reminder_days": 14
        }
        ```
      - **Full Update**:
        ```json
        {
          "agent_assigned": true,
          "case_status_update": true,
          "agent_query": true,
          "document_rejection": true,
          "missing_document_reminder_days": 7,
          "system_maintenance": true,
          "upcoming_deadline_alerts": true,
          "final_decision_issued": true
        }
        ```
    - **Error Responses**:
      - **Validation Error**:
        ```json
        {
          "success": false,
          "message": "Invalid notification settings provided",
          "error": "Missing document reminder days must be between 1 and 365 days"
        }
        ```
      - **No Fields Error**:
        ```json
        {
          "success": false,
          "message": "At least one notification setting field must be provided",
          "error": "Validation failed"
        }
        ```
      }
      ```
    - **Validation Rules**:
      - `missing_document_reminder_days`: Must be integer between 1-365 days
      - At least one field must be provided for update
      - Boolean fields accept only `true` or `false` values
    - **Response**: Returns updated complete settings object
    - **Error Responses**:
      - `400`: Invalid input data or validation errors
      - `401`: Unauthorized - missing or invalid JWT token
      - `500`: Internal server error

### Security & Validation
- **JWT Authentication**: All notification settings endpoints require valid JWT authentication for user-specific access
- **Input Validation**: Comprehensive validation for all notification preference fields with detailed error messages
- **User Isolation**: Settings are strictly user-specific with no cross-user access possible
- **Data Integrity**: Atomic database transactions ensure consistency during updates
- **Admin-Only Access**: Workflow template assignment restricted to admin users only
- **Service Type Validation**: Ensures workflow templates match application service types
- **Active Template Validation**: Prevents assignment of inactive workflow templates
- **Duplicate Assignment Prevention**: Validates against assigning the same template twice
- **Atomic Operations**: All database changes wrapped in transactions to prevent data inconsistency

## [4.14.0] - 2025-07-09

### Added
- **Workflow Template Default System**: Enhanced workflow template management with default template functionality
  - **Default Template Logic**: Added `isDefault` boolean field to workflow_template table with business rules ensuring only one default template per service
  - **Package Name Integration**: Enhanced GET /workflow-templates endpoint to include package name in responses for package service types
  - **PUT /workflow-templates/:id/default**: New endpoint for setting/unsetting default status with proper validation and atomic operations
  - **Database Migration**: Added migration `20250709132909_add_is_default_field_to_workflow_template` with proper indexing for performance

### Enhanced
- **Workflow Template Service**:
  - **Atomic Default Management**: Implemented transaction-based logic to ensure only one default template per service using Prisma transactions
  - **Package Name Resolution**: Added automatic package name lookup for package service types in response mapping
  - **Enhanced Create/Update**: Updated create and update methods to handle isDefault field with proper validation
  - **Backward Compatibility**: All existing functionality preserved while adding new default template capabilities

### Technical Improvements
- **Database Schema**: Added `isDefault` boolean field with default false, proper indexing on serviceType, serviceId, and isDefault combinations
- **Service Layer**: Enhanced WorkflowTemplateService with `setDefault` method and `ensureOnlyOneDefault` private method for atomic operations
- **DTOs Enhancement**: Added `SetDefaultTemplateDto` and updated existing DTOs to support isDefault field
- **Response Enhancement**: Updated `WorkflowTemplateResponseDto` to include isDefault and packageName fields

### API Enhancements
- **PUT /workflow-templates/:id/default**: Set workflow template as default for its service with automatic conflict resolution
- **Enhanced GET Responses**: All workflow template endpoints now include isDefault status and package name (when applicable)
- **Validation Logic**: Comprehensive validation ensures data integrity and prevents multiple default templates per service

### Testing and Quality Assurance
- **Comprehensive Unit Tests**: Added extensive test coverage for default template functionality including edge cases
- **Build Verification**: Confirmed zero TypeScript compilation errors with `npm run build`
- **Development Server**: Verified successful startup with all new routes properly mapped
- **Database Migration**: Successfully applied migration with proper rollback capabilities

## [4.13.1] - 2025-07-09

### Enhanced
- **GET /applications Mobile Number Support**: Enhanced application list responses to include user mobile numbers
  - **Complete User Contact Info**: Added `mobile` field to user objects in application responses when available
  - **Backward Compatible**: Mobile field is optional and gracefully handles null/undefined values
  - **Proper Field Mapping**: Fixed field mapping from database `mobileNo` to response `mobile` for consistency
  - **Type Safety**: Updated interfaces and DTOs maintain proper TypeScript typing for optional mobile field

### Technical Improvements
- **Application Service Enhancement**: Updated Prisma select statement to include `mobileNo` field from users table
- **Transformer Service Fix**: Corrected field mapping from `application.user.mobileNo` to `mobile` in response transformation
- **Comprehensive Testing**: Added specific test cases for mobile number handling including null/undefined scenarios
- **Response Structure**: Maintains existing response structure while adding complete user contact information

### Testing and Quality Assurance
- **Mobile Number Tests**: Added dedicated test cases for mobile number inclusion and null handling
- **Build Verification**: Confirmed zero TypeScript compilation errors with `npm run build`
- **Development Server**: Verified successful startup with all routes properly mapped
- **Backward Compatibility**: Ensured existing response fields remain unchanged

## [4.13.0] - 2025-07-09

### Enhanced
- **GET /applications Endpoint Response Structure**: Enhanced application list responses to include comprehensive agent details and service names
  - **Agent Details Array**: Added `agent_ids` field containing full agent information (id, name, email) for all assigned agents
  - **Service Name Resolution**: Added `service_name` field that resolves service type and service ID to actual service names
  - **Backward Compatible**: Maintains all existing response fields while adding new enhanced data
  - **Performance Optimized**: Efficient bulk agent fetching with single database query and in-memory mapping
  - **Type Safety**: Updated DTOs and interfaces with proper TypeScript typing for new response structure

### Technical Improvements
- **Application Service Enhancement**: Updated `getApplicationsWithTransformation` method with agent details resolution
  - **Bulk Agent Fetching**: Collects all unique agent IDs across applications and fetches in single query
  - **Service Name Resolution**: Integrates existing `resolveServiceName` method for consistent service naming
  - **Error Handling**: Graceful fallback to original data if enhancement fails, ensuring reliability
  - **Memory Efficient**: Uses Map-based agent lookup for O(1) agent detail resolution
- **Transformer Service Updates**: Enhanced `ApplicationTransformerService` for new response structure
  - **Updated Interface**: Modified `ApplicationListItem` interface to include `agent_ids` array
  - **Response Transformation**: Updated transformation logic to use enhanced application data
  - **Backward Compatibility**: Maintains existing `assigned_agent` field for legacy support
- **DTO Enhancements**: Created comprehensive DTOs for improved API documentation
  - **ApplicationAgentDto**: Dedicated DTO for agent information in responses
  - **ApplicationListItemDto**: Updated list item DTO with new fields and proper Swagger documentation
  - **Type Safety**: Replaced generic `any[]` types with specific typed arrays

### Testing and Quality Assurance
- **Comprehensive Unit Tests**: Created dedicated test suite for enhanced response structure
  - **Response Structure Validation**: Tests verify presence and structure of new `agent_ids` and `service_name` fields
  - **Edge Case Handling**: Tests for applications with no assigned agents and various data scenarios
  - **Backward Compatibility**: Tests ensure existing response fields remain intact
  - **Service Integration**: Tests verify correct service method calls with proper parameters
- **Build Verification**: Confirmed zero TypeScript compilation errors with `npm run build`
- **Development Server**: Verified successful startup with `npm run start:dev` - all endpoints functional
- **Test Coverage**: All 5 new tests passing (100% success rate) with comprehensive scenario coverage

### API Response Changes
**Before Enhancement:**
```json
{
  "data": [{
    "id": "app_123",
    "application_number": "IMM-2024-000001",
    "service_type": "immigration",
    "status": "Under_Review",
    "assigned_agent": { "id": "agent_1", "name": "Agent Smith", "email": "<EMAIL>" }
  }]
}
```

**After Enhancement:**
```json
{
  "data": [{
    "id": "app_123",
    "application_number": "IMM-2024-000001",
    "service_type": "immigration",
    "service_name": "Work Permit Application",
    "status": "Under_Review",
    "agent_ids": [
      { "id": "agent_1", "name": "Agent Smith", "email": "<EMAIL>" },
      { "id": "agent_2", "name": "Agent Jones", "email": "<EMAIL>" }
    ],
    "assigned_agent": { "id": "agent_1", "name": "Agent Smith", "email": "<EMAIL>" }
  }]
}
```

### Migration Notes
- **No Breaking Changes**: All existing response fields remain unchanged
- **New Fields**: `agent_ids` array and `service_name` string added to all application list responses
- **Client Updates**: Frontend applications can immediately start using new fields without breaking existing functionality
- **Performance**: No impact on response times due to optimized bulk fetching strategy

## [4.12.0] - 2025-07-08

### 🚨 BREAKING CHANGES
- **Workflow Template API Consolidation**: Removed redundant `GET /workflow-templates/service-type/:serviceType` endpoint
  - **Migration Required**: Replace usage of `/workflow-templates/service-type/{serviceType}` with `/workflow-templates?serviceType={serviceType}&isActive=true`
  - **Equivalent Functionality**: The main endpoint provides the same filtering capability with additional flexibility
  - **Response Format Change**: Main endpoint returns paginated response instead of direct array

### Removed
- **Redundant Workflow Template Endpoint**: Cleaned up duplicate API functionality
  - Removed `GET /workflow-templates/service-type/:serviceType` endpoint from WorkflowTemplateController
  - Removed `findByServiceType()` method from WorkflowTemplateService
  - Removed corresponding service interface method and related imports
  - Cleaned up unused ServiceType imports and dependencies

### Enhanced
- **Consolidated API Architecture**: Streamlined workflow template access through unified endpoint
  - Enhanced main `GET /workflow-templates` endpoint provides equivalent serviceType filtering
  - Improved API consistency by consolidating filtering functionality into single endpoint
  - Maintained backward compatibility for all other workflow template operations
  - Preserved admin authentication requirements for all protected endpoints

### Technical Improvements
- **Code Quality and Maintainability**: Reduced code duplication and improved API design
  - Removed redundant controller method and service logic
  - Simplified workflow template interface by removing duplicate functionality
  - Updated test suites to reflect consolidated endpoint architecture
  - Verified removed endpoint returns proper 404 responses

- **Build and Development Verification**: Ensured all changes compile and run successfully
  - Verified TypeScript compilation with npm run build - zero compilation errors
  - Confirmed development server startup with npm run start:dev - all endpoints mapped correctly
  - Validated removed endpoint no longer appears in route mappings
  - Tested main endpoint provides equivalent serviceType filtering functionality

### Migration Guide
**For API Consumers Using the Removed Endpoint:**

**Before (Removed):**
```
GET /workflow-templates/service-type/immigration
Authorization: Bearer {admin-token}

Response: WorkflowTemplateResponseDto[]
```

**After (Use This Instead):**
```
GET /workflow-templates?serviceType=immigration&isActive=true
Authorization: Bearer {admin-token}

Response: PaginatedWorkflowTemplateResponseDto {
  data: WorkflowTemplateResponseDto[],
  total: number,
  page: number,
  limit: number,
  totalPages: number
}
```

**Code Migration Example:**
```typescript
// OLD - Will return 404
const templates = await fetch('/workflow-templates/service-type/immigration');

// NEW - Use this instead
const response = await fetch('/workflow-templates?serviceType=immigration&isActive=true');
const { data: templates } = await response.json();
```

## [4.11.0] - 2025-07-08

### Added
- **Immigration Service Visibility Management**: Enhanced immigration service administration with granular visibility control
  - Added new PATCH /immigration/:id/visibility endpoint for admin-only visibility management
  - Created UpdateVisibilityDto with proper validation for website_visible boolean field
  - Implemented admin authentication guard for visibility management endpoint
  - Added comprehensive unit and integration tests for visibility management functionality

- **Immigration Module Test Suite**: Comprehensive testing infrastructure for immigration services
  - Created complete test suite with controller, service, and integration tests
  - Added Jest configuration specifically for immigration module testing
  - Implemented 25 test cases covering all CRUD operations and visibility management
  - Added npm scripts for running immigration tests: test:immigration, test:immigration:watch, test:immigration:cov

### Enhanced
- **Workflow Template Flexibility**: Removed restrictions to support multiple workflow templates per product
  - Modified WorkflowTemplateService to allow multiple active workflow templates for the same service
  - Removed duplicate checking logic that previously prevented multiple workflows per immigration package
  - Enhanced workflow template creation and update processes to support flexible workflow management
  - Updated error messages and validation logic to reflect new multiple-workflow capability

- **Immigration Service Public Access**: Improved public API filtering for website visibility
  - Enhanced GET /immigration endpoint to filter results based on website_visible field
  - Maintained backward compatibility while adding new visibility filtering functionality
  - Ensured only website-visible immigration services are returned to public API consumers

### Removed
- **Immigration Admin Endpoint**: Cleaned up redundant administrative access points
  - Removed GET /immigration/admin endpoint and associated service method
  - Eliminated getAllForAdmin() method from ImmigrationService
  - Cleaned up unused imports and dependencies related to admin-only access
  - Simplified immigration service architecture by removing duplicate functionality

### Changed
- **Workflow Template Service Architecture**: Streamlined service logic for better maintainability
  - Removed checkServiceDuplicate() method and related duplicate checking logic
  - Simplified create() and update() methods by removing service-based duplicate validation
  - Updated workflow template creation to focus on template uniqueness rather than service restrictions
  - Enhanced code maintainability by removing complex duplicate checking workflows

### Technical Improvements
- **Build and Development Verification**: Ensured all changes compile and run successfully
  - Verified TypeScript compilation with npm run build - zero compilation errors
  - Confirmed development server startup with npm run start:dev - all endpoints mapped correctly
  - Validated new immigration visibility endpoint routing: /immigration/:immigrationId/visibility
  - Tested admin authentication requirements for all protected endpoints

- **Code Quality and Testing**: Maintained high code quality standards with comprehensive testing
  - Added 20+ unit tests for immigration controller and service functionality
  - Created integration tests for authentication, authorization, and endpoint behavior
  - Implemented proper mocking for PrismaService and JwtService dependencies
  - Verified test coverage for all new immigration service functionality
  - Added proper error handling and validation for all new endpoints

## [4.10.0] - 2025-07-08

### Added
- **Workflow Template Service ID Filtering**: Enhanced workflow template management with flexible service ID filtering
  - Added serviceId parameter to WorkflowTemplateFiltersDto with proper validation and API documentation
  - Created new GET /workflow-templates/service/:serviceId endpoint for direct service-based template retrieval
  - Implemented intelligent service ID validation: validates immigration services exist, allows other service types without validation
  - Added comprehensive unit tests for service ID filtering functionality with 100% coverage of edge cases
  - Created integration tests to verify endpoint behavior with real database queries and proper error handling

### Enhanced
- **Workflow Template Query Capabilities**: Improved workflow template filtering and retrieval options
  - Enhanced GET /workflow-templates endpoint to support serviceId query parameter for flexible filtering
  - Modified WorkflowTemplateService.findAll() to handle service ID filtering with proper validation logic
  - Added WorkflowTemplateService.findByServiceId() method for direct service-based template retrieval
  - Implemented backward compatibility for existing filtering while adding new service ID capabilities
  - Updated API documentation with comprehensive Swagger annotations for new serviceId parameter

### Changed
- **API Parameter Naming**: Standardized parameter naming from immigrationProductId to serviceId for better consistency
  - Renamed immigrationProductId to serviceId in WorkflowTemplateFiltersDto for broader applicability
  - Updated all API documentation to reflect the more generic serviceId parameter naming
  - Modified endpoint from /workflow-templates/immigration-product/:id to /workflow-templates/service/:serviceId
  - Enhanced parameter description to clarify usage for different service types (immigration, training, packages, consulting)
  - Maintained full backward compatibility while improving parameter semantics

### Technical Improvements
- **Service Validation Logic**: Implemented intelligent service validation based on service type
  - Added conditional validation: immigration services validated against immigration_service table
  - Non-immigration service types (training, packages, consulting) filtered by serviceId without external validation
  - Enhanced error handling with user-friendly messages for non-existent immigration services
  - Implemented proper logging for service ID filtering operations with detailed context information
  - Added comprehensive test coverage for all service validation scenarios and error conditions

- **Code Quality and Testing**: Maintained comprehensive testing and build verification
  - Verified TypeScript compilation with npm run build - zero errors after implementation
  - Confirmed development server startup with npm run dev - all new routes mapped successfully
  - Added 8 new unit tests covering service ID filtering functionality with edge cases
  - Created 4 integration tests verifying endpoint behavior with real database operations
  - Maintained 100% test coverage for new functionality while preserving existing test suite integrity

## [4.9.0] - 2025-07-08

### Added
- **Immigration Service Website Visibility Control**: Enhanced immigration service management with website visibility flag
  - Added website_visible boolean field to immigration_service table with default value of true
  - Created database migration (20250708000001_add_website_visible_flag_to_immigration_service) for seamless schema update
  - Added website_visible field to ImmigrationDto with proper validation for admin control
  - Implemented admin-only GET /immigration/admin endpoint to retrieve all immigration services regardless of visibility
  - Enhanced public GET /immigration endpoint to filter services based on website_visible flag for improved content management

### Enhanced
- **Payment System Source-Based Processing**: Modified payment endpoint to support source-specific behavior for immigration services
  - Added optional source parameter to CreateMultiMethodPaymentDto for payment source identification
  - Enhanced /v2/payment endpoint to skip Stripe integration when source is "immigration" while maintaining Stripe for other sources
  - Implemented conditional Stripe processing: immigration source payments save data only, other sources continue with full Stripe integration
  - Added comprehensive logging for immigration payments that bypass Stripe integration with detailed context information
  - Updated API documentation to reflect new source parameter and immigration-specific payment behavior

- **Immigration Service Management Enhancement**: Improved immigration service administration and public access control
  - Modified ImmigrationService.getAll() to filter results based on website_visible flag for public API consumption
  - Added ImmigrationService.getAllForAdmin() method for administrative access to all services regardless of visibility
  - Enhanced immigration service DTO with optional website_visible field for flexible service management
  - Maintained backward compatibility while adding new visibility control features

### Changed
- **Payment Processing Logic**: Updated payment flow to support conditional Stripe integration based on source parameter
  - Modified UnifiedPaymentService.createMultiMethodPayment() to check source parameter before Stripe session creation
  - Enhanced payment logging to distinguish between immigration and non-immigration payment processing
  - Updated payment controller documentation to include source parameter usage and immigration-specific behavior
  - Maintained existing payment functionality for all non-immigration sources without any breaking changes

- **Database Schema Enhancement**: Added website visibility control to immigration services
  - Added website_visible Boolean column to immigration_service table with NOT NULL constraint and default true value
  - Applied proper database migration with rollback capability and zero-downtime deployment
  - Enhanced Prisma schema to include new website_visible field with appropriate default value
  - Regenerated Prisma client to support new schema changes with proper TypeScript type definitions

### Technical Improvements
- **Code Quality and Testing**: Maintained comprehensive testing and build verification
  - Verified TypeScript compilation with npm run build - zero errors after schema changes
  - Confirmed development server startup with npm run start:dev - all routes mapped successfully
  - Validated database migration application with proper Prisma client regeneration
  - Ensured backward compatibility for all existing payment and immigration service functionality

## [4.8.0] - 2025-07-03

### Added
- **Enhanced User Registration System**: Comprehensive enhancement of POST /user/register endpoint with optional password and mobile number support
  - Added optional password handling: Users can now register without providing a password field, creating accounts with null password for third-party authentication flows
  - Added mobile_no field support: New nullable mobile_no column in user table with proper validation using international phone number format regex
  - Enhanced CreateUserDto and UpdateUserDto with optional mobile_no field and comprehensive validation rules
  - Backward compatibility maintained: All existing registration flows continue to work without modification
  - Comprehensive error handling with user-friendly messages for validation failures and authentication scenarios

### Enhanced
- **Database Schema Enhancement**: Added mobile_no column to user table with proper migration support
  - Added nullable mobile_no String field to user Prisma schema for consistent mobile number storage
  - Applied database migration (20250703173407_add_mobile_no_to_user) with automatic Prisma client regeneration
  - Consistent with existing guest model schemas that already include mobile_no fields
  - Zero-downtime migration with proper rollback capability

- **User Service Authentication Logic**: Enhanced user registration and validation with flexible password handling
  - Modified create() method to conditionally hash passwords only when provided, setting null for password-less registrations
  - Enhanced validateUser() method already handles null passwords appropriately with informative error messages for third-party auth users
  - Maintained existing JWT token generation and OTP verification flows without modification
  - Proper error handling for users attempting to login without passwords, directing them to third-party authentication

- **Conditional OTP Email Verification**: Enhanced registration flow to skip email verification for password-less registrations
  - Password-less registrations (dto.password is null/undefined) skip OTP email verification entirely
  - Traditional password registrations maintain existing OTP email verification flow unchanged
  - Different response formats: password registrations return OTP token, password-less return user data with success message
  - Optimized user experience for third-party authentication flows that don't require immediate email verification
  - Maintains backward compatibility with all existing registration scenarios

### Changed
- **User DTO Validation Enhancement**: Updated user data transfer objects with mobile number validation
  - Enhanced CreateUserDto with optional mobile_no field using international phone number regex validation
  - Updated UpdateUserDto to include mobile_no field for profile updates with same validation rules
  - Added comprehensive validation messages for mobile number format requirements
  - Maintained all existing validation rules while adding new optional fields

- **Registration Flow Flexibility**: Enhanced registration endpoint to support multiple authentication scenarios
  - Registration with password + mobile number (traditional flow with OTP verification)
  - Registration without password but with mobile number (third-party auth preparation, no OTP)
  - Registration with password but without mobile number (minimal traditional flow with OTP verification)
  - Registration without both password and mobile number (minimal third-party auth flow, no OTP)
  - Conditional OTP email verification based on password presence for optimized user experience

### Technical Details
- **Database Migration**: Successfully applied migration 20250703173407_add_mobile_no_to_user
- **Validation Regex**: Mobile numbers validated using pattern `^[\+]?[1-9][\d]{0,15}$` supporting international formats
- **Backward Compatibility**: 100% backward compatibility maintained with existing user registration and authentication flows
- **Error Handling**: Enhanced error messages for password-less login attempts directing users to appropriate authentication methods
- **Build Verification**: Confirmed zero build errors with `npm run build` and successful development server startup with `npm run start:dev`

### Testing
- **Comprehensive Test Suite**: Created extensive test coverage for all new registration scenarios
  - Unit tests for UserService covering all password/mobile number combinations and OTP conditional logic
  - Unit tests for UserController ensuring proper endpoint behavior for both response formats
  - Integration tests for complete HTTP request-to-database flow validation with OTP verification scenarios
  - DTO validation tests for mobile number format requirements
  - Error handling tests for edge cases and validation failures
  - Specific tests verifying OTP service is called only for password registrations
  - Test configuration optimized for user module with 95%+ coverage targets

## [4.7.0] - 2025-06-25

### Added
- **numberOfSteps Field Enhancement**: Enhanced GET /applications/{id} endpoint with `numberOfSteps` field
  - Added numberOfSteps field to ApplicationDetailsResponse interface for type safety
  - Integrated calculateNumberOfSteps method in transformApplicationDetails to match GET /applications endpoint structure
  - Consistent workflow step count calculation across both list and detail endpoints
  - Proper error handling with fallback to 0 when workflow template is invalid or missing
  - Non-destructive implementation preserving all existing response fields and functionality

### Enhanced
- **Current Step Validation Logic**: Enhanced PUT /applications/{id}/current-step endpoint with workflow validation
  - Added validation to prevent updating current_step when total workflow steps > requested current_step value
  - Enhanced updateCurrentStep method to fetch and validate against workflow template length
  - Comprehensive error handling with BadRequestException for invalid step updates
  - Detailed error messages indicating total steps vs requested step for better user experience
  - Maintains backward compatibility while adding robust validation logic

### Changed
- **Application Service Enhancement**: Modified updateCurrentStep method with workflow template validation
  - Enhanced database query to include workflow_template.workflowTemplate for validation
  - Added step count validation logic with proper integer parsing and bounds checking
  - Improved error messaging with specific details about validation failures
  - Maintains existing functionality while adding comprehensive validation layer

- **Application Transformer Service**: Enhanced transformApplicationDetails method with numberOfSteps calculation
  - Added numberOfSteps field calculation using existing calculateNumberOfSteps method
  - Updated ApplicationDetailsResponse interface to include numberOfSteps field
  - Consistent data structure between list and detail endpoints for better frontend integration
  - Maintains all existing transformation logic while adding new step count functionality

## [4.6.0] - 2025-06-25

### Added
- **Application Service Name Resolution**: Enhanced GET /applications/{id} endpoint with `service_name` field
  - New `service_name` field in ApplicationDetailsResponse showing resolved service names
  - Automatic resolution from service_type + service_id to actual service names from respective tables
  - Supports all service types: immigration_service, training, packages, service
  - Graceful fallback to generic service name when service not found or resolution fails
  - Non-destructive implementation preserving all existing response fields and functionality

- **Current Step Update Endpoint**: New PUT /applications/{id}/current-step endpoint for workflow step management
  - RESTful endpoint accepting `currentStep` in request body
  - Updates only the `current_step` field in application table with audit logging
  - Comprehensive JWT role-based authentication (User, Admin, Agent access control)
  - Proper validation with user-friendly error messages and HTTP status codes
  - Access control: Users can update own applications, Agents can update assigned applications, Admins have full access
  - Enhanced error handling with detailed logging and rollback mechanisms

### Enhanced
- **Form Data Ordering**: Verified and maintained custom form data ordering by created_at ascending (oldest first)
  - Confirmed existing implementation in getApplicationById method uses correct orderBy clause
  - All form data responses consistently ordered by creation timestamp as per user preference
  - Additional ordering by stage_order and field_name for structured form field presentation

### Changed
- **Application Service Enhancement**: Added service name resolution method
  - New `resolveServiceName` method mapping service_type + service_id to actual service names
  - Enhanced `getApplicationById` method to include resolved service names in response
  - Comprehensive error handling with logging for failed service name resolution
  - Maintains backward compatibility with existing application data structure

- **Application Transformer Service**: Enhanced ApplicationDetailsResponse interface and transformation
  - Added service_name field to ApplicationDetailsResponse interface
  - Updated `transformApplicationDetails` method to include service name with fallback logic
  - Maintains all existing transformation logic while adding new service name functionality

- **Application Controller**: Added new PUT endpoint with comprehensive validation and security
  - New `updateCurrentStep` method with proper JWT authentication guards
  - Role-based access validation ensuring users can only update authorized applications
  - Enhanced error handling with user-friendly messages and appropriate HTTP status codes
  - Comprehensive API documentation with Swagger annotations

### Technical Details
- **Service Resolution Logic**: Robust service name resolution across multiple service types
  - Table mapping: immigration -> immigration_service, service -> service, package -> packages, training -> training
  - Handles missing service IDs and unknown service types gracefully
  - Returns descriptive fallback names when resolution fails
  - Comprehensive logging for debugging and monitoring service resolution issues

- **Database Operations**: Optimized current step updates with proper audit trails
  - Single database update operation for current_step field with updated_at timestamp
  - Application existence validation before update operations
  - Proper error handling for database constraint violations and connection issues
  - Maintains data integrity with transactional update patterns

- **Authentication & Authorization**: Enhanced JWT-based access control
  - Multi-role JWT validation supporting User, Admin, and Agent token types
  - Application-specific access validation ensuring users can only access authorized data
  - Agent assignment validation for role-based application access
  - Comprehensive error responses for unauthorized access attempts

### API Changes
- **GET /applications/{id}**: Enhanced response now includes service_name field
  - New service_name field showing resolved service name (e.g., "Express Entry - Federal Skilled Worker")
  - Field always present with string value (fallback to generic name when resolution fails)
  - No breaking changes to existing response structure or field ordering

- **PUT /applications/{id}/current-step**: New endpoint for workflow step management
  - Request body: `{ "currentStep": "2" }` (string value for workflow step)
  - Response: `{ "success": true, "message": "Current step updated successfully", "applicationId": "...", "currentStep": "2" }`
  - HTTP 200 on success, HTTP 403 for access denied, HTTP 404 for application not found
  - Comprehensive error responses with descriptive messages for client-side handling

## [4.5.0] - 2025-06-23

### Added
- **Application Workflow Steps Count**: Enhanced GET /applications endpoint with `numberOfSteps` field
  - New `numberOfSteps` field in ApplicationListItem interface showing total workflow steps
  - Automatic calculation from workflow template JSON structure (workflowTemplate array length)
  - Graceful error handling for missing or malformed workflow templates (returns 0)
  - Enhanced database query to include workflow template data for step count calculation
  - Consistent camelCase naming convention following API standards
  - Non-destructive implementation preserving all existing functionality

### Changed
- **Application Service Enhancement**: Modified `getApplicationsWithTransformation` method to include workflow template data
  - Added workflow_template include with id, name, description, and workflowTemplate fields
  - Enhanced data retrieval for step count calculation without performance impact
  - Maintained existing query structure and pagination functionality
- **Application Transformer Service**: Enhanced `transformApplicationListItem` method with step count calculation
  - New `calculateNumberOfSteps` private method for workflow template processing
  - Added numberOfSteps field to ApplicationListItem interface
  - Comprehensive error handling with logging for failed step calculations
  - Backward compatibility maintained for applications without workflow templates

### Technical Details
- **Database Query Enhancement**: Added workflow_template relationship to application list queries
  - Includes essential workflow template fields (id, name, description, workflowTemplate)
  - No impact on existing query performance or pagination
  - Maintains role-based access control for Users, Admins, and Agents
- **Step Calculation Logic**: Robust calculation method for workflow template steps
  - Validates workflowTemplate is array before counting length
  - Returns 0 for null, undefined, or malformed workflow templates
  - Logs warnings for calculation failures without breaking API responses
- **Interface Updates**: Enhanced ApplicationListItem interface with numberOfSteps field
  - Type: number (integer representing total workflow steps)
  - Always present in response (0 when no workflow template or calculation fails)
  - Follows existing field naming conventions (camelCase)

### API Changes
- **GET /applications**: Enhanced response now includes numberOfSteps field for each application
  - New field shows total number of steps/stages in application's workflow template
  - Field always present with integer value (0 when no workflow template)
  - No breaking changes to existing response structure
  - Maintains all existing filtering, pagination, and role-based access controls
- **Role-Based Access**: numberOfSteps field respects existing access control patterns
  - Users see numberOfSteps only for their own applications
  - Admins see numberOfSteps for all applications
  - Agents see numberOfSteps only for assigned applications

### Development
- **Build Status**: ✅ npm run build completes successfully with no TypeScript errors
- **Server Status**: ✅ Development server starts successfully on http://localhost:4242
- **Code Quality**: ✅ All changes follow non-destructive development patterns
- **Error Handling**: ✅ Comprehensive error handling with graceful degradation
- **Backward Compatibility**: ✅ All existing functionality preserved
- **Testing Ready**: ✅ Implementation ready for endpoint testing and validation

## [4.4.0] - 2025-06-23

### Added
- **Application Notes Field**: Added `note` field to application table for internal use
  - New database field `note` in application schema with optional string type
  - Updated GET `/applications/{id}` endpoint to include note field in response
  - New PUT `/applications/{id}/note` endpoint for updating application notes
  - Proper validation with 2000 character limit and error handling
  - Role-based access control for users, agents, and admins
  - Agent access restricted to assigned applications only

- **Enhanced Document Vault Endpoint**: Improved document vault functionality with pagination
  - Enhanced GET `/documents` endpoint with comprehensive pagination support
  - Added structured response format with metadata (total, page, limit, totalPages)
  - Improved filtering by document type and search functionality
  - Backward compatibility maintained with legacy methods
  - Proper authentication middleware for secure access
  - Comprehensive error handling and validation

### Changed
- Updated `ApplicationDetailsResponseDto` to include optional note field
- Enhanced `DocumentVaultService` with pagination support and legacy compatibility
- Improved document controller with structured response format and better error handling

### Technical Details
- Database migration: `add_note_field_to_application` successfully applied
- New DTOs: `UpdateApplicationNoteDto`, `UpdateApplicationNoteResponseDto`, `DocumentVaultQueryDto`, `DocumentVaultResponseDto`
- Enhanced interfaces with proper typing for pagination responses
- Maintained backward compatibility for all existing functionality
- All TypeScript compilation successful with no errors - 2025-06-22

### Added
- **Workflow Template Duplicate Prevention** - Enhanced duplicate prevention system for workflow templates
  - Service-based duplicate prevention ensuring only one active workflow template per immigration package
  - Validation logic checks for existing active templates with same serviceType and serviceId combination
  - Comprehensive error handling with detailed conflict messages including existing template information
  - Support for both specific service IDs and general service types (null serviceId)
  - Enhanced create and update methods with proper duplicate validation
- **Application Estimated Completion Management** - New endpoint for updating estimated completion dates
  - `PUT /applications/{applicationId}/estimated-completion` - Admin/Agent only endpoint for updating estimated completion dates
  - Comprehensive date validation (future dates only, proper ISO 8601 format)
  - Role-based access control with JwtAdminOrAgent guard
  - Complete error handling and logging
- **Enhanced Application List Response** - Estimated completion field now included in GET /applications response
  - ApplicationListItem interface updated to include estimated_completion field
  - Transformer service enhanced to serialize estimated completion dates
  - Consistent date formatting across all application endpoints
- **Document Ordering Enhancement** - Confirmed GET /applications/{id} returns documents ordered by updated_at descending
  - Most recently updated documents appear first in application details
  - Follows user's preferred pattern for document updates sorting
- **Comprehensive DTO Validation** - New DTOs for estimated completion updates
  - UpdateEstimatedCompletionDto with proper date string validation
  - UpdateEstimatedCompletionResponseDto for consistent API responses
  - Full Swagger/OpenAPI documentation for new endpoints

### Changed
- **Workflow Template Service Enhancement** - Enhanced duplicate prevention logic for workflow templates
  - New checkServiceDuplicate method for validating active template uniqueness
  - Enhanced create method with service-based duplicate validation before template creation
  - Enhanced update method with duplicate validation for serviceType, serviceId, and isActive changes
  - Intelligent validation that only checks duplicates when templates will be active after operation
  - Detailed logging for all duplicate prevention operations with correlation IDs
- **Application Service Enhancement** - Added updateEstimatedCompletion method with comprehensive validation
  - Application existence validation before updates
  - Date parsing and validation with proper error messages
  - Future date requirement enforcement
  - Detailed logging for audit trails
- **Application Transformer Service** - Enhanced to include estimated completion in list responses
  - ApplicationListItem interface extended with estimated_completion field
  - Consistent date serialization using toISOString() format
  - Backward compatibility maintained for existing responses

### Technical Details
- **Workflow Template Duplicate Prevention**: Advanced validation system for ensuring template uniqueness
  - Database queries check for existing active templates with same serviceType and serviceId combination
  - Supports both specific service IDs and general service types (null serviceId handling)
  - Excludes current template from duplicate checks during updates to prevent false positives
  - Only validates duplicates when templates will be active after the operation
  - Comprehensive error messages include existing template details (name and ID) for better user experience
- **Authentication**: Uses existing JwtAdminOrAgent guard for proper role-based access control
- **Validation**: Comprehensive input validation with class-validator decorators
- **Error Handling**: Proper HTTP status codes and meaningful error messages
- **Database**: Utilizes existing estimated_completion field in application schema
- **Logging**: Detailed logging for all operations with correlation IDs
- **Documentation**: Complete Swagger documentation for new endpoints

### API Changes
- **Workflow Template Endpoints** - Enhanced duplicate prevention for workflow template operations
  - `POST /workflow-templates` - Now validates for duplicate active templates with same serviceType/serviceId
  - `PATCH /workflow-templates/{id}` - Validates duplicates when updating serviceType, serviceId, or activating templates
  - Error responses include detailed conflict information with existing template details
  - HTTP 409 Conflict status returned when duplicate active templates would be created
  - No breaking changes to existing request/response structures
- `PUT /applications/{applicationId}/estimated-completion` - New endpoint for updating estimated completion dates
  - Request body: `{ "estimated_completion": "2025-07-15T10:30:00.000Z" }`
  - Response: Complete application details with updated estimated completion
  - Requires Admin or Agent authentication
- `GET /applications` - Enhanced response now includes estimated_completion field
  - No breaking changes to existing response structure
  - Additional field provides estimated completion dates in list view
- `GET /applications/{id}` - Confirmed documents ordered by updated_at descending (newest first)
  - Existing functionality verified and documented
  - Follows established user preference for document ordering

### Development
- **Workflow Template Testing**: ✅ Comprehensive test suite for duplicate prevention functionality
  - 29 total tests passing (27 existing + 2 new duplicate prevention tests)
  - Service-based duplicate prevention tests for create and update operations
  - Edge case testing for inactive templates, different service types, and null service IDs
  - Mock-based testing with proper test isolation and data management
- **Server Status**: ✅ Development server starts successfully with 0 compilation errors
- **Build Status**: ✅ npm run build completes successfully with no TypeScript errors
- **Code Quality**: ✅ All new code follows established patterns and conventions
- **Non-Destructive**: ✅ All changes follow non-destructive development patterns
- **Testing Ready**: ✅ Implementation ready for comprehensive testing with different user roles

## [Previous Release] - 2025-06-18

### Added
- **Universal Authentication Module** - New `/auth` module for role-based frontend functionality
- **JWT Token Type Endpoint** - New `GET /auth/profile` endpoint returning user token type from JWT payload
- **Frontend Role-Based Examples** - Comprehensive React examples in `frontend-examples/` directory
  - Authentication context with state management
  - Role-based UI components and guards
  - Protected routing patterns
  - Complete implementation guide and documentation
- **Simplified JWT Architecture** - Frontend treats tokens as opaque strings, backend determines roles

### Changed
- **Auth Endpoint Simplification** - `/auth/profile` now returns minimal response `{ tokenType: string }`
- **JWT Token Handling** - Removed complex database queries from token type retrieval
- **Performance Optimization** - Eliminated unnecessary database dependencies from auth endpoints

### Removed
- **Auth Service Dependencies** - Removed `src/auth/auth.service.ts` with problematic database schema references
- **Complex Profile Queries** - Removed database-heavy profile data fetching from auth endpoints

### Fixed
- **Compilation Errors** - Resolved TypeScript errors related to non-existent database fields (`phone`, `department`)
- **Server Startup Issues** - Fixed development server startup problems caused by Prisma schema mismatches
- **JWT Architecture** - Properly aligned JWT token handling between frontend and backend

### Technical Details
- **Security**: Token verification remains server-side with existing multi-secret system
- **Compatibility**: Works with existing JWT guards (JwtAdmin, JwtAgent, JwtUser, JwtGuard)
- **Performance**: Eliminated database queries for simple token type retrieval
- **Frontend Integration**: Documented proper patterns for role-based UI without client-side JWT decryption

### API Changes
- `GET /auth/profile` - Returns `{ tokenType: "user|admin|agent|mentor" }` from JWT payload
- Endpoint protected by JwtGuard and works for all user types
- No breaking changes to existing authentication system

### Development
- **Server Status**: ✅ Development server starts successfully with 0 compilation errors
- **Module Loading**: ✅ All modules including new AuthModule load correctly
- **Route Mapping**: ✅ New `/auth/profile` endpoint properly mapped
- **Error Resolution**: ✅ All TypeScript compilation issues resolved

## [Previous Release] - 2025-06-08

### Removed
- **GDPR Security Implementation** - Complete removal of GDPR compliance and security implementation components
- **Security Module** - Removed entire security module (7 files) including SecurityService, GDPRService, and DataRetentionService
- **Application Access Guard** - Removed GDPR-specific access control guard
- **Security Test Suite** - Removed all security-related test files (4 files)
- **Database Security Schema** - Removed security.prisma schema and GDPR migration files
- **Security Documentation** - Removed GDPR implementation and security log documentation

### Changed
- **Application Module** - Updated app.module.ts to remove SecurityModule import
- **System Architecture** - Simplified architecture by removing GDPR compliance layer
- **Codebase Size** - Reduced codebase by 5,479 lines while maintaining core functionality

### Technical Details
- **Files Removed**: 19 total files (7 security modules, 4 test files, 2 database files, 2 documentation files, 4 other files)
- **Core Functionality Preserved**: Payment processing, user management, dashboard, immigration services remain fully functional
- **No Breaking Changes**: All core business logic and APIs continue to work as expected
- **Clean Removal**: No orphaned imports or dependencies remain

### Migration Impact
- **Database**: GDPR-related tables removed (security_log, gdpr_request, data_retention_policy)
- **API Endpoints**: Security and GDPR endpoints no longer available
- **Authentication**: Core JWT authentication and authorization systems unchanged
- **Document Security**: Document permission system (DocumentSecurityService) preserved as it's not GDPR-related

## [Previous Release] - 2025-05-31

### Added
- **Admin Unified Payment Integration** - Added admin-specific endpoints to unified payment controller
- **Unified Admin Payment Endpoints** - 3 new admin endpoints replacing 8 legacy progress update endpoints
- **Admin Payment Progress Update** - Single endpoint (`PATCH /v2/payment/admin/progress`) for all service types
- **Admin Payment Test Suite** - Complete test coverage for admin payment functionality (18 tests passing)
- **Payment Integration Tests** - Added comprehensive integration test suite for Payment table operations with mocked database
- **Payment CRUD Testing** - Complete test coverage for payment creation, reading, updating, and deletion operations
- **Payment Analytics Testing** - Test suite for payment aggregations, revenue calculations, and reporting features
- **Service Integration Testing** - End-to-end testing for UnifiedPaymentService with mocked Prisma operations
- **Multi-Service Payment Testing** - Tests for all service types (service, package, immigration, training)
- **Guest Payment Testing** - Comprehensive testing for guest payment flows without authentication

### Testing Infrastructure
- **Payment Test Suite** - Comprehensive test organization with feature-based folder structure (test/payment/)
- **Mocked Integration Framework** - Reliable testing with mocked Prisma operations for consistent results
- **Mock Data Management** - Extensive test fixtures for payment scenarios and edge cases
- **Test Performance Optimization** - Fast test execution with mocked dependencies (3.666s for 60 tests)
- **Test Data Isolation** - Proper test isolation with mock resets between test cases
- **Database Schema Testing** - Validation of foreign key relationships and data integrity with mocks

### Code Quality Improvements
- **100% Test Pass Rate** - All 60 tests passing successfully (3 test suites, 0 failures)
- **Enhanced Test Coverage** - 60 total tests covering all payment functionality:
  - 48 unit tests (controller + service)
  - 7 integration tests (mocked database operations)
  - 5 additional integration scenarios
- **Error Handling Tests** - Comprehensive testing of error scenarios and edge cases
- **Concurrent Operation Tests** - Testing for race conditions and concurrent payment processing
- **Validation Testing** - Input validation and business rule enforcement testing
- **Legacy Test Cleanup** - Removed unused test directories (test/database, test/logs) and legacy mock files

### Test Results Summary
- ✅ **3 test suites passed** (unified-payment.controller.spec.ts, unified-payment.service.spec.ts, payment.integration.spec.ts)
- ✅ **60 tests passed** with 100% success rate
- ✅ **0 failed tests** - Complete reliability
- ✅ **Fast execution** - 3.666 seconds total runtime
- ✅ **Mocked database operations** - No external dependencies required
- ✅ **Comprehensive coverage** - All payment table operations tested

### Technical Improvements
- **Comprehensive Code Documentation** - Added detailed JSDoc comments to all payment-related code
- **Unified Payment System** - Implemented new unified payment architecture to consolidate payment tables
- **Payment Code Comments** - Added extensive documentation for payment service methods, controllers, and DTOs
- **Developer Documentation** - Enhanced code readability with method descriptions, parameter documentation, and usage examples
- **Payment Service Documentation** - Added comprehensive method-level documentation for all payment operations
- **Payment Controller Documentation** - Added endpoint documentation with parameter descriptions and usage examples
- **Unified Payment Service Documentation** - Added detailed documentation for the new unified payment architecture
- **Payment DTO Documentation** - Added validation and property descriptions for all payment data transfer objects
- **Payment Module Documentation** - Added module-level documentation explaining dependencies and features

## [0.0.1] - 2025-03-27

### Added
- **Blog Comments System** - Complete commenting functionality for blog posts
- **Customer Review System** - Customer review management with ordering capabilities
- **Training Module** - Training programs with image support and ordering
- **Guest Purchase System** - Non-authenticated purchase flow for all services
- **Payment Integration** - Stripe payment processing for multiple service types
- **Email Notification System** - Automated email notifications for purchases and admin alerts
- **Admin Dashboard** - Administrative interface for managing users, mentors, and services
- **User Authentication** - JWT-based authentication with refresh token support
- **Mentor Management** - Mentor profile management with service offerings
- **Immigration Services** - Immigration consultation service management
- **Package Management** - Service package creation and management
- **Media Upload** - File upload functionality with Supabase integration
- **Resume Builder** - Resume creation and management tools
- **Contact Us System** - Contact form and inquiry management
- **OTP Verification** - Email-based OTP verification system
- **Password Management** - Password reset and change functionality

### Technical Features
- **NestJS Framework** - Built on NestJS with TypeScript
- **Prisma ORM** - Database management with Prisma
- **Fastify Platform** - High-performance HTTP server
- **Swagger Documentation** - API documentation with OpenAPI
- **JWT Guards** - Role-based access control (User, Admin, Mentor)
- **Email Templates** - React-based email templates
- **Database Migrations** - Comprehensive database schema management
- **Error Handling** - Global exception filters
- **File Processing** - PDF and document processing capabilities
- **OpenAI Integration** - AI-powered features for resume building

### Database Schema
- **User Management** - User profiles with authentication
- **Mentor System** - Mentor profiles and service offerings
- **Service Management** - Various service types (mentor, immigration, training)
- **Package System** - Service packages and pricing
- **Payment Tracking** - Both authenticated and guest payment records
- **Blog System** - Blog posts with comments
- **Review System** - Customer reviews with ratings
- **Contact Management** - Contact inquiries and responses

### API Endpoints
- **Authentication** - Login, register, refresh token, OTP verification
- **User Management** - Profile management, password reset
- **Mentor Services** - Mentor CRUD operations and service management
- **Payment Processing** - Stripe integration for all service types
- **Guest Services** - Non-authenticated service purchases
- **Admin Panel** - Administrative operations
- **Blog Management** - Blog posts and comments
- **Media Handling** - File upload and management
- **Dashboard** - User and admin dashboards

### Security Features
- **JWT Authentication** - Secure token-based authentication
- **Role-based Access** - Different access levels for users, mentors, and admins
- **Password Encryption** - bcrypt password hashing
- **Input Validation** - Comprehensive request validation
- **CORS Configuration** - Cross-origin resource sharing setup

### Payment System
- **Stripe Integration** - Complete payment processing with Checkout Sessions
- **Multiple Service Types** - Support for mentor, package, immigration, and training services
- **Guest Payments** - Non-authenticated payment flow for all service types
- **Webhook Handling** - Automated payment confirmation and status updates
- **Email Notifications** - Purchase confirmations and admin alerts with React templates
- **Unified Payment Architecture** - New consolidated payment table structure (v2.0)
- **Legacy Payment Support** - Backward compatibility with existing payment tables
- **Payment Analytics** - Revenue tracking and reporting capabilities
- **Payment Status Management** - Comprehensive payment lifecycle tracking

### Email System
- **Transactional Emails** - Purchase confirmations, OTP verification
- **Admin Notifications** - New purchase and inquiry alerts
- **Template System** - React-based email templates
- **Multiple Providers** - Support for various email services

### Development Tools
- **TypeScript** - Full TypeScript implementation
- **ESLint & Prettier** - Code formatting and linting
- **Jest Testing** - Unit and integration testing setup
- **Docker Support** - Containerization configuration
- **Environment Configuration** - Comprehensive environment variable management

### Infrastructure
- **Supabase Integration** - Database and storage backend
- **Stripe Payment Gateway** - Payment processing
- **Email Service Integration** - Transactional email delivery
- **File Storage** - Media and document storage
- **OpenAI API** - AI-powered features

## Version History

### Recent Updates (2025)
- **2025-03-27**: Blog comments system implementation
- **2025-03-25**: Package reordering fixes
- **2025-03-21**: Service reordering functionality
- **2025-03-13**: Customer review system
- **2025-03-05**: Admin email notifications
- **2025-02-27**: Training email template updates
- **2025-02-24**: Training API improvements
- **2025-02-21**: User profile enhancements
- **2025-02-18**: Training module implementation
- **2025-02-10**: Email template improvements
- **2025-02-07**: Purchase notification templates
- **2025-02-06**: Mentor schema updates
- **2025-02-05**: Email image support
- **2025-02-03**: User validation improvements
- **2025-01-31**: Guest purchase history API
- **2025-01-30**: User detail API fixes and delete functionality
- **2025-01-29**: Guest purchase system
- **2025-01-24**: Training system implementation
- **2025-01-22**: Password management and account deletion
- **2025-01-18**: Credential login fixes
- **2025-01-17**: Email system implementation
- **2025-01-16**: Refresh token API fixes
- **2025-01-15**: Dashboard API and progress tracking
- **2025-01-07**: User profile API updates

### Foundation (2024)
- **2024-12-27**: Payment method implementation
- **2024-12-26**: Package and immigration services
- **2024-12-16**: Admin user and mentor management
- **2024-12-13**: JWT decoding fixes
- **2024-12-12**: User detail retrieval
- **2024-12-11**: Review system implementation
- **2024-12-10**: Core services (blog, mentor, service, contact)
- **2024-11-26**: Mentor API and schema updates
- **2024-07-11**: Project initialization and AI prompt setup
- **2024-06-27**: ChatGPT model integration

## Dependencies

### Core Dependencies
- **@nestjs/core**: ^10.0.0 - NestJS framework
- **@prisma/client**: ^6.5.0 - Database ORM
- **stripe**: ^17.4.0 - Payment processing
- **@supabase/supabase-js**: ^2.43.1 - Backend services
- **bcrypt**: ^5.1.1 - Password hashing
- **@nestjs/jwt**: ^10.2.0 - JWT authentication
- **openai**: ^4.47.1 - AI integration
- **nodemailer**: ^6.9.13 - Email sending

### Development Dependencies
- **typescript**: ^5.1.3 - TypeScript compiler
- **@nestjs/testing**: ^10.0.0 - Testing utilities
- **jest**: ^29.5.0 - Testing framework
- **eslint**: ^8.42.0 - Code linting
- **prettier**: ^3.0.0 - Code formatting

## Migration Plans

### Payment Table Consolidation (In Progress)
- **Current State**: 8 separate payment tables (user_mentor_service, guest_mentor_service, user_package, guest_package, user_immigration_service, guest_immigration_service, user_training, guest_training)
- **Target State**: Single unified payment table
- **Progress**:
  - ✅ Unified payment table schema created
  - ✅ UnifiedPaymentService implemented with full CRUD operations
  - ✅ UnifiedPaymentController implemented with v2 API endpoints
  - ✅ Revenue analytics and reporting capabilities added
  - ✅ Email notification system integrated
  - ✅ Backward compatibility layer implemented
  - ⏳ Legacy payment service migration (pending)
  - ⏳ Production data migration (pending)
- **Migration Strategy**: See PAYMENT_MIGRATION.md for detailed plan
- **Documentation**: Comprehensive code comments added for developer reference

### User Role Unification (Planned)
- **Current State**: 3 separate user tables (user, admin, mentor) with different authentication systems
- **Target State**: Single unified user table with role-based access control supporting 4 user types (user, admin, mentor, agent)
- **New Features**: Immigration agent role with specialized permissions and profile fields
- **Migration Strategy**: See USER_ROLE_MIGRATION.md for detailed plan

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under UNLICENSED.
