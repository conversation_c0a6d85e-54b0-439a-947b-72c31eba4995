/**
 * Application Status Enum Validation Tests
 * 
 * Tests to verify that the ApplicationStatus enum includes "Pending" 
 * and works correctly with the database.
 * 
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-08-09
 */

import { ApplicationStatus } from '@prisma/client';

describe('ApplicationStatus Enum', () => {
  it('should include Pending as a valid enum value', () => {
    expect(ApplicationStatus.Pending).toBe('Pending');
    expect(Object.values(ApplicationStatus)).toContain('Pending');
  });

  it('should include all expected enum values', () => {
    const expectedValues = [
      'Draft',
      'Pending',
      'Submitted',
      'Under_Review',
      'Additional_Info_Required',
      'Approved',
      'Rejected',
      'Completed',
      'Cancelled',
      'On_Hold',
    ];

    expectedValues.forEach(value => {
      expect(Object.values(ApplicationStatus)).toContain(value);
    });
  });

  it('should allow ApplicationStatus.Pending to be used in application creation', () => {
    // This test verifies that the enum value can be used without TypeScript errors
    const status = ApplicationStatus.Pending;
    expect(status).toBe('Pending');
    expect(typeof status).toBe('string');
  });

  it('should validate that Pending is the correct status for new applications', () => {
    // Verify that Pending is appropriate for new applications
    expect(ApplicationStatus.Pending).toBe('Pending');
    expect(ApplicationStatus.Draft).toBe('Draft');
    
    // Both should be valid for new applications, but Pending is used for payment webhook
    expect([ApplicationStatus.Pending, ApplicationStatus.Draft]).toContain(ApplicationStatus.Pending);
  });
});
