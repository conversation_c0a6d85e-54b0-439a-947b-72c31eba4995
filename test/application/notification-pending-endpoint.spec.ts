/**
 * Notification Service getPendingNotifications Unit Tests
 *
 * Tests for the modified getPendingNotifications endpoint functionality
 * with new response structure and database schema changes.
 *
 * <AUTHOR> Ireland Development Team
 * @version 2.0.0
 * @since 2025-08-10
 */

import {
  PendingNotificationResponse,
  BatchNotificationError,
  DocumentStatusChange,
} from '../../src/application/interfaces/notification.interface';

describe('NotificationService - getPendingNotifications API Structure', () => {

  const mockApplicationId = 'test-app-id';
  const mockNotificationQueueId = 'test-notification-id';
  const mockApplicationNumber = 'IMM-2025-000001';
  
  const mockDocumentChanges: DocumentStatusChange[] = [
    {
      documentId: 'doc-1',
      documentName: 'Passport Copy',
      previousStatus: 'PENDING',
      currentStatus: 'APPROVED',
      reason: 'Document approved',
      reviewComments: 'Clear and valid document',
      changedAt: new Date('2025-08-10T10:30:00.000Z'),
      changedBy: '<EMAIL>',
    },
  ];

  const mockNotificationRecord = {
    id: mockNotificationQueueId,
    notification_type: 'Email',
    recipient_user: '<EMAIL>',
    recipient_name: 'John Doe',
    subject: 'Document Status Updates',
    message_body: 'Document status changes pending',
    application_id: mockApplicationId,
    document_changes: mockDocumentChanges,
    status: 'Pending',
    created_at: new Date(),
    updated_at: new Date(),
    application: {
      application_number: mockApplicationNumber,
    },
  };

  // Mock notification service for testing response structure
  const mockNotificationService = {
    getPendingNotifications: jest.fn(),
  };

  describe('Response Structure Tests', () => {
    it('should return pending notifications with correct new structure', async () => {
      // Arrange
      const expectedResponse: PendingNotificationResponse = {
        notification_queue_id: mockNotificationQueueId,
        applicationNumber: mockApplicationNumber,
        emailPreview: {
          subject: 'Document Status Updates - Application IMM-2025-000001',
          htmlContent: '<h2>Document Status Updates</h2><p>Dear John Doe,</p>...',
        },
      };

      mockNotificationService.getPendingNotifications.mockResolvedValue(expectedResponse);

      // Act
      const result = await mockNotificationService.getPendingNotifications(mockApplicationId);

      // Assert
      expect(result).toHaveProperty('notification_queue_id', mockNotificationQueueId);
      expect(result).toHaveProperty('applicationNumber', mockApplicationNumber);
      expect(result).toHaveProperty('emailPreview');
      expect((result as PendingNotificationResponse).emailPreview).toHaveProperty('subject');
      expect((result as PendingNotificationResponse).emailPreview).toHaveProperty('htmlContent');
    });

    it('should return error when no pending notifications found', async () => {
      // Arrange
      const expectedError: BatchNotificationError = {
        success: false,
        message: 'No pending notifications found for this application',
        errorCode: 'NO_PENDING_NOTIFICATIONS',
        applicationId: mockApplicationId,
      };

      mockNotificationService.getPendingNotifications.mockResolvedValue(expectedError);

      // Act
      const result = await mockNotificationService.getPendingNotifications(mockApplicationId);

      // Assert
      expect(result).toEqual(expectedError);
    });

    it('should verify removed fields are not included in response', async () => {
      // Arrange
      const expectedResponse: PendingNotificationResponse = {
        notification_queue_id: mockNotificationQueueId,
        applicationNumber: mockApplicationNumber,
        emailPreview: {
          subject: 'Document Status Updates - Application IMM-2025-000001',
          htmlContent: '<h2>Document Status Updates</h2><p>Dear John Doe,</p>...',
        },
      };

      mockNotificationService.getPendingNotifications.mockResolvedValue(expectedResponse);

      // Act
      const result = await mockNotificationService.getPendingNotifications(mockApplicationId);

      // Assert - Verify removed fields are not present
      expect(result).not.toHaveProperty('pendingCount');
      expect(result).not.toHaveProperty('recipientEmail');
      expect(result).not.toHaveProperty('recipientName');
      expect(result).not.toHaveProperty('applicationId');
      expect(result).not.toHaveProperty('documentChanges');

      // Verify required fields are present
      expect(result).toHaveProperty('notification_queue_id');
      expect(result).toHaveProperty('applicationNumber');
      expect(result).toHaveProperty('emailPreview');
    });
  });
});
