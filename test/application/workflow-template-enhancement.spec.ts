import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationTransformerService } from '../../src/application/services/application-transformer.service';
import { Logger } from '@nestjs/common';

describe('ApplicationTransformerService - Workflow Template Enhancement', () => {
  let service: ApplicationTransformerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ApplicationTransformerService],
    }).compile();

    service = module.get<ApplicationTransformerService>(
      ApplicationTransformerService,
    );
  });

  describe('transformApplicationListItem with workflow template', () => {
    it('should include workflow template data when available', () => {
      const mockApplication = {
        id: 'app_123',
        application_number: 'IMM-2024-000001',
        service_type: 'immigration',
        service_id: 'service_456',
        service_name: 'Work Permit Application',
        status: 'draft',
        priority_level: 'medium',
        current_step: '1',
        agent_details: [
          { id: 'agent_1', name: '<PERSON>', email: '<EMAIL>' },
        ],
        workflow_template: {
          id: 'template_123',
          name: 'Immigration Workflow',
          description: 'Standard immigration application workflow',
          workflowTemplate: [
            { stageName: 'Initial Review', stageOrder: 1 },
            { stageName: 'Document Collection', stageOrder: 2 },
          ],
        },
        estimated_completion: new Date('2024-12-31'),
        created_at: new Date('2024-01-01'),
        updated_at: new Date('2024-01-02'),
        user: {
          name: 'Jane Smith',
          email: '<EMAIL>',
          mobileNo: '+1234567890',
        },
      };

      const result = service.transformApplicationListItem(mockApplication);

      expect(result).toMatchObject({
        id: 'app_123',
        application_number: 'IMM-2024-000001',
        service_type: 'immigration',
        service_id: 'service_456',
        service_name: 'Work Permit Application',
        status: 'draft',
        priority_level: 'medium',
        current_step: '1',
        numberOfSteps: 2,
        workflow_template: {
          id: 'template_123',
          name: 'Immigration Workflow',
          description: 'Standard immigration application workflow',
        },
      });

      expect(result.user).toEqual({
        name: 'Jane Smith',
        email: '<EMAIL>',
        mobile: '+1234567890',
      });

      expect(result.agent_ids).toHaveLength(1);
      expect(result.agent_ids[0]).toEqual({
        id: 'agent_1',
        name: 'John Doe',
        email: '<EMAIL>',
      });
    });

    it('should handle applications without workflow template gracefully', () => {
      const mockApplication = {
        id: 'app_456',
        application_number: 'IMM-2024-000002',
        service_type: 'immigration',
        service_id: 'service_789',
        service_name: 'Student Visa Application',
        status: 'in_progress',
        priority_level: 'high',
        current_step: '2',
        agent_details: [],
        workflow_template: null,
        estimated_completion: null,
        created_at: new Date('2024-01-01'),
        updated_at: new Date('2024-01-02'),
        guest_name: 'Guest User',
        guest_email: '<EMAIL>',
        guest_mobile: '+0987654321',
      };

      const result = service.transformApplicationListItem(mockApplication);

      expect(result).toMatchObject({
        id: 'app_456',
        application_number: 'IMM-2024-000002',
        service_type: 'immigration',
        service_id: 'service_789',
        service_name: 'Student Visa Application',
        status: 'in_progress',
        priority_level: 'high',
        current_step: '2',
        numberOfSteps: 0,
        estimated_completion: null,
      });

      expect(result.workflow_template).toBeUndefined();
      expect(result.guest).toEqual({
        name: 'Guest User',
        email: '<EMAIL>',
        mobile: '+0987654321',
      });
    });

    it('should handle workflow template with empty or invalid data', () => {
      const mockApplication = {
        id: 'app_789',
        application_number: 'IMM-2024-000003',
        service_type: 'immigration',
        service_id: 'service_101',
        service_name: 'Visitor Visa Application',
        status: 'pending',
        priority_level: 'low',
        current_step: '1',
        agent_details: [],
        workflow_template: {
          id: '',
          name: '',
          description: '',
          workflowTemplate: [],
        },
        estimated_completion: new Date('2024-06-30'),
        created_at: new Date('2024-01-01'),
        updated_at: new Date('2024-01-02'),
      };

      const result = service.transformApplicationListItem(mockApplication);

      expect(result.workflow_template).toEqual({
        id: '',
        name: '',
        description: '',
      });
      expect(result.numberOfSteps).toBe(0);
    });

    it('should handle error scenarios gracefully', () => {
      const mockApplication = {
        id: 'app_error',
        application_number: 'ERR-2024-000001',
        service_type: 'immigration',
        service_id: null,
        service_name: null,
        status: 'error',
        priority_level: null,
        current_step: null,
        agent_details: null,
        workflow_template: undefined,
        estimated_completion: null,
        created_at: new Date('2024-01-01'),
        updated_at: new Date('2024-01-02'),
      };

      const result = service.transformApplicationListItem(mockApplication);

      expect(result).toMatchObject({
        id: 'app_error',
        application_number: 'ERR-2024-000001',
        service_type: 'immigration',
        service_id: null,
        service_name: 'immigration service',
        status: 'error',
        numberOfSteps: 0,
      });

      expect(result.workflow_template).toBeUndefined();
      expect(result.agent_ids).toEqual([]);
    });

    it('should handle malformed application data with error recovery', () => {
      // Simulate a malformed application object that would cause errors
      const malformedApplication = {
        // Missing required fields
        created_at: 'invalid-date',
        updated_at: null,
      };

      const result = service.transformApplicationListItem(malformedApplication);

      // Should return a safe fallback object
      expect(result).toMatchObject({
        id: 'unknown',
        application_number: 'unknown',
        service_type: 'unknown',
        status: 'unknown',
        service_name: 'Unknown Service',
        service_id: '',
        priority_level: 'medium',
        current_step: '1',
        numberOfSteps: 0,
        agent_ids: [],
        estimated_completion: null,
      });

      expect(result.created_at).toBeDefined();
      expect(result.updated_at).toBeDefined();
    });

    it('should calculate numberOfSteps correctly from workflow template', () => {
      const mockApplication = {
        id: 'app_steps',
        application_number: 'STEPS-2024-000001',
        service_type: 'immigration',
        service_id: 'service_steps',
        service_name: 'Multi-step Application',
        status: 'draft',
        priority_level: 'medium',
        current_step: '1',
        agent_details: [],
        workflow_template: {
          id: 'template_steps',
          name: 'Multi-step Workflow',
          description: 'Workflow with multiple steps',
          workflowTemplate: [
            { stageName: 'Step 1', stageOrder: 1 },
            { stageName: 'Step 2', stageOrder: 2 },
            { stageName: 'Step 3', stageOrder: 3 },
            { stageName: 'Step 4', stageOrder: 4 },
            { stageName: 'Step 5', stageOrder: 5 },
          ],
        },
        estimated_completion: new Date('2024-12-31'),
        created_at: new Date('2024-01-01'),
        updated_at: new Date('2024-01-02'),
      };

      const result = service.transformApplicationListItem(mockApplication);

      expect(result.numberOfSteps).toBe(5);
      expect(result.workflow_template).toEqual({
        id: 'template_steps',
        name: 'Multi-step Workflow',
        description: 'Workflow with multiple steps',
      });
    });
  });

  describe('backward compatibility', () => {
    it('should maintain all existing fields in the response', () => {
      const mockApplication = {
        id: 'app_compat',
        application_number: 'COMPAT-2024-000001',
        service_type: 'immigration',
        service_id: 'service_compat',
        service_name: 'Compatibility Test',
        status: 'draft',
        priority_level: 'medium',
        current_step: '1',
        agent_details: [
          { id: 'agent_1', name: 'Agent One', email: '<EMAIL>' },
        ],
        assigned_agent: {
          id: 'agent_2',
          name: 'Agent Two',
          email: '<EMAIL>',
        },
        workflow_template: {
          id: 'template_compat',
          name: 'Compatibility Workflow',
          description: 'Test workflow for compatibility',
          workflowTemplate: [{ stageName: 'Test Step', stageOrder: 1 }],
        },
        estimated_completion: new Date('2024-12-31'),
        created_at: new Date('2024-01-01'),
        updated_at: new Date('2024-01-02'),
        user: {
          name: 'Test User',
          email: '<EMAIL>',
          mobileNo: '+1234567890',
        },
      };

      const result = service.transformApplicationListItem(mockApplication);

      // Verify all existing fields are present
      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('application_number');
      expect(result).toHaveProperty('service_type');
      expect(result).toHaveProperty('service_name');
      expect(result).toHaveProperty('service_id');
      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('priority_level');
      expect(result).toHaveProperty('current_step');
      expect(result).toHaveProperty('numberOfSteps');
      expect(result).toHaveProperty('agent_ids');
      expect(result).toHaveProperty('assigned_agent');
      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('estimated_completion');
      expect(result).toHaveProperty('created_at');
      expect(result).toHaveProperty('updated_at');

      // Verify new workflow_template field is present
      expect(result).toHaveProperty('workflow_template');
      expect(result.workflow_template).toEqual({
        id: 'template_compat',
        name: 'Compatibility Workflow',
        description: 'Test workflow for compatibility',
      });
    });
  });
});
