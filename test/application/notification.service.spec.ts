/**
 * Notification Service Unit Tests
 *
 * Comprehensive test suite for NotificationService covering notification settings
 * management, validation logic, error handling, and business rules.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-10
 */

import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { NotificationService } from '../../src/application/services/notification.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { MailerService } from '../../src/mailer/mailer.service';
import {
  NotificationSettingsDto,
  UpdateNotificationSettingsDto,
} from '../../src/application/dto/notification.dto';

describe('NotificationService', () => {
  let service: NotificationService;
  let prismaService: jest.Mocked<PrismaService>;
  let loggerService: jest.Mocked<LoggerService>;
  let mailerService: jest.Mocked<MailerService>;

  const mockUserId = 'test-user-id';
  const mockDefaultSettings = {
    id: 'settings-id',
    user_id: mockUserId,
    agent_assigned: true,
    case_status_update: true,
    agent_query: true,
    document_rejection: true,
    missing_document_reminder_days: 7,
    system_maintenance: true,
    upcoming_deadline_alerts: true,
    final_decision_issued: true,
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockPrismaTransaction = {
    notification_settings: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationService,
        {
          provide: PrismaService,
          useValue: {
            notification_settings: {
              findUnique: jest.fn(),
              create: jest.fn(),
              update: jest.fn(),
            },
            $transaction: jest.fn(),
          },
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
          },
        },
        {
          provide: MailerService,
          useValue: {
            sendEmail: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<NotificationService>(NotificationService);
    prismaService = module.get(PrismaService);
    loggerService = module.get(LoggerService);
    mailerService = module.get(MailerService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('getUserNotificationSettings', () => {
    it('should return existing notification settings for user', async () => {
      // Arrange
      prismaService.notification_settings.findUnique.mockResolvedValue(
        mockDefaultSettings,
      );

      // Act
      const result = await service.getUserNotificationSettings(mockUserId);

      // Assert
      expect(result).toEqual({
        agent_assigned: true,
        case_status_update: true,
        agent_query: true,
        document_rejection: true,
        missing_document_reminder_days: 7,
        system_maintenance: true,
        upcoming_deadline_alerts: true,
        final_decision_issued: true,
      });
      expect(
        prismaService.notification_settings.findUnique,
      ).toHaveBeenCalledWith({
        where: { user_id: mockUserId },
      });
    });

    it('should create default settings if none exist', async () => {
      // Arrange
      prismaService.notification_settings.findUnique.mockResolvedValue(null);
      prismaService.notification_settings.create.mockResolvedValue(
        mockDefaultSettings,
      );

      // Act
      const result = await service.getUserNotificationSettings(mockUserId);

      // Assert
      expect(result).toEqual({
        agent_assigned: true,
        case_status_update: true,
        agent_query: true,
        document_rejection: true,
        missing_document_reminder_days: 7,
        system_maintenance: true,
        upcoming_deadline_alerts: true,
        final_decision_issued: true,
      });
      expect(prismaService.notification_settings.create).toHaveBeenCalledWith({
        data: {
          user_id: mockUserId,
          agent_assigned: true,
          case_status_update: true,
          agent_query: true,
          document_rejection: true,
          missing_document_reminder_days: 7,
          system_maintenance: true,
          upcoming_deadline_alerts: true,
          final_decision_issued: true,
        },
      });
    });

    it('should handle database errors gracefully', async () => {
      // Arrange
      const dbError = new Error('Database connection failed');
      prismaService.notification_settings.findUnique.mockRejectedValue(dbError);

      // Act & Assert
      await expect(
        service.getUserNotificationSettings(mockUserId),
      ).rejects.toThrow(dbError);
      expect(loggerService.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to get notification settings'),
        expect.any(String),
      );
    });
  });

  describe('updateUserNotificationSettings', () => {
    const validUpdateDto: UpdateNotificationSettingsDto = {
      agent_assigned: false,
      missing_document_reminder_days: 14,
      system_maintenance: false,
    };

    it('should update existing notification settings', async () => {
      // Arrange
      const updatedSettings = { ...mockDefaultSettings, ...validUpdateDto };

      prismaService.$transaction.mockImplementation(async (callback) => {
        mockPrismaTransaction.notification_settings.findUnique.mockResolvedValue(
          mockDefaultSettings,
        );
        mockPrismaTransaction.notification_settings.update.mockResolvedValue(
          updatedSettings,
        );
        return callback(mockPrismaTransaction);
      });

      // Act
      const result = await service.updateUserNotificationSettings(
        mockUserId,
        validUpdateDto,
      );

      // Assert
      expect(result).toEqual({
        agent_assigned: false,
        case_status_update: true,
        agent_query: true,
        document_rejection: true,
        missing_document_reminder_days: 14,
        system_maintenance: false,
        upcoming_deadline_alerts: true,
        final_decision_issued: true,
      });
    });

    it('should create default settings before update if none exist', async () => {
      // Arrange
      const updatedSettings = { ...mockDefaultSettings, ...validUpdateDto };

      prismaService.$transaction.mockImplementation(async (callback) => {
        mockPrismaTransaction.notification_settings.findUnique.mockResolvedValue(
          null,
        );
        mockPrismaTransaction.notification_settings.create.mockResolvedValue(
          mockDefaultSettings,
        );
        mockPrismaTransaction.notification_settings.update.mockResolvedValue(
          updatedSettings,
        );
        return callback(mockPrismaTransaction);
      });

      // Act
      const result = await service.updateUserNotificationSettings(
        mockUserId,
        validUpdateDto,
      );

      // Assert
      expect(result).toBeDefined();
      expect(
        mockPrismaTransaction.notification_settings.create,
      ).toHaveBeenCalledWith({
        data: {
          user_id: mockUserId,
          agent_assigned: true,
          case_status_update: true,
          agent_query: true,
          document_rejection: true,
          missing_document_reminder_days: 7,
          system_maintenance: true,
          upcoming_deadline_alerts: true,
          final_decision_issued: true,
        },
      });
    });

    it('should validate missing_document_reminder_days range (1-365)', async () => {
      // Arrange
      const invalidUpdateDto: UpdateNotificationSettingsDto = {
        missing_document_reminder_days: 0, // Invalid: below minimum
      };

      // Act & Assert
      await expect(
        service.updateUserNotificationSettings(mockUserId, invalidUpdateDto),
      ).rejects.toThrow(
        'Missing document reminder days must be between 1 and 365 days',
      );
    });

    it('should validate missing_document_reminder_days maximum range', async () => {
      // Arrange
      const invalidUpdateDto: UpdateNotificationSettingsDto = {
        missing_document_reminder_days: 366, // Invalid: above maximum
      };

      // Act & Assert
      await expect(
        service.updateUserNotificationSettings(mockUserId, invalidUpdateDto),
      ).rejects.toThrow(
        'Missing document reminder days must be between 1 and 365 days',
      );
    });

    it('should accept valid missing_document_reminder_days values', async () => {
      // Arrange
      const validDays = [1, 30, 180, 365];

      for (const days of validDays) {
        const updateDto: UpdateNotificationSettingsDto = {
          missing_document_reminder_days: days,
        };

        const updatedSettings = {
          ...mockDefaultSettings,
          missing_document_reminder_days: days,
        };

        prismaService.$transaction.mockImplementation(async (callback) => {
          mockPrismaTransaction.notification_settings.findUnique.mockResolvedValue(
            mockDefaultSettings,
          );
          mockPrismaTransaction.notification_settings.update.mockResolvedValue(
            updatedSettings,
          );
          return callback(mockPrismaTransaction);
        });

        // Act
        const result = await service.updateUserNotificationSettings(
          mockUserId,
          updateDto,
        );

        // Assert
        expect(result.missing_document_reminder_days).toBe(days);
      }
    });
  });

  describe('shouldReceiveNotification', () => {
    it('should return true for enabled notification types', async () => {
      // Arrange
      prismaService.notification_settings.findUnique.mockResolvedValue(
        mockDefaultSettings,
      );

      // Act
      const result = await service.shouldReceiveNotification(
        mockUserId,
        'agent_assigned',
      );

      // Assert
      expect(result).toBe(true);
    });

    it('should return false for disabled notification types', async () => {
      // Arrange
      const disabledSettings = {
        ...mockDefaultSettings,
        agent_assigned: false,
      };
      prismaService.notification_settings.findUnique.mockResolvedValue(
        disabledSettings,
      );

      // Act
      const result = await service.shouldReceiveNotification(
        mockUserId,
        'agent_assigned',
      );

      // Assert
      expect(result).toBe(false);
    });

    it('should return true if no settings exist (default behavior)', async () => {
      // Arrange
      prismaService.notification_settings.findUnique.mockResolvedValue(null);

      // Act
      const result = await service.shouldReceiveNotification(
        mockUserId,
        'agent_assigned',
      );

      // Assert
      expect(result).toBe(true);
    });

    it('should return true on database errors (fail-safe)', async () => {
      // Arrange
      prismaService.notification_settings.findUnique.mockRejectedValue(
        new Error('DB Error'),
      );

      // Act
      const result = await service.shouldReceiveNotification(
        mockUserId,
        'agent_assigned',
      );

      // Assert
      expect(result).toBe(true);
      expect(loggerService.error).toHaveBeenCalled();
    });
  });

  describe('createDefaultSettings', () => {
    it('should create default notification settings for user', async () => {
      // Arrange
      prismaService.notification_settings.create.mockResolvedValue(
        mockDefaultSettings,
      );

      // Act
      const result = await service.createDefaultSettings(mockUserId);

      // Assert
      expect(result).toEqual(mockDefaultSettings);
      expect(prismaService.notification_settings.create).toHaveBeenCalledWith({
        data: {
          user_id: mockUserId,
          agent_assigned: true,
          case_status_update: true,
          agent_query: true,
          document_rejection: true,
          missing_document_reminder_days: 7,
          system_maintenance: true,
          upcoming_deadline_alerts: true,
          final_decision_issued: true,
        },
      });
    });

    it('should handle creation errors gracefully', async () => {
      // Arrange
      const dbError = new Error('Unique constraint violation');
      prismaService.notification_settings.create.mockRejectedValue(dbError);

      // Act & Assert
      await expect(service.createDefaultSettings(mockUserId)).rejects.toThrow(
        dbError,
      );
      expect(loggerService.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Failed to create default notification settings',
        ),
        expect.any(String),
      );
    });
  });
});
