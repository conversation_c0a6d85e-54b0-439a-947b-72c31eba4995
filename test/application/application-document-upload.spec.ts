/**
 * Application Document Upload Tests
 *
 * Tests for the new document upload functionality in ApplicationService.
 * Verifies that documents can be uploaded and linked to applications correctly.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { ApplicationService } from '../../src/application/application.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { MediaService } from '../../src/media/media.service';
import { DocumentVaultService } from '../../src/application/services/document-vault.service';
import { ApplicationFormService } from '../../src/application/services/application-form.service';
import { ApplicationDocumentService } from '../../src/application/services/application-document.service';
import { ApplicationTransformerService } from '../../src/application/services/application-transformer.service';
import { EmailTemplateIntegrationService } from '../../src/application/services/email-template-integration.service';
import { NotificationService } from '../../src/application/services/notification.service';
import { NotificationSettingsStorageService } from '../../src/utils/notification-settings-storage.service';
import { LoggerService } from '../../src/utils/logger.service';
import { DocumentType } from '@prisma/client';

describe('ApplicationService - Document Upload with File Replacement', () => {
  let service: ApplicationService;
  let prismaService: PrismaService;
  let mediaService: MediaService;
  let documentVaultService: DocumentVaultService;

  // Mock file for testing
  const mockFile: Express.Multer.File = {
    fieldname: 'file',
    originalname: 'test-passport.pdf',
    encoding: '7bit',
    mimetype: 'application/pdf',
    size: 1024 * 1024, // 1MB
    buffer: Buffer.from('mock pdf content'),
    destination: '',
    filename: '',
    path: '',
    stream: null,
  };

  const mockApplication = {
    id: 'app-123',
    user_id: 'user-123',
    guest_email: null,
  } as any;

  const mockUploadedDocument = {
    id: 'doc-123',
    document_name: 'Test Passport',
    file_path: 'documents/app-123/test-passport.pdf',
    file_size: 1024 * 1024,
    created_at: new Date(),
    original_filename: 'test-passport.pdf',
    document_type: DocumentType.Passport,
    expiry_date: null,
    user_id: 'user-123',
    updated_at: new Date(),
  } as any;

  const mockExistingDocumentVault = {
    id: 'existing-vault-123',
    document_name: 'Old Test Passport',
    file_path: 'documents/app-123/old-passport.pdf',
    file_size: 512 * 1024,
    original_filename: 'old-passport.pdf',
    document_type: DocumentType.Passport,
    expiry_date: null,
    user_id: 'user-123',
    updated_at: new Date(),
  } as any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationService,
        {
          provide: PrismaService,
          useValue: {
            application: {
              findUnique: jest.fn(),
            },
            application_document: {
              findFirst: jest.fn(),
              update: jest.fn(),
              create: jest.fn(),
            },
            document_vault: {
              update: jest.fn(),
              delete: jest.fn(),
            },
            $transaction: jest.fn(),
          },
        },
        {
          provide: MediaService,
          useValue: {
            uploadFile: jest.fn(),
            deleteFile: jest.fn(),
          },
        },
        {
          provide: DocumentVaultService,
          useValue: {
            uploadApplicationDocument: jest.fn(),
          },
        },
        {
          provide: ApplicationFormService,
          useValue: {},
        },
        {
          provide: ApplicationDocumentService,
          useValue: {},
        },
        {
          provide: ApplicationTransformerService,
          useValue: {},
        },
        {
          provide: EmailTemplateIntegrationService,
          useValue: {
            sendApplicationRequirementsEmail: jest.fn(),
            sendDocumentRequestEmail: jest.fn(),
            sendDocumentRejectionEmail: jest.fn(),
          },
        },
        {
          provide: NotificationService,
          useValue: {
            sendNotification: jest.fn(),
          },
        },
        {
          provide: NotificationSettingsStorageService,
          useValue: {
            getNotificationSettings: jest.fn(),
          },
        },
        {
          provide: LoggerService,
          useValue: {
            info: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ApplicationService>(ApplicationService);
    prismaService = module.get<PrismaService>(PrismaService);
    mediaService = module.get<MediaService>(MediaService);
    documentVaultService =
      module.get<DocumentVaultService>(DocumentVaultService);
  });

  describe('uploadApplicationDocument', () => {
    const uploadMetadata = {
      document_id: 'app-doc-123',
      document_name: 'Test Passport',
      document_category: 'identity',
      stage_order: 1,
      required: true,
      expiry_date: '2030-12-31',
    };

    it('should replace existing file successfully with transaction support', async () => {
      // Arrange - Mock existing document record with linked document vault
      const existingDocumentRecord = {
        id: 'app-doc-123',
        application_id: 'app-123',
        document_vault_id: 'existing-vault-123',
        stage_order: 1,
        file_name: 'Test Passport',
        status: 'uploaded',
        document: mockExistingDocumentVault, // Include linked document vault
      } as any;

      // Mock transaction function
      const mockTransaction = jest.fn().mockImplementation(async (callback) => {
        const mockTx = {
          document_vault: {
            update: jest.fn().mockResolvedValue({}),
            delete: jest.fn().mockResolvedValue({}),
          },
          application_document: {
            update: jest.fn().mockResolvedValue({}),
          },
        };
        return await callback(mockTx);
      });

      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(mockApplication);
      jest
        .spyOn(prismaService.application_document, 'findFirst')
        .mockResolvedValue(existingDocumentRecord);
      jest
        .spyOn(documentVaultService, 'uploadApplicationDocument')
        .mockResolvedValue(mockUploadedDocument);
      jest
        .spyOn(mediaService, 'deleteFile')
        .mockResolvedValue(true); // Successfully deleted existing file
      jest
        .spyOn(prismaService, '$transaction')
        .mockImplementation(mockTransaction);

      // Act
      const result = await service.uploadApplicationDocument(
        'app-123',
        mockFile,
        uploadMetadata,
        'user-123',
      );

      // Assert
      expect(result).toEqual({
        document_id: 'app-doc-123',
        document_name: 'Test Passport',
        file_path: 'documents/app-123/test-passport.pdf',
        file_size: 1024 * 1024,
        upload_date: expect.any(String),
        status: 'uploaded',
      });

      // Verify application lookup
      expect(prismaService.application.findUnique).toHaveBeenCalledWith({
        where: { id: 'app-123' },
        select: {
          id: true,
          user_id: true,
          guest_email: true,
        },
      });

      // Verify existing document lookup with include
      expect(prismaService.application_document.findFirst).toHaveBeenCalledWith({
        where: {
          application_id: 'app-123',
          id: 'app-doc-123',
          stage_order: 1,
          file_name: 'Test Passport',
        },
        include: {
          document: true,
        },
      });

      // Verify existing file deletion
      expect(mediaService.deleteFile).toHaveBeenCalledWith(
        'documents/app-123/old-passport.pdf',
      );

      // Verify new document upload
      expect(documentVaultService.uploadApplicationDocument).toHaveBeenCalledWith(
        mockFile,
        'app-123',
        {
          document_name: 'Test Passport',
          document_type: 'Other',
          document_category: 'identity',
          expiry_date: new Date('2030-12-31'),
          application_id: 'app-123',
          guest_email: null,
        },
        'user-123',
      );

      // Verify transaction was used
      expect(prismaService.$transaction).toHaveBeenCalled();
    });

    it('should handle case where existing file does not exist in storage', async () => {
      // Arrange - Mock existing document record with empty file path
      const existingDocumentRecord = {
        id: 'app-doc-123',
        application_id: 'app-123',
        document_vault_id: 'existing-vault-123',
        stage_order: 1,
        file_name: 'Test Passport',
        status: 'pending',
        document: {
          ...mockExistingDocumentVault,
          file_path: '', // No existing file
        },
      } as any;

      const mockTransaction = jest.fn().mockImplementation(async (callback) => {
        const mockTx = {
          document_vault: {
            update: jest.fn().mockResolvedValue({}),
            delete: jest.fn().mockResolvedValue({}),
          },
          application_document: {
            update: jest.fn().mockResolvedValue({}),
          },
        };
        return await callback(mockTx);
      });

      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(mockApplication);
      jest
        .spyOn(prismaService.application_document, 'findFirst')
        .mockResolvedValue(existingDocumentRecord);
      jest
        .spyOn(documentVaultService, 'uploadApplicationDocument')
        .mockResolvedValue(mockUploadedDocument);
      jest
        .spyOn(prismaService, '$transaction')
        .mockImplementation(mockTransaction);

      // Act
      const result = await service.uploadApplicationDocument(
        'app-123',
        mockFile,
        uploadMetadata,
        'user-123',
      );

      // Assert
      expect(result.status).toBe('uploaded');

      // Verify that deleteFile was not called since there was no existing file
      expect(mediaService.deleteFile).not.toHaveBeenCalled();

      // Verify transaction was still used
      expect(prismaService.$transaction).toHaveBeenCalled();
    });

    it('should continue upload even if existing file deletion fails', async () => {
      // Arrange - Mock existing document record with file that fails to delete
      const existingDocumentRecord = {
        id: 'app-doc-123',
        application_id: 'app-123',
        document_vault_id: 'existing-vault-123',
        stage_order: 1,
        file_name: 'Test Passport',
        status: 'uploaded',
        document: mockExistingDocumentVault,
      } as any;

      const mockTransaction = jest.fn().mockImplementation(async (callback) => {
        const mockTx = {
          document_vault: {
            update: jest.fn().mockResolvedValue({}),
            delete: jest.fn().mockResolvedValue({}),
          },
          application_document: {
            update: jest.fn().mockResolvedValue({}),
          },
        };
        return await callback(mockTx);
      });

      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(mockApplication);
      jest
        .spyOn(prismaService.application_document, 'findFirst')
        .mockResolvedValue(existingDocumentRecord);
      jest
        .spyOn(documentVaultService, 'uploadApplicationDocument')
        .mockResolvedValue(mockUploadedDocument);
      jest
        .spyOn(mediaService, 'deleteFile')
        .mockResolvedValue(false); // File deletion failed
      jest
        .spyOn(prismaService, '$transaction')
        .mockImplementation(mockTransaction);

      // Act
      const result = await service.uploadApplicationDocument(
        'app-123',
        mockFile,
        uploadMetadata,
        'user-123',
      );

      // Assert
      expect(result.status).toBe('uploaded');

      // Verify that deleteFile was called but failed
      expect(mediaService.deleteFile).toHaveBeenCalledWith(
        'documents/app-123/old-passport.pdf',
      );

      // Verify upload continued despite deletion failure
      expect(documentVaultService.uploadApplicationDocument).toHaveBeenCalled();
      expect(prismaService.$transaction).toHaveBeenCalled();
    });

    it('should throw NotFoundException when application does not exist', async () => {
      // Arrange
      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.uploadApplicationDocument(
          'non-existent-app',
          mockFile,
          uploadMetadata,
          'user-123',
        ),
      ).rejects.toThrow(NotFoundException);

      expect(prismaService.application.findUnique).toHaveBeenCalledWith({
        where: { id: 'non-existent-app' },
        select: {
          id: true,
          user_id: true,
          guest_email: true,
        },
      });
    });

    it('should throw NotFoundException when document record does not exist', async () => {
      // Arrange
      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(mockApplication);
      jest
        .spyOn(prismaService.application_document, 'findFirst')
        .mockResolvedValue(null); // No existing document record

      // Act & Assert
      await expect(
        service.uploadApplicationDocument(
          'app-123',
          mockFile,
          uploadMetadata,
          'user-123',
        ),
      ).rejects.toThrow(NotFoundException);

      expect(prismaService.application_document.findFirst).toHaveBeenCalledWith({
        where: {
          application_id: 'app-123',
          id: 'app-doc-123',
          stage_order: 1,
          file_name: 'Test Passport',
        },
        include: {
          document: true,
        },
      });
    });

    it('should handle document vault service errors with transaction rollback', async () => {
      // Arrange
      const existingDocumentRecord = {
        id: 'app-doc-123',
        application_id: 'app-123',
        document_vault_id: 'existing-vault-123',
        stage_order: 1,
        file_name: 'Test Passport',
        status: 'uploaded',
        document: mockExistingDocumentVault,
      } as any;

      const mockTransaction = jest.fn().mockImplementation(async (callback) => {
        const mockTx = {
          document_vault: {
            update: jest.fn(),
            delete: jest.fn(),
          },
          application_document: {
            update: jest.fn(),
          },
        };
        return await callback(mockTx);
      });

      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(mockApplication);
      jest
        .spyOn(prismaService.application_document, 'findFirst')
        .mockResolvedValue(existingDocumentRecord);
      jest
        .spyOn(documentVaultService, 'uploadApplicationDocument')
        .mockRejectedValue(new BadRequestException('File type not supported'));
      jest
        .spyOn(mediaService, 'deleteFile')
        .mockResolvedValue(true);
      jest
        .spyOn(prismaService, '$transaction')
        .mockImplementation(mockTransaction);

      // Act & Assert
      await expect(
        service.uploadApplicationDocument(
          'app-123',
          mockFile,
          uploadMetadata,
          'user-123',
        ),
      ).rejects.toThrow(BadRequestException);

      // Verify transaction was attempted
      expect(prismaService.$transaction).toHaveBeenCalled();
    });

    it('should provide user-friendly error messages for different error types', async () => {
      // Arrange
      const existingDocumentRecord = {
        id: 'app-doc-123',
        application_id: 'app-123',
        document_vault_id: 'existing-vault-123',
        stage_order: 1,
        file_name: 'Test Passport',
        status: 'uploaded',
        document: mockExistingDocumentVault,
      } as any;

      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(mockApplication);
      jest
        .spyOn(prismaService.application_document, 'findFirst')
        .mockResolvedValue(existingDocumentRecord);

      // Test storage error
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new Error('storage connection failed'));

      await expect(
        service.uploadApplicationDocument(
          'app-123',
          mockFile,
          uploadMetadata,
          'user-123',
        ),
      ).rejects.toThrow('Failed to upload document to storage');

      // Test transaction error
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new Error('transaction failed'));

      await expect(
        service.uploadApplicationDocument(
          'app-123',
          mockFile,
          uploadMetadata,
          'user-123',
        ),
      ).rejects.toThrow('Document upload failed due to a system error');
    });

    it('should handle metadata without expiry date', async () => {
      // Arrange
      const metadataWithoutExpiry = { ...uploadMetadata };
      delete metadataWithoutExpiry.expiry_date;

      const existingDocumentRecord = {
        id: 'app-doc-123',
        application_id: 'app-123',
        document_vault_id: 'existing-vault-123',
        stage_order: 1,
        file_name: 'Test Passport',
        status: 'pending',
        document: {
          ...mockExistingDocumentVault,
          file_path: '', // No existing file
        },
      } as any;

      const mockTransaction = jest.fn().mockImplementation(async (callback) => {
        const mockTx = {
          document_vault: {
            update: jest.fn().mockResolvedValue({}),
            delete: jest.fn().mockResolvedValue({}),
          },
          application_document: {
            update: jest.fn().mockResolvedValue({}),
          },
        };
        return await callback(mockTx);
      });

      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(mockApplication);
      jest
        .spyOn(prismaService.application_document, 'findFirst')
        .mockResolvedValue(existingDocumentRecord);
      jest
        .spyOn(documentVaultService, 'uploadApplicationDocument')
        .mockResolvedValue(mockUploadedDocument);
      jest
        .spyOn(prismaService, '$transaction')
        .mockImplementation(mockTransaction);

      // Act
      await service.uploadApplicationDocument(
        'app-123',
        mockFile,
        metadataWithoutExpiry,
        'user-123',
      );

      // Assert
      expect(
        documentVaultService.uploadApplicationDocument,
      ).toHaveBeenCalledWith(
        mockFile,
        'app-123',
        expect.objectContaining({
          document_name: 'Test Passport',
          document_type: 'Other',
          document_category: 'identity',
          expiry_date: undefined,
          application_id: 'app-123',
          guest_email: null,
        }),
        'user-123',
      );

      expect(prismaService.$transaction).toHaveBeenCalled();
    });
  });
});
