/**
 * Notification Controller Unit Tests
 *
 * Comprehensive test suite for NotificationController covering endpoint functionality,
 * authentication, validation, error handling, and response formatting.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-10
 */

import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { NotificationController } from '../../src/application/controllers/notification.controller';
import { NotificationService } from '../../src/application/services/notification.service';
import {
  NotificationSettingsDto,
  UpdateNotificationSettingsDto,
} from '../../src/application/dto/notification.dto';
import { IJWTPayload } from '../../src/types/auth';

describe('NotificationController', () => {
  let controller: NotificationController;
  let notificationService: jest.Mocked<NotificationService>;

  const mockJWTPayload: IJWTPayload = {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    provider: 'local',
  };

  const mockNotificationSettings: NotificationSettingsDto = {
    agent_assigned: true,
    case_status_update: true,
    agent_query: true,
    document_rejection: true,
    missing_document_reminder_days: 7,
    system_maintenance: true,
    upcoming_deadline_alerts: true,
    final_decision_issued: true,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NotificationController],
      providers: [
        {
          provide: NotificationService,
          useValue: {
            getUserNotificationSettings: jest.fn(),
            updateUserNotificationSettings: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<NotificationController>(NotificationController);
    notificationService = module.get(NotificationService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('getUserNotificationSettings', () => {
    it('should return user notification settings successfully', async () => {
      // Arrange
      notificationService.getUserNotificationSettings.mockResolvedValue(
        mockNotificationSettings,
      );

      // Act
      const result =
        await controller.getUserNotificationSettings(mockJWTPayload);

      // Assert
      expect(result).toEqual(mockNotificationSettings);
      expect(
        notificationService.getUserNotificationSettings,
      ).toHaveBeenCalledWith(mockJWTPayload.id);
    });

    it('should handle service errors and throw HttpException', async () => {
      // Arrange
      const serviceError = new Error('Database connection failed');
      notificationService.getUserNotificationSettings.mockRejectedValue(
        serviceError,
      );

      // Act & Assert
      await expect(
        controller.getUserNotificationSettings(mockJWTPayload),
      ).rejects.toThrow(
        new HttpException(
          {
            success: false,
            message: 'Failed to retrieve notification settings',
            error: 'Internal server error',
          },
          HttpStatus.INTERNAL_SERVER_ERROR,
        ),
      );
    });
  });

  describe('updateUserNotificationSettings', () => {
    const validUpdateDto: UpdateNotificationSettingsDto = {
      agent_assigned: false,
      missing_document_reminder_days: 14,
      system_maintenance: false,
    };

    const updatedSettings: NotificationSettingsDto = {
      ...mockNotificationSettings,
      agent_assigned: false,
      missing_document_reminder_days: 14,
      system_maintenance: false,
    };

    it('should update notification settings successfully', async () => {
      // Arrange
      notificationService.updateUserNotificationSettings.mockResolvedValue(
        updatedSettings,
      );

      // Act
      const result = await controller.updateUserNotificationSettings(
        mockJWTPayload,
        validUpdateDto,
      );

      // Assert
      expect(result).toEqual(updatedSettings);
      expect(
        notificationService.updateUserNotificationSettings,
      ).toHaveBeenCalledWith(mockJWTPayload.id, validUpdateDto);
    });

    it('should validate that at least one field is provided', async () => {
      // Arrange
      const emptyUpdateDto: UpdateNotificationSettingsDto = {};

      // Act & Assert
      await expect(
        controller.updateUserNotificationSettings(
          mockJWTPayload,
          emptyUpdateDto,
        ),
      ).rejects.toThrow(
        new HttpException(
          {
            success: false,
            message: 'At least one notification setting field must be provided',
            error: 'Validation failed',
          },
          HttpStatus.BAD_REQUEST,
        ),
      );
    });

    it('should handle validation errors from service', async () => {
      // Arrange
      const validationError = new Error(
        'Missing document reminder days must be between 1 and 365 days',
      );
      notificationService.updateUserNotificationSettings.mockRejectedValue(
        validationError,
      );

      // Act & Assert
      await expect(
        controller.updateUserNotificationSettings(
          mockJWTPayload,
          validUpdateDto,
        ),
      ).rejects.toThrow(
        new HttpException(
          {
            success: false,
            message: 'Invalid notification settings provided',
            error: validationError.message,
          },
          HttpStatus.BAD_REQUEST,
        ),
      );
    });

    it('should handle generic service errors', async () => {
      // Arrange
      const serviceError = new Error('Database connection failed');
      notificationService.updateUserNotificationSettings.mockRejectedValue(
        serviceError,
      );

      // Act & Assert
      await expect(
        controller.updateUserNotificationSettings(
          mockJWTPayload,
          validUpdateDto,
        ),
      ).rejects.toThrow(
        new HttpException(
          {
            success: false,
            message: 'Failed to update notification settings',
            error: 'Internal server error',
          },
          HttpStatus.INTERNAL_SERVER_ERROR,
        ),
      );
    });

    it('should re-throw HttpExceptions as-is', async () => {
      // Arrange
      const httpException = new HttpException(
        'Custom error',
        HttpStatus.FORBIDDEN,
      );
      notificationService.updateUserNotificationSettings.mockRejectedValue(
        httpException,
      );

      // Act & Assert
      await expect(
        controller.updateUserNotificationSettings(
          mockJWTPayload,
          validUpdateDto,
        ),
      ).rejects.toThrow(httpException);
    });

    it('should accept partial updates with valid fields', async () => {
      // Arrange
      const partialUpdateDto: UpdateNotificationSettingsDto = {
        agent_assigned: false,
      };
      const partialUpdatedSettings: NotificationSettingsDto = {
        ...mockNotificationSettings,
        agent_assigned: false,
      };
      notificationService.updateUserNotificationSettings.mockResolvedValue(
        partialUpdatedSettings,
      );

      // Act
      const result = await controller.updateUserNotificationSettings(
        mockJWTPayload,
        partialUpdateDto,
      );

      // Assert
      expect(result).toEqual(partialUpdatedSettings);
      expect(
        notificationService.updateUserNotificationSettings,
      ).toHaveBeenCalledWith(mockJWTPayload.id, partialUpdateDto);
    });

    it('should handle boolean field updates correctly', async () => {
      // Arrange
      const booleanUpdateDto: UpdateNotificationSettingsDto = {
        agent_assigned: false,
        case_status_update: false,
        agent_query: true,
        document_rejection: false,
        system_maintenance: true,
        upcoming_deadline_alerts: false,
        final_decision_issued: true,
      };
      const booleanUpdatedSettings: NotificationSettingsDto = {
        ...mockNotificationSettings,
        ...booleanUpdateDto,
      };
      notificationService.updateUserNotificationSettings.mockResolvedValue(
        booleanUpdatedSettings,
      );

      // Act
      const result = await controller.updateUserNotificationSettings(
        mockJWTPayload,
        booleanUpdateDto,
      );

      // Assert
      expect(result).toEqual(booleanUpdatedSettings);
      expect(result.agent_assigned).toBe(false);
      expect(result.case_status_update).toBe(false);
      expect(result.agent_query).toBe(true);
      expect(result.document_rejection).toBe(false);
      expect(result.system_maintenance).toBe(true);
      expect(result.upcoming_deadline_alerts).toBe(false);
      expect(result.final_decision_issued).toBe(true);
    });

    it('should handle missing_document_reminder_days updates correctly', async () => {
      // Arrange
      const validDays = [1, 30, 90, 180, 365];

      for (const days of validDays) {
        const daysUpdateDto: UpdateNotificationSettingsDto = {
          missing_document_reminder_days: days,
        };
        const daysUpdatedSettings: NotificationSettingsDto = {
          ...mockNotificationSettings,
          missing_document_reminder_days: days,
        };
        notificationService.updateUserNotificationSettings.mockResolvedValue(
          daysUpdatedSettings,
        );

        // Act
        const result = await controller.updateUserNotificationSettings(
          mockJWTPayload,
          daysUpdateDto,
        );

        // Assert
        expect(result.missing_document_reminder_days).toBe(days);
      }
    });
  });

  describe('Input Validation Edge Cases', () => {
    it('should handle undefined values in update DTO', async () => {
      // Arrange
      const undefinedUpdateDto: UpdateNotificationSettingsDto = {
        agent_assigned: undefined,
        case_status_update: true,
        missing_document_reminder_days: undefined,
      };
      const filteredUpdatedSettings: NotificationSettingsDto = {
        ...mockNotificationSettings,
        case_status_update: true,
      };
      notificationService.updateUserNotificationSettings.mockResolvedValue(
        filteredUpdatedSettings,
      );

      // Act
      const result = await controller.updateUserNotificationSettings(
        mockJWTPayload,
        undefinedUpdateDto,
      );

      // Assert
      expect(result).toEqual(filteredUpdatedSettings);
    });

    it('should handle mixed valid and undefined fields', async () => {
      // Arrange
      const mixedUpdateDto: UpdateNotificationSettingsDto = {
        agent_assigned: false,
        case_status_update: undefined,
        agent_query: true,
        document_rejection: undefined,
        missing_document_reminder_days: 30,
        system_maintenance: undefined,
        upcoming_deadline_alerts: false,
        final_decision_issued: undefined,
      };
      const mixedUpdatedSettings: NotificationSettingsDto = {
        ...mockNotificationSettings,
        agent_assigned: false,
        agent_query: true,
        missing_document_reminder_days: 30,
        upcoming_deadline_alerts: false,
      };
      notificationService.updateUserNotificationSettings.mockResolvedValue(
        mixedUpdatedSettings,
      );

      // Act
      const result = await controller.updateUserNotificationSettings(
        mockJWTPayload,
        mixedUpdateDto,
      );

      // Assert
      expect(result).toEqual(mixedUpdatedSettings);
    });
  });
});

/**
 * Notification DTO Validation Tests
 *
 * Tests for notification DTO validation including edge cases and error messages.
 */

import { validate } from 'class-validator';

describe('Notification DTO Validation', () => {
  describe('UpdateNotificationSettingsDto', () => {
    it('should validate successfully with all valid fields', async () => {
      const dto = new UpdateNotificationSettingsDto();
      dto.agent_assigned = true;
      dto.case_status_update = false;
      dto.agent_query = true;
      dto.document_rejection = false;
      dto.missing_document_reminder_days = 30;
      dto.system_maintenance = true;
      dto.upcoming_deadline_alerts = false;
      dto.final_decision_issued = true;

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate successfully with only some fields', async () => {
      const dto = new UpdateNotificationSettingsDto();
      dto.agent_assigned = false;
      dto.missing_document_reminder_days = 14;

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation for missing_document_reminder_days below minimum', async () => {
      const dto = new UpdateNotificationSettingsDto();
      dto.missing_document_reminder_days = 0;

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const dayError = errors.find(
        (error) => error.property === 'missing_document_reminder_days',
      );
      expect(dayError).toBeDefined();
      expect(dayError?.constraints?.min).toContain(
        'Missing document reminder days must be at least 1 day',
      );
    });

    it('should fail validation for missing_document_reminder_days above maximum', async () => {
      const dto = new UpdateNotificationSettingsDto();
      dto.missing_document_reminder_days = 366;

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const dayError = errors.find(
        (error) => error.property === 'missing_document_reminder_days',
      );
      expect(dayError).toBeDefined();
      expect(dayError?.constraints?.max).toContain(
        'Missing document reminder days cannot exceed 365 days',
      );
    });

    it('should validate valid day ranges', async () => {
      const validDays = [1, 7, 30, 90, 180, 365];

      for (const days of validDays) {
        const dto = new UpdateNotificationSettingsDto();
        dto.missing_document_reminder_days = days;

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should fail validation for non-integer missing_document_reminder_days', async () => {
      const dto = new UpdateNotificationSettingsDto();
      (dto as any).missing_document_reminder_days = 7.5;

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const dayError = errors.find(
        (error) => error.property === 'missing_document_reminder_days',
      );
      expect(dayError).toBeDefined();
    });

    it('should fail validation for non-boolean notification flags', async () => {
      const dto = new UpdateNotificationSettingsDto();
      (dto as any).agent_assigned = 'true'; // String instead of boolean

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const boolError = errors.find(
        (error) => error.property === 'agent_assigned',
      );
      expect(boolError).toBeDefined();
    });
  });
});
