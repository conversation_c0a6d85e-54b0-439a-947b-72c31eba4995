/**
 * Email Notification Tests
 *
 * Tests for the enhanced email notification functionality in ApplicationService.
 * Covers status change notifications, stage change notifications, and error handling.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { ApplicationService } from '../../src/application/application.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { ApplicationFormService } from '../../src/application/services/application-form.service';
import { ApplicationDocumentService } from '../../src/application/services/application-document.service';
import { ApplicationTransformerService } from '../../src/application/services/application-transformer.service';
import { EmailTemplateIntegrationService } from '../../src/application/services/email-template-integration.service';
import { MediaService } from '../../src/media/media.service';
import { DocumentVaultService } from '../../src/application/services/document-vault.service';
import { NotificationService } from '../../src/application/services/notification.service';
import { NotificationSettingsStorageService } from '../../src/utils/notification-settings-storage.service';
import { DocumentStatusEnum } from '../../src/application/dto/document-status.dto';

describe('ApplicationService - Email Notifications', () => {
  let service: ApplicationService;
  let prismaService: PrismaService;
  let loggerService: LoggerService;
  let emailTemplateService: EmailTemplateIntegrationService;
  let notificationService: NotificationService;

  const mockPrismaService = {
    application: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    application_document: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    workflow_template: {
      findUnique: jest.fn(),
    },
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
  };

  const mockNestLogger = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    verbose: jest.fn(),
  };

  const mockEmailTemplateService = {
    sendApplicationStatusChangeEmail: jest.fn(),
    sendApplicationStageChangeEmail: jest.fn(),
  };

  const mockNotificationService = {
    sendNotification: jest.fn(),
    shouldReceiveNotification: jest.fn(),
  };

  const mockApplicationFormService = {
    updateFormFields: jest.fn(),
  };

  const mockApplicationDocumentService = {};
  const mockApplicationTransformerService = {};
  const mockMediaService = {};
  const mockDocumentVaultService = {};
  const mockNotificationSettingsStorage = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationService,
        { provide: PrismaService, useValue: mockPrismaService },
        { provide: LoggerService, useValue: mockLoggerService },
        { provide: ApplicationFormService, useValue: mockApplicationFormService },
        { provide: ApplicationDocumentService, useValue: mockApplicationDocumentService },
        { provide: ApplicationTransformerService, useValue: mockApplicationTransformerService },
        { provide: EmailTemplateIntegrationService, useValue: mockEmailTemplateService },
        { provide: MediaService, useValue: mockMediaService },
        { provide: DocumentVaultService, useValue: mockDocumentVaultService },
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: NotificationSettingsStorageService, useValue: mockNotificationSettingsStorage },
      ],
    }).compile();

    service = module.get<ApplicationService>(ApplicationService);
    prismaService = module.get<PrismaService>(PrismaService);
    loggerService = module.get<LoggerService>(LoggerService);
    emailTemplateService = module.get<EmailTemplateIntegrationService>(EmailTemplateIntegrationService);
    notificationService = module.get<NotificationService>(NotificationService);

    // Mock the private logger property
    (service as any).logger = mockNestLogger;

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  describe('updateCurrentStep - Stage Change Notifications', () => {
    const mockApplication = {
      id: 'app-123',
      current_step: '1',
      application_number: 'APP-2024-001',
      user_id: 'user-123',
      guest_email: null,
      guest_name: null,
      workflow_template_id: 'wf-123',
      workflow_template: {
        workflowTemplate: [
          { stageName: 'Initial Review', stageOrder: 1, description: 'First stage review' },
          { stageName: 'Document Verification', stageOrder: 2, description: 'Verify documents' },
        ],
        name: 'Immigration Service',
      },
      user: {
        id: 'user-123',
        name: 'John Doe',
        email: '<EMAIL>',
      },
    };

    it('should send stage change notification with actual stage names when step changes', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(mockApplication);
      mockPrismaService.application.update.mockResolvedValue({ ...mockApplication, current_step: '2' });
      mockPrismaService.workflow_template.findUnique.mockResolvedValue({
        name: 'Immigration Service',
        description: 'Immigration workflow',
        workflowTemplate: [
          { stageName: 'Initial Review', stageOrder: 1, description: 'First stage review' },
          { stageName: 'Document Verification', stageOrder: 2, description: 'Verify documents' },
        ],
      });
      mockEmailTemplateService.sendApplicationStageChangeEmail.mockResolvedValue(undefined);

      const result = await service.updateCurrentStep('app-123', '2');

      expect(result).toEqual({
        applicationId: 'app-123',
        currentStep: '2',
      });

      expect(mockEmailTemplateService.sendApplicationStageChangeEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        expect.objectContaining({
          applicantName: 'John Doe',
          applicationId: 'APP-2024-001',
          serviceName: 'Immigration Service',
          previousStage: 'Initial Review', // Actual stage name from workflow template
          currentStage: 'Document Verification', // Actual stage name from workflow template
          stageChangeDate: expect.any(Date),
          nextSteps: expect.any(Array),
          stageDescription: 'Verify documents', // From workflow template
          websiteUrl: expect.any(String),
          additionalNotes: expect.any(String),
        })
      );

      expect(mockLoggerService.info).toHaveBeenCalledWith(
        'Stage change notification sent successfully',
        expect.objectContaining({
          applicationId: 'app-123',
          previousStep: '1',
          currentStep: '2',
        })
      );
    });

    it('should not send notification when step does not change', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(mockApplication);
      mockPrismaService.application.update.mockResolvedValue(mockApplication);

      await service.updateCurrentStep('app-123', '1');

      expect(mockEmailTemplateService.sendApplicationStageChangeEmail).not.toHaveBeenCalled();
    });

    it('should handle email notification failure gracefully', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(mockApplication);
      mockPrismaService.application.update.mockResolvedValue({ ...mockApplication, current_step: '2' });
      mockEmailTemplateService.sendApplicationStageChangeEmail.mockRejectedValue(new Error('Email service down'));

      const result = await service.updateCurrentStep('app-123', '2');

      expect(result).toEqual({
        applicationId: 'app-123',
        currentStep: '2',
      });

      expect(mockLoggerService.error).toHaveBeenCalledWith(
        'Failed to send stage change notification email',
        expect.any(Error),
        expect.objectContaining({
          applicationId: 'app-123',
          previousStep: '1',
          currentStep: '2',
        })
      );
    });

    it('should throw NotFoundException when application not found', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(null);

      await expect(service.updateCurrentStep('nonexistent', '2')).rejects.toThrow(NotFoundException);
      expect(mockEmailTemplateService.sendApplicationStageChangeEmail).not.toHaveBeenCalled();
    });

    it('should handle guest applications', async () => {
      const guestApplication = {
        ...mockApplication,
        user_id: null,
        user: null,
        guest_email: '<EMAIL>',
        guest_name: 'Guest User',
      };

      mockPrismaService.application.findUnique.mockResolvedValue(guestApplication);
      mockPrismaService.application.update.mockResolvedValue({ ...guestApplication, current_step: '2' });
      mockEmailTemplateService.sendApplicationStageChangeEmail.mockResolvedValue(undefined);

      await service.updateCurrentStep('app-123', '2');

      expect(mockEmailTemplateService.sendApplicationStageChangeEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        expect.objectContaining({
          applicantName: 'Guest User',
        })
      );
    });

    it('should fallback to generic stage names when workflow template has no stage names', async () => {
      const applicationWithoutStageNames = {
        ...mockApplication,
        workflow_template: {
          workflowTemplate: [
            { stageOrder: 1, description: 'First stage' },
            { stageOrder: 2, description: 'Second stage' },
          ],
          name: 'Immigration Service',
        },
      };

      mockPrismaService.application.findUnique.mockResolvedValue(applicationWithoutStageNames);
      mockPrismaService.application.update.mockResolvedValue({ ...applicationWithoutStageNames, current_step: '2' });
      mockPrismaService.workflow_template.findUnique.mockResolvedValue({
        name: 'Immigration Service',
        description: 'Immigration workflow',
        workflowTemplate: [
          { stageOrder: 1, description: 'First stage' }, // No stageName field
          { stageOrder: 2, description: 'Second stage' }, // No stageName field
        ],
      });
      mockEmailTemplateService.sendApplicationStageChangeEmail.mockResolvedValue(undefined);

      await service.updateCurrentStep('app-123', '2');

      expect(mockEmailTemplateService.sendApplicationStageChangeEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        expect.objectContaining({
          previousStage: 'Step 1', // Fallback to generic name
          currentStage: 'Step 2', // Fallback to generic name
        })
      );
    });

    it('should handle malformed workflow template gracefully', async () => {
      const applicationWithMalformedTemplate = {
        ...mockApplication,
        workflow_template: {
          workflowTemplate: 'invalid-template', // Not an array
          name: 'Immigration Service',
        },
      };

      mockPrismaService.application.findUnique.mockResolvedValue(applicationWithMalformedTemplate);
      mockPrismaService.application.update.mockResolvedValue({ ...applicationWithMalformedTemplate, current_step: '2' });
      mockPrismaService.workflow_template.findUnique.mockResolvedValue({
        name: 'Immigration Service',
        description: 'Immigration workflow',
        workflowTemplate: 'invalid-template', // Not an array - this will trigger the error handling
      });
      mockEmailTemplateService.sendApplicationStageChangeEmail.mockResolvedValue(undefined);

      await service.updateCurrentStep('app-123', '2');

      expect(mockEmailTemplateService.sendApplicationStageChangeEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        expect.objectContaining({
          previousStage: 'Step 1', // Fallback to generic name
          currentStage: 'Step 2', // Fallback to generic name
        })
      );

      // The warning should be logged when the helper method fails to extract stage names
      expect(mockLoggerService.warn).toHaveBeenCalledWith(
        'Failed to extract stage names from workflow template, using fallback names',
        expect.objectContaining({
          applicationId: 'app-123',
          currentStep: '2',
          previousStep: '1',
        })
      );
    });

    it('should handle out-of-bounds step numbers gracefully', async () => {
      // Create an application with a larger workflow template to avoid validation error
      const applicationWithLargerTemplate = {
        ...mockApplication,
        workflow_template: {
          workflowTemplate: [
            { stageName: 'Initial Review', stageOrder: 1, description: 'First stage review' },
            { stageName: 'Document Verification', stageOrder: 2, description: 'Verify documents' },
            { stageName: 'Stage 3', stageOrder: 3, description: 'Third stage' },
            { stageName: 'Stage 4', stageOrder: 4, description: 'Fourth stage' },
            { stageName: 'Stage 5', stageOrder: 5, description: 'Fifth stage' },
            { stageName: 'Stage 6', stageOrder: 6, description: 'Sixth stage' },
          ],
          name: 'Immigration Service',
        },
      };

      mockPrismaService.application.findUnique.mockResolvedValue(applicationWithLargerTemplate);
      mockPrismaService.application.update.mockResolvedValue({ ...applicationWithLargerTemplate, current_step: '5' });
      mockPrismaService.workflow_template.findUnique.mockResolvedValue({
        name: 'Immigration Service',
        description: 'Immigration workflow',
        workflowTemplate: [
          { stageName: 'Initial Review', stageOrder: 1, description: 'First stage review' },
          { stageName: 'Document Verification', stageOrder: 2, description: 'Verify documents' },
          { stageName: 'Stage 3', stageOrder: 3, description: 'Third stage' },
          { stageName: 'Stage 4', stageOrder: 4, description: 'Fourth stage' },
          { stageName: 'Stage 5', stageOrder: 5, description: 'Fifth stage' },
          { stageName: 'Stage 6', stageOrder: 6, description: 'Sixth stage' },
        ],
      });
      mockEmailTemplateService.sendApplicationStageChangeEmail.mockResolvedValue(undefined);

      await service.updateCurrentStep('app-123', '5');

      expect(mockEmailTemplateService.sendApplicationStageChangeEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        expect.objectContaining({
          previousStage: 'Initial Review', // Previous step exists
          currentStage: 'Stage 5', // Actual stage name from template
        })
      );
    });
  });

  describe('updateDocumentStatus - Document Status Change Notifications', () => {
    const mockApplicationDocument = {
      id: 'doc-123',
      status: 'pending',
      application: {
        id: 'app-123',
        application_number: 'APP-2024-001',
        user: {
          id: 'user-123',
          name: 'John Doe',
          email: '<EMAIL>',
        },
        guest_email: null,
        guest_name: null,
      },
      document: {
        id: 'vault-123',
        document_name: 'Passport',
        original_filename: 'passport.pdf',
      },
    };

    it('should send document status notification when status changes', async () => {
      mockPrismaService.application_document.findUnique.mockResolvedValue(mockApplicationDocument);
      mockPrismaService.application_document.update.mockResolvedValue({
        ...mockApplicationDocument,
        status: 'approved',
      });
      mockNotificationService.sendNotification.mockResolvedValue(undefined);

      const result = await service.updateDocumentStatus(
        'doc-123',
        { status: DocumentStatusEnum.APPROVED },
        'reviewer-123'
      );

      expect(result.status).toBe(DocumentStatusEnum.APPROVED);
      expect(mockNotificationService.sendNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          recipient_email: '<EMAIL>',
          subject: 'Document Approved - Passport',
          message_body: expect.stringContaining('changed from "PENDING" to "Approved"'),
          notification_type: 'Email',
        })
      );
    });

    it('should not send notification when status does not change', async () => {
      mockPrismaService.application_document.findUnique.mockResolvedValue(mockApplicationDocument);
      mockPrismaService.application_document.update.mockResolvedValue(mockApplicationDocument);

      await service.updateDocumentStatus(
        'doc-123',
        { status: DocumentStatusEnum.PENDING },
        'reviewer-123'
      );

      expect(mockNotificationService.sendNotification).not.toHaveBeenCalled();
    });

    it('should handle email notification failure gracefully', async () => {
      mockPrismaService.application_document.findUnique.mockResolvedValue(mockApplicationDocument);
      mockPrismaService.application_document.update.mockResolvedValue({
        ...mockApplicationDocument,
        status: 'approved',
      });
      mockNotificationService.sendNotification.mockRejectedValue(new Error('Email service down'));

      const result = await service.updateDocumentStatus(
        'doc-123',
        { status: DocumentStatusEnum.APPROVED },
        'reviewer-123'
      );

      expect(result.status).toBe(DocumentStatusEnum.APPROVED);
      // The error is logged by the sendDocumentStatusNotification method using this.logger.error
      expect(mockNestLogger.error).toHaveBeenCalledWith(
        'Failed to send document status notification',
        expect.any(Error)
      );
    });

    it('should require reason for rejection status', async () => {
      mockPrismaService.application_document.findUnique.mockResolvedValue(mockApplicationDocument);

      await expect(
        service.updateDocumentStatus(
          'doc-123',
          { status: DocumentStatusEnum.REJECTED },
          'reviewer-123'
        )
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('extractStageNamesFromTemplate - Helper Method', () => {
    it('should extract stage names correctly from valid workflow template', () => {
      const workflowTemplate = [
        { stageName: 'Document Collection', stageOrder: 1, description: 'Collect documents', estimatedDuration: 5 },
        { stageName: 'Initial Review', stageOrder: 2, description: 'Review application', estimatedDuration: 3 },
        { stageName: 'Final Decision', stageOrder: 3, description: 'Make final decision', estimatedDuration: 2 },
      ];

      const result = (service as any).extractStageNamesFromTemplate(workflowTemplate, '2', '1');

      expect(result).toEqual({
        currentStageName: 'Initial Review',
        previousStageName: 'Document Collection',
        stageDescription: 'Review application',
        estimatedDuration: '3',
      });
    });

    it('should handle missing stage names gracefully', () => {
      const workflowTemplate = [
        { stageOrder: 1, description: 'First stage' },
        { stageOrder: 2, description: 'Second stage' },
      ];

      const result = (service as any).extractStageNamesFromTemplate(workflowTemplate, '2', '1');

      expect(result).toEqual({
        currentStageName: undefined,
        previousStageName: undefined,
        stageDescription: 'Second stage',
        estimatedDuration: undefined,
      });
    });

    it('should handle invalid workflow template', () => {
      expect(() => {
        (service as any).extractStageNamesFromTemplate(null, '2', '1');
      }).toThrow('Workflow template is not a valid array');

      expect(() => {
        (service as any).extractStageNamesFromTemplate('invalid', '2', '1');
      }).toThrow('Workflow template is not a valid array');
    });

    it('should handle out-of-bounds step numbers', () => {
      const workflowTemplate = [
        { stageName: 'Document Collection', stageOrder: 1 },
      ];

      const result = (service as any).extractStageNamesFromTemplate(workflowTemplate, '5', '3');

      expect(result).toEqual({
        currentStageName: undefined,
        previousStageName: undefined,
        stageDescription: undefined,
        estimatedDuration: undefined,
      });
    });

    it('should handle first step with no previous step', () => {
      const workflowTemplate = [
        { stageName: 'Document Collection', stageOrder: 1, description: 'Collect documents' },
        { stageName: 'Initial Review', stageOrder: 2, description: 'Review application' },
      ];

      const result = (service as any).extractStageNamesFromTemplate(workflowTemplate, '1');

      expect(result).toEqual({
        currentStageName: 'Document Collection',
        previousStageName: undefined,
        stageDescription: 'Collect documents',
        estimatedDuration: undefined,
      });
    });
  });
});
