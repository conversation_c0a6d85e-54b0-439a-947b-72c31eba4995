module.exports = {
  displayName: 'Guards Module Tests',
  preset: 'ts-jest',
  testEnvironment: 'node',
  rootDir: '../../',
  testMatch: ['<rootDir>/test/guards/**/*.spec.ts'],
  setupFilesAfterEnv: ['<rootDir>/test/config/jest.setup.js'],
  collectCoverageFrom: [
    'src/guards/**/*.(t|j)s',
    '!src/guards/**/*.spec.ts',
    '!src/guards/**/*.interface.ts',
    '!src/guards/**/index.ts',
  ],
  coverageDirectory: '<rootDir>/coverage/guards',
  coverageReporters: ['text', 'lcov', 'html'],
  testTimeout: 30000,
  verbose: true,
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 90,
      lines: 85,
      statements: 85,
    },
    'src/guards/jwt.guard.ts': {
      branches: 90,
      functions: 95,
      lines: 90,
      statements: 90,
    },
  },
};
