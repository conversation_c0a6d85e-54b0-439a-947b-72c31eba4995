module.exports = {
  displayName: 'Template Tests',
  rootDir: '../../',
  testMatch: ['<rootDir>/test/template/**/*.spec.ts'],
  moduleFileExtensions: ['js', 'json', 'ts', 'tsx'],
  transform: {
    '^.+\\.(t|j)sx?$': 'ts-jest',
  },
  collectCoverageFrom: ['src/template/**/*.(t|j)s(x)?'],
  coverageDirectory: 'coverage/template',
  testEnvironment: 'node',
  moduleNameMapping: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
  resolver: undefined,
  extensionsToTreatAsEsm: [],
};
