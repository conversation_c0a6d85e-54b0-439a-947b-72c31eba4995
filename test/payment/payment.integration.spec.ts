/**
 * Payment Integration Tests
 *
 * Comprehensive integration test suite for the Payment table and related operations.
 * Tests mocked database interactions, data integrity, and business logic integration.
 *
 * @desc Tests the unified payment table with mocked Prisma operations
 * @assumptions Uses mocked database operations for reliable testing
 * @integration_level Service, Controller integration with mocked database
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { PrismaService } from '../../src/utils/prisma.service';
import { UnifiedPaymentService } from '../../src/payment/unified-payment.service';
import { UnifiedPaymentController } from '../../src/payment/unified-payment.controller';
import { MailerService } from '../../src/mailer/mailer.service';
import { LoggerService } from '../../src/utils/logger.service';
import { STRIPE_CLIENT } from '../../src/config/stripe.config';
import { JwtService } from '@nestjs/jwt';
import {
  CreateUnifiedPaymentDto,
  PaymentFiltersDto,
  ServiceType,
  PaymentType,
} from '../../src/payment/dto/payment.dto';
import { Status } from '@prisma/client';
import { ApplicationIntegrationService } from '../../src/application/services/application-integration.service';

// Mock external dependencies
jest.mock('@react-email/components', () => ({
  render: jest.fn().mockReturnValue('<html>Mock Email</html>'),
}));

describe('Payment Integration Tests', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let paymentService: UnifiedPaymentService;

  // Test data IDs
  const testUserId = 'test-user-id-123';
  const testServiceId = 'test-service-id-123';
  const testPackageId = 'test-package-id-123';
  const testImmigrationServiceId = 'test-immigration-id-123';
  const testTrainingId = 'test-training-id-123';

  // Mock implementations
  const mockStripeClient = {
    checkout: {
      sessions: {
        create: jest.fn().mockResolvedValue({
          id: 'cs_test_123',
          url: 'https://checkout.stripe.com/pay/cs_test_123',
        }),
      },
    },
    webhooks: {
      constructEvent: jest.fn(),
    },
  };

  const mockMailerService = {
    sendEmail: jest.fn().mockResolvedValue(true),
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  // Mock Prisma Service
  const mockPrismaService = {
    payment: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
      count: jest.fn(),
      aggregate: jest.fn(),
      groupBy: jest.fn(),
      createMany: jest.fn(),
    },
    user: {
      create: jest.fn(),
      findUnique: jest.fn(),
      deleteMany: jest.fn(),
    },
    service: {
      create: jest.fn(),
      findUnique: jest.fn(),
      deleteMany: jest.fn(),
    },
    packages: {
      create: jest.fn(),
      findUnique: jest.fn(),
      deleteMany: jest.fn(),
    },
    immigration_service: {
      create: jest.fn(),
      findUnique: jest.fn(),
      deleteMany: jest.fn(),
    },
    training: {
      create: jest.fn(),
      findUnique: jest.fn(),
      deleteMany: jest.fn(),
    },
    $connect: jest.fn(),
    $disconnect: jest.fn(),
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [UnifiedPaymentController],
      providers: [
        UnifiedPaymentService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        JwtService,
        {
          provide: STRIPE_CLIENT,
          useValue: mockStripeClient,
        },
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
        {
          provide: ApplicationIntegrationService,
          useValue: {
            createApplicationFromPayment: jest.fn(),
            validateWorkflowTemplate: jest.fn(),
          },
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    prisma = moduleFixture.get<PrismaService>(PrismaService);
    paymentService = moduleFixture.get<UnifiedPaymentService>(
      UnifiedPaymentService,
    );
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  describe('Payment Table CRUD Operations', () => {
    describe('Create Payment Records', () => {
      it('should create user payment for service', async () => {
        // Arrange
        const paymentData = {
          amount: 10000,
          status: 'pending',
          payment_type: 'user',
          service_type: 'service',
          progress: Status.Pending,
          userId: testUserId,
          serviceId: testServiceId,
          stripe_session_id: 'cs_test_user_service',
        };

        const mockPayment = {
          id: 'payment_test_123',
          ...paymentData,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const mockPaymentWithRelations = {
          ...mockPayment,
          user: {
            id: testUserId,
            name: 'Test User',
            email: '<EMAIL>',
          },
          service: { id: testServiceId, name: 'Test Service' },
        };

        mockPrismaService.payment.create.mockResolvedValue(mockPayment);
        mockPrismaService.payment.findUnique.mockResolvedValue(
          mockPaymentWithRelations,
        );

        // Act
        const payment = await prisma.payment.create({
          data: paymentData,
        });

        // Assert
        expect(payment).toBeDefined();
        expect(payment.id).toBeDefined();
        expect(payment.amount).toBe(10000);
        expect(payment.status).toBe('pending');
        expect(payment.payment_type).toBe('user');
        expect(payment.service_type).toBe('service');
        expect(payment.userId).toBe(testUserId);
        expect(payment.serviceId).toBe(testServiceId);
        expect(payment.stripe_session_id).toBe('cs_test_user_service');
        expect(payment.createdAt).toBeDefined();
        expect(payment.updatedAt).toBeDefined();

        // Verify foreign key relationships
        const paymentWithRelations = await prisma.payment.findUnique({
          where: { id: payment.id },
          include: {
            user: true,
            service: true,
          },
        });

        expect(paymentWithRelations?.user?.id).toBe(testUserId);
        expect(paymentWithRelations?.service?.id).toBe(testServiceId);
        expect(mockPrismaService.payment.create).toHaveBeenCalledWith({
          data: paymentData,
        });
        expect(mockPrismaService.payment.findUnique).toHaveBeenCalledWith({
          where: { id: payment.id },
          include: {
            user: true,
            service: true,
          },
        });
      });

      it('should create guest payment for package', async () => {
        // Arrange
        const paymentData = {
          amount: 25000,
          status: 'pending',
          payment_type: 'guest',
          service_type: 'package',
          progress: Status.Pending,
          packageId: testPackageId,
          guest_name: 'John Doe',
          guest_email: '<EMAIL>',
          guest_mobile: '+353123456789',
          stripe_session_id: 'cs_test_guest_package',
        };

        const mockPayment = {
          id: 'payment_guest_123',
          ...paymentData,
          userId: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const mockPaymentWithPackage = {
          ...mockPayment,
          package: { id: testPackageId, name: 'Test Package' },
        };

        mockPrismaService.payment.create.mockResolvedValue(mockPayment);
        mockPrismaService.payment.findUnique.mockResolvedValue(
          mockPaymentWithPackage,
        );

        // Act
        const payment = await prisma.payment.create({
          data: paymentData,
        });

        // Assert
        expect(payment).toBeDefined();
        expect(payment.amount).toBe(25000);
        expect(payment.payment_type).toBe('guest');
        expect(payment.service_type).toBe('package');
        expect(payment.packageId).toBe(testPackageId);
        expect(payment.guest_name).toBe('John Doe');
        expect(payment.guest_email).toBe('<EMAIL>');
        expect(payment.guest_mobile).toBe('+353123456789');
        expect(payment.userId).toBeNull();

        // Verify package relationship
        const paymentWithPackage = await prisma.payment.findUnique({
          where: { id: payment.id },
          include: { package: true },
        });

        expect(paymentWithPackage?.package?.id).toBe(testPackageId);
        expect(mockPrismaService.payment.create).toHaveBeenCalledWith({
          data: paymentData,
        });
      });

      it('should create payment for immigration service', async () => {
        // Arrange
        const paymentData = {
          amount: 50000,
          status: 'pending',
          payment_type: 'user',
          service_type: 'immigration',
          progress: Status.Pending,
          userId: testUserId,
          immigration_serviceId: testImmigrationServiceId,
          stripe_session_id: 'cs_test_immigration',
        };

        const mockPayment = {
          id: 'payment_immigration_123',
          ...paymentData,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const mockPaymentWithImmigration = {
          ...mockPayment,
          immigration_service: {
            id: testImmigrationServiceId,
            name: 'Test Immigration Service',
          },
        };

        mockPrismaService.payment.create.mockResolvedValue(mockPayment);
        mockPrismaService.payment.findUnique.mockResolvedValue(
          mockPaymentWithImmigration,
        );

        // Act
        const payment = await prisma.payment.create({
          data: paymentData,
        });

        // Assert
        expect(payment).toBeDefined();
        expect(payment.immigration_serviceId).toBe(testImmigrationServiceId);
        expect(payment.service_type).toBe('immigration');

        // Verify immigration service relationship
        const paymentWithImmigration = await prisma.payment.findUnique({
          where: { id: payment.id },
          include: { immigration_service: true },
        });

        expect(paymentWithImmigration?.immigration_service?.id).toBe(
          testImmigrationServiceId,
        );
        expect(mockPrismaService.payment.create).toHaveBeenCalledWith({
          data: paymentData,
        });
      });

      it('should create payment for training service', async () => {
        // Arrange
        const paymentData = {
          amount: 15000,
          status: 'pending',
          payment_type: 'user',
          service_type: 'training',
          progress: Status.Pending,
          userId: testUserId,
          trainingId: testTrainingId,
          stripe_session_id: 'cs_test_training',
        };

        const mockPayment = {
          id: 'payment_training_123',
          ...paymentData,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const mockPaymentWithTraining = {
          ...mockPayment,
          training: { id: testTrainingId, name: 'Test Training' },
        };

        mockPrismaService.payment.create.mockResolvedValue(mockPayment);
        mockPrismaService.payment.findUnique.mockResolvedValue(
          mockPaymentWithTraining,
        );

        // Act
        const payment = await prisma.payment.create({
          data: paymentData,
        });

        // Assert
        expect(payment).toBeDefined();
        expect(payment.trainingId).toBe(testTrainingId);
        expect(payment.service_type).toBe('training');

        // Verify training relationship
        const paymentWithTraining = await prisma.payment.findUnique({
          where: { id: payment.id },
          include: { training: true },
        });

        expect(paymentWithTraining?.training?.id).toBe(testTrainingId);
        expect(mockPrismaService.payment.create).toHaveBeenCalledWith({
          data: paymentData,
        });
      });

      it('should handle payment update operations', async () => {
        // Arrange
        const paymentId = 'payment_update_123';
        const updateData = {
          status: 'paid',
          stripe_payment_intent_id: 'pi_test_123',
          progress: Status.Completed,
        };

        const mockUpdatedPayment = {
          id: paymentId,
          amount: 10000,
          status: 'paid',
          payment_type: 'user',
          service_type: 'service',
          progress: Status.Completed,
          userId: testUserId,
          serviceId: testServiceId,
          stripe_session_id: 'cs_test_update',
          stripe_payment_intent_id: 'pi_test_123',
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        mockPrismaService.payment.update.mockResolvedValue(mockUpdatedPayment);

        // Act
        const updatedPayment = await prisma.payment.update({
          where: { id: paymentId },
          data: updateData,
        });

        // Assert
        expect(updatedPayment).toBeDefined();
        expect(updatedPayment.status).toBe('paid');
        expect(updatedPayment.stripe_payment_intent_id).toBe('pi_test_123');
        expect(updatedPayment.progress).toBe(Status.Completed);
        expect(mockPrismaService.payment.update).toHaveBeenCalledWith({
          where: { id: paymentId },
          data: updateData,
        });
      });

      it('should handle payment query operations', async () => {
        // Arrange
        const mockPayments = [
          {
            id: 'payment_1',
            amount: 10000,
            status: 'paid',
            payment_type: 'user',
            service_type: 'service',
            userId: testUserId,
            serviceId: testServiceId,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: 'payment_2',
            amount: 25000,
            status: 'pending',
            payment_type: 'guest',
            service_type: 'package',
            packageId: testPackageId,
            guest_name: 'Guest User',
            guest_email: '<EMAIL>',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ];

        mockPrismaService.payment.findMany.mockResolvedValue(mockPayments);

        // Act
        const payments = await prisma.payment.findMany({
          where: { status: 'paid' },
          include: { user: true, service: true },
        });

        // Assert
        expect(payments).toBeDefined();
        expect(Array.isArray(payments)).toBe(true);
        expect(payments.length).toBe(2);
        expect(mockPrismaService.payment.findMany).toHaveBeenCalledWith({
          where: { status: 'paid' },
          include: { user: true, service: true },
        });
      });

      it('should handle payment aggregation operations', async () => {
        // Arrange
        const mockAggregation = {
          _sum: { amount: 85000 },
          _count: 3,
          _avg: { amount: 28333.33 },
        };

        mockPrismaService.payment.aggregate.mockResolvedValue(mockAggregation);

        // Act
        const aggregation = await prisma.payment.aggregate({
          where: { status: 'paid' },
          _sum: { amount: true },
          _count: true,
          _avg: { amount: true },
        });

        // Assert
        expect(aggregation).toBeDefined();
        expect(aggregation._sum.amount).toBe(85000);
        expect(aggregation._count).toBe(3);
        expect(aggregation._avg.amount).toBe(28333.33);
        expect(mockPrismaService.payment.aggregate).toHaveBeenCalledWith({
          where: { status: 'paid' },
          _sum: { amount: true },
          _count: true,
          _avg: { amount: true },
        });
      });
    });
  });
});
