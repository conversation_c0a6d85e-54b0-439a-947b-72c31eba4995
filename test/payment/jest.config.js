/**
 * Jest Configuration for Payment Module Tests
 *
 * Specialized Jest configuration for running payment module tests
 * with optimized settings for unit, integration, and coverage testing.
 */

module.exports = {
  // Test environment
  testEnvironment: 'node',

  // Root directory for tests
  rootDir: '../../',

  // Test file patterns
  testMatch: ['<rootDir>/test/payment/**/*.spec.ts'],

  // Module file extensions
  moduleFileExtensions: ['js', 'json', 'ts', 'tsx'],

  // Transform configuration
  transform: {
    '^.+\\.(ts|tsx)$': [
      'ts-jest',
      {
        tsconfig: '<rootDir>/tsconfig.json',
        isolatedModules: true,
      },
    ],
  },

  // Module name mapping for path resolution
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
    '^test/(.*)$': '<rootDir>/test/$1',
  },

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/test/config/jest.setup.js'],

  // Coverage configuration
  collectCoverage: false,
  coverageDirectory: '<rootDir>/coverage/payment',
  coverageReporters: ['text', 'text-summary', 'html', 'lcov', 'json'],

  // Coverage collection patterns - Focus on unified payment system only
  collectCoverageFrom: [
    'src/payment/unified-payment.controller.ts',
    'src/payment/unified-payment.service.ts',
    'src/payment/dto/payment.dto.ts',
  ],

  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90,
    },
    'src/payment/unified-payment.controller.ts': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95,
    },
    'src/payment/unified-payment.service.ts': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90,
    },
  },

  // Test timeout
  testTimeout: 30000,

  // Verbose output
  verbose: true,

  // Clear mocks between tests
  clearMocks: true,

  // Restore mocks after each test
  restoreMocks: true,

  // Error handling
  errorOnDeprecated: true,

  // Test result processor (commented out - optional dependency)
  // testResultsProcessor: 'jest-sonar-reporter',

  // Global variables (deprecated - moved to transform config)
  // globals: {
  //   'ts-jest': {
  //     tsconfig: '<rootDir>/tsconfig.json',
  //     isolatedModules: true,
  //   },
  // },

  // Module paths
  modulePaths: ['<rootDir>/src', '<rootDir>/test'],

  // Ignore patterns
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/dist/',
    '<rootDir>/coverage/',
  ],

  // Watch plugins (commented out - optional dependencies)
  // watchPlugins: [
  //   'jest-watch-typeahead/filename',
  //   'jest-watch-typeahead/testname',
  // ],

  // Reporters
  reporters: [
    'default',
    // Uncomment if jest-junit is installed
    // [
    //   'jest-junit',
    //   {
    //     outputDirectory: '<rootDir>/coverage/payment',
    //     outputName: 'junit.xml',
    //     suiteName: 'Payment Module Tests',
    //   },
    // ],
  ],

  // Max workers for parallel execution
  maxWorkers: '50%',

  // Cache directory
  cacheDirectory: '<rootDir>/node_modules/.cache/jest/payment',
};
