/**
 * Admin Service Unit Tests
 *
 * Comprehensive tests for admin password reset functionality.
 * Tests success cases, error cases, and edge cases.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import {
  ConflictException,
  UnauthorizedException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { AdminService } from '../../src/admin/admin.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { MailerService } from '../../src/mailer/mailer.service';
import { LoggerService } from '../../src/utils/logger.service';
import * as bcrypt from 'bcrypt';

// Mock bcrypt
jest.mock('bcrypt');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('AdminService - Password Reset', () => {
  let service: AdminService;
  let prismaService: PrismaService;
  let jwtService: JwtService;
  let mailerService: MailerService;
  let loggerService: LoggerService;

  const mockPrismaService = {
    admin: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
  };

  const mockJwtService = {
    signAsync: jest.fn(),
    verifyAsync: jest.fn(),
  };

  const mockMailerService = {
    sendEmail: jest.fn(),
  };

  const mockLoggerService = {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  };

  const mockAdmin = {
    id: 'admin_123456789',
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'hashed_password',
    emailVerified: true,
    image: null,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    service = module.get<AdminService>(AdminService);
    prismaService = module.get<PrismaService>(PrismaService);
    jwtService = module.get<JwtService>(JwtService);
    mailerService = module.get<MailerService>(MailerService);
    loggerService = module.get<LoggerService>(LoggerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('resetPassword', () => {
    const resetPasswordDto = {
      email: '<EMAIL>',
    };

    it('should send password reset email for existing admin', async () => {
      const resetToken = 'mock_reset_token';
      
      mockPrismaService.admin.findUnique.mockResolvedValue(mockAdmin);
      mockJwtService.signAsync.mockResolvedValue(resetToken);
      mockMailerService.sendEmail.mockResolvedValue({ id: 'email_123' });

      await service.resetPassword(resetPasswordDto);

      expect(mockPrismaService.admin.findUnique).toHaveBeenCalledWith({
        where: { email: resetPasswordDto.email },
      });
      expect(mockJwtService.signAsync).toHaveBeenCalledWith(
        { email: mockAdmin.email, type: 'password_reset' },
        {
          secret: 'reset-secret',
          expiresIn: '15m',
        },
      );
      expect(mockMailerService.sendEmail).toHaveBeenCalled();
      expect(mockLoggerService.info).toHaveBeenCalledWith(
        `Password reset email sent to admin: ${mockAdmin.email}`,
      );
    });

    it('should not reveal if admin does not exist', async () => {
      mockPrismaService.admin.findUnique.mockResolvedValue(null);

      await service.resetPassword(resetPasswordDto);

      expect(mockPrismaService.admin.findUnique).toHaveBeenCalledWith({
        where: { email: resetPasswordDto.email },
      });
      expect(mockLoggerService.warn).toHaveBeenCalledWith(
        `Password reset attempted for non-existent admin: ${resetPasswordDto.email}`,
      );
      expect(mockJwtService.signAsync).not.toHaveBeenCalled();
      expect(mockMailerService.sendEmail).not.toHaveBeenCalled();
    });

    it('should handle email sending errors', async () => {
      const resetToken = 'mock_reset_token';
      const emailError = new Error('Email service unavailable');
      
      mockPrismaService.admin.findUnique.mockResolvedValue(mockAdmin);
      mockJwtService.signAsync.mockResolvedValue(resetToken);
      mockMailerService.sendEmail.mockRejectedValue(emailError);

      await expect(service.resetPassword(resetPasswordDto)).rejects.toThrow(emailError);

      expect(mockLoggerService.error).toHaveBeenCalledWith(
        `Failed to process password reset for admin: ${resetPasswordDto.email}`,
        emailError.stack,
      );
    });
  });

  describe('confirmResetPassword', () => {
    const confirmResetDto = {
      token: 'valid_reset_token',
      newPassword: 'NewSecurePassword123!',
    };

    it('should reset password with valid token', async () => {
      const tokenPayload = {
        email: mockAdmin.email,
        type: 'password_reset',
      };
      const hashedPassword = 'new_hashed_password';

      mockJwtService.verifyAsync.mockResolvedValue(tokenPayload);
      mockPrismaService.admin.findUnique.mockResolvedValue(mockAdmin);
      mockedBcrypt.hash.mockResolvedValue(hashedPassword as never);
      mockPrismaService.admin.update.mockResolvedValue({
        ...mockAdmin,
        password: hashedPassword,
      });

      await service.confirmResetPassword(confirmResetDto);

      expect(mockJwtService.verifyAsync).toHaveBeenCalledWith(
        confirmResetDto.token,
        { secret: 'reset-secret' },
      );
      expect(mockPrismaService.admin.findUnique).toHaveBeenCalledWith({
        where: { email: tokenPayload.email },
      });
      expect(mockedBcrypt.hash).toHaveBeenCalledWith(confirmResetDto.newPassword, 10);
      expect(mockPrismaService.admin.update).toHaveBeenCalledWith({
        where: { id: mockAdmin.id },
        data: { password: hashedPassword },
      });
      expect(mockLoggerService.info).toHaveBeenCalledWith(
        `Password successfully reset for admin: ${mockAdmin.email}`,
      );
    });

    it('should throw UnauthorizedException for invalid token type', async () => {
      const tokenPayload = {
        email: mockAdmin.email,
        type: 'invalid_type',
      };

      mockJwtService.verifyAsync.mockResolvedValue(tokenPayload);

      await expect(service.confirmResetPassword(confirmResetDto)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(mockLoggerService.error).toHaveBeenCalled();
    });

    it('should throw NotFoundException for non-existent admin', async () => {
      const tokenPayload = {
        email: '<EMAIL>',
        type: 'password_reset',
      };

      mockJwtService.verifyAsync.mockResolvedValue(tokenPayload);
      mockPrismaService.admin.findUnique.mockResolvedValue(null);

      await expect(service.confirmResetPassword(confirmResetDto)).rejects.toThrow(
        NotFoundException,
      );
      expect(mockLoggerService.error).toHaveBeenCalled();
    });

    it('should throw BadRequestException for expired token', async () => {
      const tokenError = new Error('jwt expired');
      mockJwtService.verifyAsync.mockRejectedValue(tokenError);

      await expect(service.confirmResetPassword(confirmResetDto)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockLoggerService.error).toHaveBeenCalledWith(
        `Failed to confirm password reset: ${tokenError.message}`,
        tokenError.stack,
      );
    });

    it('should handle database update errors', async () => {
      const tokenPayload = {
        email: mockAdmin.email,
        type: 'password_reset',
      };
      const hashedPassword = 'new_hashed_password';
      const dbError = new Error('Database connection failed');

      mockJwtService.verifyAsync.mockResolvedValue(tokenPayload);
      mockPrismaService.admin.findUnique.mockResolvedValue(mockAdmin);
      mockedBcrypt.hash.mockResolvedValue(hashedPassword as never);
      mockPrismaService.admin.update.mockRejectedValue(dbError);

      await expect(service.confirmResetPassword(confirmResetDto)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockLoggerService.error).toHaveBeenCalledWith(
        `Failed to confirm password reset: ${dbError.message}`,
        dbError.stack,
      );
    });
  });
});
