/**
 * Admin Controller Unit Tests
 *
 * Tests for admin password reset controller endpoints.
 * Tests success cases, error handling, and API responses.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { AdminController } from '../../src/admin/admin.controller';
import { AdminService } from '../../src/admin/admin.service';
import { LoggerService } from '../../src/utils/logger.service';
import {
  ResetAdminPasswordDto,
  ConfirmResetAdminPasswordDto,
} from '../../src/admin/dto/admin.dto';

describe('AdminController - Password Reset', () => {
  let controller: AdminController;
  let adminService: AdminService;
  let loggerService: LoggerService;

  const mockAdminService = {
    resetPassword: jest.fn(),
    confirmResetPassword: jest.fn(),
  };

  const mockLoggerService = {
    error: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminController],
      providers: [
        {
          provide: AdminService,
          useValue: mockAdminService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    controller = module.get<AdminController>(AdminController);
    adminService = module.get<AdminService>(AdminService);
    loggerService = module.get<LoggerService>(LoggerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('resetPassword', () => {
    const resetPasswordDto: ResetAdminPasswordDto = {
      email: '<EMAIL>',
    };

    it('should send password reset email successfully', async () => {
      mockAdminService.resetPassword.mockResolvedValue(undefined);

      const result = await controller.resetPassword(resetPasswordDto);

      expect(result).toEqual({
        success: true,
        message:
          'If an admin account with this email exists, a password reset link has been sent.',
      });
      expect(mockAdminService.resetPassword).toHaveBeenCalledWith(resetPasswordDto);
    });

    it('should return success message even when service throws error (security)', async () => {
      const serviceError = new Error('Admin not found');
      mockAdminService.resetPassword.mockRejectedValue(serviceError);

      const result = await controller.resetPassword(resetPasswordDto);

      expect(result).toEqual({
        success: true,
        message:
          'If an admin account with this email exists, a password reset link has been sent.',
      });
      expect(mockLoggerService.error).toHaveBeenCalledWith(
        `Password reset failed: ${serviceError.message}`,
        serviceError.stack,
      );
    });

    it('should handle email service errors gracefully', async () => {
      const emailError = new Error('Email service unavailable');
      mockAdminService.resetPassword.mockRejectedValue(emailError);

      const result = await controller.resetPassword(resetPasswordDto);

      expect(result).toEqual({
        success: true,
        message:
          'If an admin account with this email exists, a password reset link has been sent.',
      });
      expect(mockLoggerService.error).toHaveBeenCalledWith(
        `Password reset failed: ${emailError.message}`,
        emailError.stack,
      );
    });
  });

  describe('confirmResetPassword', () => {
    const confirmResetDto: ConfirmResetAdminPasswordDto = {
      token: 'valid_reset_token',
      newPassword: 'NewSecurePassword123!',
    };

    it('should confirm password reset successfully', async () => {
      mockAdminService.confirmResetPassword.mockResolvedValue(undefined);

      const result = await controller.confirmResetPassword(confirmResetDto);

      expect(result).toEqual({
        success: true,
        message: 'Password reset successfully',
      });
      expect(mockAdminService.confirmResetPassword).toHaveBeenCalledWith(confirmResetDto);
    });

    it('should throw HttpException for invalid token', async () => {
      const tokenError = new Error('Invalid or expired token');
      tokenError['status'] = HttpStatus.UNAUTHORIZED;
      mockAdminService.confirmResetPassword.mockRejectedValue(tokenError);

      await expect(controller.confirmResetPassword(confirmResetDto)).rejects.toThrow(
        HttpException,
      );

      expect(mockLoggerService.error).toHaveBeenCalledWith(
        `Password reset confirmation failed: ${tokenError.message}`,
        tokenError.stack,
      );
    });

    it('should throw HttpException for expired token', async () => {
      const expiredError = new Error('jwt expired');
      mockAdminService.confirmResetPassword.mockRejectedValue(expiredError);

      await expect(controller.confirmResetPassword(confirmResetDto)).rejects.toThrow(
        HttpException,
      );

      expect(mockLoggerService.error).toHaveBeenCalledWith(
        `Password reset confirmation failed: ${expiredError.message}`,
        expiredError.stack,
      );
    });

    it('should handle service errors with proper HTTP status', async () => {
      const serviceError = new Error('Database connection failed');
      mockAdminService.confirmResetPassword.mockRejectedValue(serviceError);

      try {
        await controller.confirmResetPassword(confirmResetDto);
      } catch (error) {
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toBe('Database connection failed');
        expect(error.getStatus()).toBe(HttpStatus.UNAUTHORIZED);
      }

      expect(mockLoggerService.error).toHaveBeenCalledWith(
        `Password reset confirmation failed: ${serviceError.message}`,
        serviceError.stack,
      );
    });

    it('should use default error message when none provided', async () => {
      const serviceError = new Error();
      mockAdminService.confirmResetPassword.mockRejectedValue(serviceError);

      try {
        await controller.confirmResetPassword(confirmResetDto);
      } catch (error) {
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toBe('Failed to reset password');
        expect(error.getStatus()).toBe(HttpStatus.UNAUTHORIZED);
      }
    });
  });

  describe('API endpoint validation', () => {
    it('should validate email format in resetPassword', async () => {
      const invalidDto = {
        email: 'invalid-email',
      };

      // This would be handled by class-validator in real scenario
      // Here we just test that the service is called with the DTO
      mockAdminService.resetPassword.mockResolvedValue(undefined);

      await controller.resetPassword(invalidDto as ResetAdminPasswordDto);

      expect(mockAdminService.resetPassword).toHaveBeenCalledWith(invalidDto);
    });

    it('should validate password length in confirmResetPassword', async () => {
      const invalidDto = {
        token: 'valid_token',
        newPassword: '123', // Too short
      };

      // This would be handled by class-validator in real scenario
      mockAdminService.confirmResetPassword.mockResolvedValue(undefined);

      await controller.confirmResetPassword(invalidDto as ConfirmResetAdminPasswordDto);

      expect(mockAdminService.confirmResetPassword).toHaveBeenCalledWith(invalidDto);
    });
  });

  describe('Security considerations', () => {
    it('should not reveal admin existence in reset password response', async () => {
      const nonExistentDto = {
        email: '<EMAIL>',
      };

      mockAdminService.resetPassword.mockRejectedValue(new Error('Admin not found'));

      const result = await controller.resetPassword(nonExistentDto);

      expect(result.message).toBe(
        'If an admin account with this email exists, a password reset link has been sent.',
      );
      expect(result.success).toBe(true);
    });

    it('should log security events for monitoring', async () => {
      const resetPasswordDto = {
        email: '<EMAIL>',
      };

      mockAdminService.resetPassword.mockResolvedValue(undefined);

      await controller.resetPassword(resetPasswordDto);

      // Verify that no sensitive information is logged
      expect(mockLoggerService.error).not.toHaveBeenCalled();
    });
  });
});
