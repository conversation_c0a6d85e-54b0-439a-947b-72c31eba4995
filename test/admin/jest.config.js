/**
 * Jest Configuration for Admin Tests
 *
 * Specific configuration for admin management system tests.
 * Includes proper setup for database mocking and environment variables.
 */

module.exports = {
  displayName: 'Admin Management Tests',
  testMatch: ['<rootDir>/test/admin/**/*.spec.ts'],
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../..',
  testEnvironment: 'node',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/admin/**/*.(t|j)s',
    'src/guards/jwt.admin.guard.(t|j)s',
    '!src/admin/**/*.spec.ts',
    '!src/admin/**/*.interface.ts',
    '!src/admin/**/*.dto.ts',
  ],
  coverageDirectory: '<rootDir>/coverage/admin',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/test/config/jest.setup.js'],
  testTimeout: 30000,
  verbose: true,
  detectOpenHandles: true,
  forceExit: true,
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,
  moduleNameMapping: {
    '^src/(.*)$': '<rootDir>/src/$1',
    '^test/(.*)$': '<rootDir>/test/$1',
  },
  globals: {
    'ts-jest': {
      tsconfig: '<rootDir>/tsconfig.json',
    },
  },
  testEnvironmentOptions: {
    NODE_ENV: 'test',
  },
};
