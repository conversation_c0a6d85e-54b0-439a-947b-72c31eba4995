import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { DocumentProcessingService } from '../../src/application/services/document-processing.service';
import { PrismaService } from '../../src/utils/prisma.service';

// Mock external dependencies
jest.mock('pdf-parse');
jest.mock('mammoth');

describe('DocumentProcessingService', () => {
  let service: DocumentProcessingService;
  let prismaService: jest.Mocked<PrismaService>;

  const mockPdfFile: Express.Multer.File = {
    fieldname: 'file',
    originalname: 'test-document.pdf',
    encoding: '7bit',
    mimetype: 'application/pdf',
    size: 1024 * 1024,
    buffer: Buffer.from('mock pdf content'),
    destination: '',
    filename: '',
    path: '',
    stream: null,
  };

  const mockWordFile: Express.Multer.File = {
    fieldname: 'file',
    originalname: 'test-document.docx',
    encoding: '7bit',
    mimetype:
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    size: 1024 * 1024,
    buffer: Buffer.from('mock docx content'),
    destination: '',
    filename: '',
    path: '',
    stream: null,
  };

  const mockTextFile: Express.Multer.File = {
    fieldname: 'file',
    originalname: 'test-document.txt',
    encoding: '7bit',
    mimetype: 'text/plain',
    size: 1024,
    buffer: Buffer.from('This is a test document with sample text content.'),
    destination: '',
    filename: '',
    path: '',
    stream: null,
  };

  beforeEach(async () => {
    const mockPrismaService = {
      document_vault: {
        update: jest.fn().mockResolvedValue({}),
        findUnique: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentProcessingService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<DocumentProcessingService>(DocumentProcessingService);
    prismaService = module.get(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('extractTextFromDocument', () => {
    it('should extract text from PDF files', async () => {
      const mockPdfParse = require('pdf-parse');
      mockPdfParse.mockResolvedValue({ text: 'Extracted PDF text content' });

      const result = await service.extractTextFromDocument(mockPdfFile);

      expect(result).toBe('Extracted PDF text content');
      expect(mockPdfParse).toHaveBeenCalledWith(mockPdfFile.buffer);
    });

    it('should extract text from Word documents', async () => {
      const mockMammoth = require('mammoth');
      mockMammoth.extractRawText.mockResolvedValue({
        value: 'Extracted Word text content',
      });

      const result = await service.extractTextFromDocument(mockWordFile);

      expect(result).toBe('Extracted Word text content');
      expect(mockMammoth.extractRawText).toHaveBeenCalledWith({
        buffer: mockWordFile.buffer,
      });
    });

    it('should extract text from plain text files', async () => {
      const result = await service.extractTextFromDocument(mockTextFile);

      expect(result).toBe('This is a test document with sample text content.');
    });

    it('should handle image files (OCR placeholder)', async () => {
      const mockImageFile = {
        ...mockTextFile,
        originalname: 'test-image.jpg',
        mimetype: 'image/jpeg',
      };

      const result = await service.extractTextFromDocument(mockImageFile);

      expect(result).toBe(''); // Placeholder implementation returns empty string
    });

    it('should throw BadRequestException for unsupported file types', async () => {
      const unsupportedFile = {
        ...mockTextFile,
        originalname: 'test-file.exe',
        mimetype: 'application/exe',
      };

      await expect(
        service.extractTextFromDocument(unsupportedFile),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle PDF parsing errors', async () => {
      const mockPdfParse = require('pdf-parse');
      mockPdfParse.mockRejectedValue(new Error('PDF parsing failed'));

      await expect(
        service.extractTextFromDocument(mockPdfFile),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle Word document parsing errors', async () => {
      const mockMammoth = require('mammoth');
      mockMammoth.extractRawText.mockRejectedValue(
        new Error('Word parsing failed'),
      );

      await expect(
        service.extractTextFromDocument(mockWordFile),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('processDocumentForSearch', () => {
    beforeEach(() => {
      const mockPdfParse = require('pdf-parse');
      mockPdfParse.mockResolvedValue({
        text: 'Sample document content for indexing',
      });
    });

    it('should process document and update search metadata', async () => {
      await service.processDocumentForSearch('doc-123', mockPdfFile);

      expect(prismaService.document_vault.update).toHaveBeenCalledWith({
        where: { id: 'doc-123' },
        data: {
          metadata: {
            extracted_text: 'Sample document content for indexing',
            text_length: 'Sample document content for indexing'.length,
            processed_at: expect.any(String),
          },
        },
      });
    });

    it('should handle processing errors gracefully', async () => {
      const mockPdfParse = require('pdf-parse');
      mockPdfParse.mockRejectedValue(new Error('Processing failed'));

      await expect(
        service.processDocumentForSearch('doc-123', mockPdfFile),
      ).rejects.toThrow();
    });
  });

  describe('analyzeDocumentContent', () => {
    beforeEach(() => {
      const mockPdfParse = require('pdf-parse');
      mockPdfParse.mockResolvedValue({
        text: 'This is a sample document with multiple sentences. It contains various words and phrases for analysis.',
      });
    });

    it('should analyze document content and return metadata', async () => {
      const result = await service.analyzeDocumentContent(mockPdfFile);

      expect(result).toEqual({
        word_count: expect.any(Number),
        character_count: expect.any(Number),
        language: expect.any(String),
        contains_personal_info: expect.any(Boolean),
        document_structure: expect.any(Object),
        keywords: expect.any(Array),
        readability_score: expect.any(Number),
      });

      expect(result.word_count).toBeGreaterThan(0);
      expect(result.character_count).toBeGreaterThan(0);
      expect(result.keywords).toBeInstanceOf(Array);
    });

    it('should detect English language', async () => {
      const mockPdfParse = require('pdf-parse');
      mockPdfParse.mockResolvedValue({
        text: 'The quick brown fox jumps over the lazy dog and runs to the forest.',
      });

      const result = await service.analyzeDocumentContent(mockPdfFile);

      expect(result.language).toBe('en');
    });

    it('should detect personal information patterns', async () => {
      const mockPdfParse = require('pdf-parse');
      mockPdfParse.mockResolvedValue({
        text: 'Contact John <NAME_EMAIL> or call 555-123-4567.',
      });

      const result = await service.analyzeDocumentContent(mockPdfFile);

      expect(result.contains_personal_info).toBe(true);
    });

    it('should analyze document structure', async () => {
      const mockPdfParse = require('pdf-parse');
      mockPdfParse.mockResolvedValue({
        text: 'TITLE\n\nThis is paragraph one.\n\nThis is paragraph two.\n\n• Bullet point one\n• Bullet point two',
      });

      const result = await service.analyzeDocumentContent(mockPdfFile);

      expect(result.document_structure).toEqual({
        line_count: expect.any(Number),
        paragraph_count: expect.any(Number),
        average_paragraph_length: expect.any(Number),
        has_headers: expect.any(Boolean),
        has_bullet_points: expect.any(Boolean),
        has_numbers: expect.any(Boolean),
      });
    });

    it('should extract keywords', async () => {
      const mockPdfParse = require('pdf-parse');
      mockPdfParse.mockResolvedValue({
        text: 'document analysis processing text extraction content management system workflow',
      });

      const result = await service.analyzeDocumentContent(mockPdfFile);

      expect(result.keywords).toBeInstanceOf(Array);
      expect(result.keywords.length).toBeGreaterThan(0);
      expect(result.keywords).toContain('document');
    });

    it('should calculate readability score', async () => {
      const mockPdfParse = require('pdf-parse');
      mockPdfParse.mockResolvedValue({
        text: 'This is a simple sentence. This is another simple sentence. Easy to read.',
      });

      const result = await service.analyzeDocumentContent(mockPdfFile);

      expect(result.readability_score).toBeGreaterThanOrEqual(0);
      expect(result.readability_score).toBeLessThanOrEqual(100);
    });
  });

  describe('private helper methods', () => {
    it('should count words correctly', () => {
      // Access private method through service instance
      const text = 'This is a test document with seven words.';
      const wordCount = (service as any).countWords(text);

      expect(wordCount).toBe(9);
    });

    it('should detect language correctly', () => {
      const englishText = 'The quick brown fox jumps over the lazy dog.';
      const language = (service as any).detectLanguage(englishText);

      expect(language).toBe('en');
    });

    it('should detect personal information', () => {
      const textWithEmail =
        'Contact <NAME_EMAIL> for more information.';
      const hasPersonalInfo = (service as any).detectPersonalInfo(
        textWithEmail,
      );

      expect(hasPersonalInfo).toBe(true);
    });

    it('should count syllables in words', () => {
      const syllableCount1 = (service as any).countSyllables('hello');
      const syllableCount2 = (service as any).countSyllables('beautiful');
      const syllableCount3 = (service as any).countSyllables('a');

      expect(syllableCount1).toBe(2);
      expect(syllableCount2).toBe(3);
      expect(syllableCount3).toBe(1);
    });
  });
});
