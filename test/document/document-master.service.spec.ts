/**
 * Document Master Service Unit Tests
 *
 * Comprehensive test suite for DocumentMasterService covering all CRUD operations,
 * validation logic, error handling, and business rules.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { DocumentMasterService } from '../../src/document/document-master.service';
import { PrismaService } from '../../src/utils/prisma.service';
import {
  CreateDocumentMasterDto,
  UpdateDocumentMasterDto,
  DocumentMasterFiltersDto,
} from '../../src/document/dto/document-master.dto';

describe('DocumentMasterService', () => {
  let service: DocumentMasterService;
  let prismaService: PrismaService;

  // Mock data
  const mockDocumentMaster = {
    id: 'clx1234567890abcdef',
    name: 'Passport Copy',
    description: 'A clear copy of the passport bio-data page',
    category: 'Identity Documents',
    // Task 4: Removed document_type field - following non-destructive patterns
    // document_type: 'Government ID',
    instructions:
      'Please upload a clear, colored scan of your passport bio-data page.',
    created_by: 'admin123',
    updated_by: null,
    created_at: new Date('2025-01-06T10:30:00.000Z'),
    updated_at: new Date('2025-01-06T10:30:00.000Z'),
  };

  const mockCreateDto: CreateDocumentMasterDto = {
    name: 'Passport Copy',
    description: 'A clear copy of the passport bio-data page',
    category: 'Identity Documents',
    // Task 4: Removed document_type field - following non-destructive patterns
    // document_type: 'Government ID',
    instructions:
      'Please upload a clear, colored scan of your passport bio-data page.',
  };

  const mockUpdateDto: UpdateDocumentMasterDto = {
    name: 'Updated Passport Copy',
    description: 'Updated description',
  };

  const mockFilters: DocumentMasterFiltersDto = {
    page: 1,
    limit: 10,
    category: 'Identity Documents',
  };

  // Mock PrismaService
  const mockPrismaService = {
    document_master: {
      create: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentMasterService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<DocumentMasterService>(DocumentMasterService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a document master successfully', async () => {
      mockPrismaService.document_master.findFirst.mockResolvedValue(null);
      mockPrismaService.document_master.create.mockResolvedValue(
        mockDocumentMaster,
      );

      const result = await service.create(mockCreateDto, 'admin123');

      expect(mockPrismaService.document_master.findFirst).toHaveBeenCalledWith({
        where: {
          name: {
            equals: mockCreateDto.name,
            mode: 'insensitive',
          },
        },
      });
      expect(mockPrismaService.document_master.create).toHaveBeenCalledWith({
        data: {
          ...mockCreateDto,
          created_by: 'admin123',
        },
      });
      expect(result).toEqual(mockDocumentMaster);
    });

    it('should throw ConflictException if document master with same name exists', async () => {
      mockPrismaService.document_master.findFirst.mockResolvedValue(
        mockDocumentMaster,
      );

      await expect(service.create(mockCreateDto, 'admin123')).rejects.toThrow(
        ConflictException,
      );
      expect(mockPrismaService.document_master.create).not.toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      mockPrismaService.document_master.findFirst.mockResolvedValue(null);
      mockPrismaService.document_master.create.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(service.create(mockCreateDto, 'admin123')).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('findAll', () => {
    it('should return paginated document masters', async () => {
      const mockCount = 25;
      const mockDocumentMasters = [mockDocumentMaster];

      mockPrismaService.document_master.count.mockResolvedValue(mockCount);
      mockPrismaService.document_master.findMany.mockResolvedValue(
        mockDocumentMasters,
      );

      const result = await service.findAll(mockFilters);

      expect(mockPrismaService.document_master.count).toHaveBeenCalled();
      expect(mockPrismaService.document_master.findMany).toHaveBeenCalledWith({
        where: {
          category: {
            contains: mockFilters.category,
            mode: 'insensitive',
          },
        },
        skip: 0,
        take: 10,
        orderBy: [{ created_at: 'desc' }, { name: 'asc' }],
      });
      expect(result).toEqual({
        document_masters: mockDocumentMasters,
        total: mockCount,
        page: 1,
        limit: 10,
        totalPages: 3,
      });
    });

    it('should handle search filters', async () => {
      const searchFilters = { ...mockFilters, search: 'passport' };
      mockPrismaService.document_master.count.mockResolvedValue(1);
      mockPrismaService.document_master.findMany.mockResolvedValue([
        mockDocumentMaster,
      ]);

      await service.findAll(searchFilters);

      expect(mockPrismaService.document_master.findMany).toHaveBeenCalledWith({
        where: {
          category: {
            contains: searchFilters.category,
            mode: 'insensitive',
          },
          OR: [
            {
              name: {
                contains: 'passport',
                mode: 'insensitive',
              },
            },
            {
              description: {
                contains: 'passport',
                mode: 'insensitive',
              },
            },
          ],
        },
        skip: 0,
        take: 10,
        orderBy: [{ created_at: 'desc' }, { name: 'asc' }],
      });
    });

    it('should cap limit at 100', async () => {
      const filtersWithHighLimit = { ...mockFilters, limit: 200 };
      mockPrismaService.document_master.count.mockResolvedValue(1);
      mockPrismaService.document_master.findMany.mockResolvedValue([
        mockDocumentMaster,
      ]);

      await service.findAll(filtersWithHighLimit);

      expect(mockPrismaService.document_master.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 100,
        }),
      );
    });
  });

  describe('findOne', () => {
    it('should return a document master by ID', async () => {
      mockPrismaService.document_master.findUnique.mockResolvedValue(
        mockDocumentMaster,
      );

      const result = await service.findOne('clx1234567890abcdef');

      expect(mockPrismaService.document_master.findUnique).toHaveBeenCalledWith(
        {
          where: { id: 'clx1234567890abcdef' },
        },
      );
      expect(result).toEqual(mockDocumentMaster);
    });

    it('should throw NotFoundException if document master not found', async () => {
      mockPrismaService.document_master.findUnique.mockResolvedValue(null);

      await expect(service.findOne('nonexistent')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update a document master successfully', async () => {
      const updatedDocumentMaster = { ...mockDocumentMaster, ...mockUpdateDto };
      mockPrismaService.document_master.findUnique.mockResolvedValue(
        mockDocumentMaster,
      );
      mockPrismaService.document_master.findFirst.mockResolvedValue(null);
      mockPrismaService.document_master.update.mockResolvedValue(
        updatedDocumentMaster,
      );

      const result = await service.update(
        'clx1234567890abcdef',
        mockUpdateDto,
        'admin123',
      );

      expect(mockPrismaService.document_master.update).toHaveBeenCalledWith({
        where: { id: 'clx1234567890abcdef' },
        data: {
          ...mockUpdateDto,
          updated_by: 'admin123',
        },
      });
      expect(result).toEqual(updatedDocumentMaster);
    });

    it('should throw NotFoundException if document master not found', async () => {
      mockPrismaService.document_master.findUnique.mockResolvedValue(null);

      await expect(
        service.update('nonexistent', mockUpdateDto, 'admin123'),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw ConflictException if name conflicts with existing document master', async () => {
      const conflictingDocumentMaster = {
        ...mockDocumentMaster,
        id: 'different-id',
      };
      mockPrismaService.document_master.findUnique.mockResolvedValue(
        mockDocumentMaster,
      );
      mockPrismaService.document_master.findFirst.mockResolvedValue(
        conflictingDocumentMaster,
      );

      await expect(
        service.update(
          'clx1234567890abcdef',
          { name: 'Conflicting Name' },
          'admin123',
        ),
      ).rejects.toThrow(ConflictException);
    });
  });

  describe('remove', () => {
    it('should delete a document master successfully when not in use', async () => {
      mockPrismaService.document_master.findUnique.mockResolvedValue(
        mockDocumentMaster,
      );
      mockPrismaService.document_master.delete.mockResolvedValue(
        mockDocumentMaster,
      );

      // Mock checkUsage to return not in use
      jest.spyOn(service, 'checkUsage').mockResolvedValue({
        inUse: false,
        usageCount: 0,
      });

      await service.remove('clx1234567890abcdef');

      expect(mockPrismaService.document_master.delete).toHaveBeenCalledWith({
        where: { id: 'clx1234567890abcdef' },
      });
    });

    it('should throw NotFoundException if document master not found', async () => {
      mockPrismaService.document_master.findUnique.mockResolvedValue(null);

      await expect(service.remove('nonexistent')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw ConflictException if document master is in use', async () => {
      mockPrismaService.document_master.findUnique.mockResolvedValue(
        mockDocumentMaster,
      );

      // Mock checkUsage to return in use
      jest.spyOn(service, 'checkUsage').mockResolvedValue({
        inUse: true,
        usageCount: 5,
        usageDetails: ['Application #12345'],
      });

      await expect(service.remove('clx1234567890abcdef')).rejects.toThrow(
        ConflictException,
      );
      expect(mockPrismaService.document_master.delete).not.toHaveBeenCalled();
    });
  });

  describe('checkUsage', () => {
    it('should return usage information', async () => {
      const result = await service.checkUsage('clx1234567890abcdef');

      expect(result).toEqual({
        inUse: false,
        usageCount: 0,
        usageDetails: undefined,
      });
    });
  });

  describe('getCategories', () => {
    it('should return unique categories', async () => {
      const mockCategories = [
        { category: 'Identity Documents' },
        { category: 'Educational Documents' },
      ];
      mockPrismaService.document_master.findMany.mockResolvedValue(
        mockCategories,
      );

      const result = await service.getCategories();

      expect(mockPrismaService.document_master.findMany).toHaveBeenCalledWith({
        select: { category: true },
        distinct: ['category'],
        orderBy: { category: 'asc' },
      });
      expect(result).toEqual(['Identity Documents', 'Educational Documents']);
    });
  });

  // Task 4: Removed getDocumentTypes test - following non-destructive patterns
  // describe('getDocumentTypes', () => {
  //   it('should return unique document types', async () => {
  //     const mockTypes = [
  //       { document_type: 'Government ID' },
  //       { document_type: 'Certificate' },
  //     ];
  //     mockPrismaService.document_master.findMany.mockResolvedValue(mockTypes);

  //     const result = await service.getDocumentTypes();

  //     expect(mockPrismaService.document_master.findMany).toHaveBeenCalledWith({
  //       select: { document_type: true },
  //       distinct: ['document_type'],
  //       orderBy: { document_type: 'asc' },
  //     });
  //     expect(result).toEqual(['Government ID', 'Certificate']);
  //   });
  // });
});
