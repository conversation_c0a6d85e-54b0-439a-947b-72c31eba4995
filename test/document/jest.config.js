module.exports = {
  displayName: 'Document Management Tests',
  preset: 'ts-jest',
  testEnvironment: 'node',
  rootDir: '../../',
  testMatch: ['<rootDir>/test/document/**/*.spec.ts'],
  setupFilesAfterEnv: ['<rootDir>/test/config/jest.setup.js'],
  collectCoverageFrom: [
    'src/application/services/document-*.service.ts',
    'src/application/controllers/document.controller.ts',
    'src/application/modules/document.module.ts',
  ],
  coverageDirectory: '<rootDir>/coverage/document',
  coverageReporters: ['text', 'lcov', 'html'],
  testTimeout: 30000,
  verbose: true,
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
};
