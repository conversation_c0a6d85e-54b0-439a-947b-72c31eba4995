import { Test, TestingModule } from '@nestjs/testing';
import { DocumentVaultService } from '../../src/application/services/document-vault.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { MediaService } from '../../src/media/media.service';
import { DocumentType } from '@prisma/client';

describe('DocumentVaultService', () => {
  let service: DocumentVaultService;
  let prismaService: PrismaService;
  let mediaService: MediaService;

  const mockPrismaService = {
    document_vault: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
      count: jest.fn(),
      delete: jest.fn(),
    },
    application_document: {
      create: jest.fn(),
      findMany: jest.fn(),
    },
    application: {
      findUnique: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
    admin: {
      findUnique: jest.fn(),
    },
  };

  const mockMediaService = {
    uploadFile: jest.fn().mockResolvedValue({
      status: 'OK',
      url: 'documents/test-file.pdf',
    }),
    deleteFile: jest.fn().mockResolvedValue({ status: 'OK' }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentVaultService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: MediaService,
          useValue: mockMediaService,
        },
      ],
    }).compile();

    service = module.get<DocumentVaultService>(DocumentVaultService);
    prismaService = module.get<PrismaService>(PrismaService);
    mediaService = module.get<MediaService>(MediaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('uploadDocument', () => {
    const mockFile = {
      fieldname: 'file',
      originalname: 'test-document.pdf',
      encoding: '7bit',
      mimetype: 'application/pdf',
      size: 1024,
      buffer: Buffer.from('test content'),
      destination: '',
      filename: '',
      path: '',
      stream: null,
    } as Express.Multer.File;

    const mockUploadData = {
      document_name: 'Test Document',
      document_type: DocumentType.Passport,
    };

    it('should upload document successfully', async () => {
      const mockCreatedDocument = {
        id: 'doc123',
        document_name: 'Test Document',
        original_filename: 'test-document.pdf',
        document_type: DocumentType.Passport,
        file_path: 'documents/test-file.pdf',
        file_size: 1024,
        user_id: 'user123',
        uploaded_by: 'user123',
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockPrismaService.document_vault.create.mockResolvedValue(
        mockCreatedDocument,
      );
      mockPrismaService.document_vault.findFirst.mockResolvedValue(null); // No duplicate

      const result = await service.uploadDocument(
        mockFile,
        mockUploadData,
        'user123',
      );

      expect(mediaService.uploadFile).toHaveBeenCalledWith(
        mockFile,
        'documents',
      );
      expect(prismaService.document_vault.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          document_name: 'Test Document',
          original_filename: 'test-document.pdf',
          document_type: DocumentType.Passport,
          file_path: 'documents/test-file.pdf',
          file_size: 1024,
          uploaded_by: 'user123',
        }),
      });
      expect(result).toEqual(mockCreatedDocument);
    });

    it('should handle upload failure', async () => {
      mockMediaService.uploadFile.mockResolvedValue({
        status: 'ERROR',
        message: 'Upload failed',
      });
      mockPrismaService.document_vault.findFirst.mockResolvedValue(null); // No duplicate

      await expect(
        service.uploadDocument(mockFile, mockUploadData, 'user123'),
      ).rejects.toThrow('Failed to upload file to storage');
    });
  });

  describe('getUserVault', () => {
    it('should return user vault documents', async () => {
      const mockDocuments = [
        { id: 'doc1', document_name: 'Document 1' },
        { id: 'doc2', document_name: 'Document 2' },
      ];

      mockPrismaService.document_vault.findMany.mockResolvedValue(
        mockDocuments,
      );

      const result = await service.getUserVault('user123');

      expect(prismaService.document_vault.findMany).toHaveBeenCalledWith({
        where: {
          user_id: 'user123',
        },
        orderBy: { created_at: 'desc' },
        skip: 0,
        take: 20,
      });
      expect(result).toEqual(mockDocuments);
    });

    it('should apply filters correctly', async () => {
      const mockDocuments = [{ id: 'doc1', document_name: 'Document 1' }];

      mockPrismaService.document_vault.findMany.mockResolvedValue(
        mockDocuments,
      );

      const result = await service.getUserVault('user123', {
        document_type: DocumentType.Passport,
        page: 2,
        limit: 5,
      });

      expect(prismaService.document_vault.findMany).toHaveBeenCalledWith({
        where: {
          user_id: 'user123',
          document_type: DocumentType.Passport,
        },
        orderBy: { created_at: 'desc' },
        skip: 5,
        take: 5,
      });
      expect(result).toEqual(mockDocuments);
    });
  });

  describe('checkExpiringDocuments', () => {
    it('should return expiring documents', async () => {
      const mockExpiringDocuments = [
        {
          id: 'doc1',
          document_name: 'Expiring Document 1',
          expiry_date: new Date(),
        },
        {
          id: 'doc2',
          document_name: 'Expiring Document 2',
          expiry_date: new Date(),
        },
      ];

      mockPrismaService.document_vault.findMany.mockResolvedValue(
        mockExpiringDocuments,
      );

      const result = await service.checkExpiringDocuments(30);

      expect(prismaService.document_vault.findMany).toHaveBeenCalledWith({
        where: {
          expiry_date: {
            lte: expect.any(Date),
            gte: expect.any(Date),
          },
          expiry_reminder_sent: false,
        },
        orderBy: { expiry_date: 'asc' },
      });

      expect(result).toEqual(mockExpiringDocuments);
    });
  });

  describe('linkDocumentToApplication', () => {
    it('should link document to application successfully', async () => {
      const mockDocument = {
        id: 'doc123',
        document_name: 'Test Document',
        original_filename: 'Test Document',
      };
      const mockApplication = { id: 'app123' };

      mockPrismaService.document_vault.findUnique.mockResolvedValue(
        mockDocument,
      );
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.application_document.create.mockResolvedValue({});

      await service.linkDocumentToApplication('doc123', 'app123');

      expect(prismaService.document_vault.findUnique).toHaveBeenCalledWith({
        where: { id: 'doc123' },
      });
      expect(prismaService.application.findUnique).toHaveBeenCalledWith({
        where: { id: 'app123' },
      });
      expect(prismaService.application_document.create).toHaveBeenCalledWith({
        data: {
          application_id: 'app123',
          document_vault_id: 'doc123',
          request_reason: 'Test Document',
          stage_order: 1,
          file_name: 'Test Document',
          file_url: undefined,
          required: true,
          status: 'pending',
          upload_date: expect.any(Date),
        },
      });
    });

    it('should throw error if document not found', async () => {
      mockPrismaService.document_vault.findUnique.mockResolvedValue(null);

      await expect(
        service.linkDocumentToApplication('nonexistent', 'app123'),
      ).rejects.toThrow('Document not found');
    });

    it('should throw error if application not found', async () => {
      const mockDocument = {
        id: 'doc123',
        document_name: 'Test Document',
      };
      mockPrismaService.document_vault.findUnique.mockResolvedValue(
        mockDocument,
      );
      mockPrismaService.application.findUnique.mockResolvedValue(null);

      await expect(
        service.linkDocumentToApplication('doc123', 'nonexistent'),
      ).rejects.toThrow('Application not found');
    });
  });

  // Note: Removed calculateFileHash and duplicate detection tests as these features were removed for schema simplification
});
