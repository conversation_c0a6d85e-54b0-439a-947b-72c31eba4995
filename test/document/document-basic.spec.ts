import { Test, TestingModule } from '@nestjs/testing';
import { DocumentVaultService } from '../../src/application/services/document-vault.service';
import { DocumentProcessingService } from '../../src/application/services/document-processing.service';
import { DocumentClassificationService } from '../../src/application/services/document-classification.service';
// import { DocumentVersionService } from '../../src/application/services/document-version.service'; // REMOVED: Service disabled
// import { DocumentSearchService } from '../../src/application/services/document-search.service'; // REMOVED (deprecated)
import { PrismaService } from '../../src/utils/prisma.service';
import { MediaService } from '../../src/media/media.service';

describe('Document Management System - Basic Tests', () => {
  let documentVaultService: DocumentVaultService;
  let documentProcessingService: DocumentProcessingService;
  let documentClassificationService: DocumentClassificationService;
  // let documentVersionService: DocumentVersionService; // REMOVED: Service disabled
  // let documentSearchService: DocumentSearchService; // REMOVED (deprecated)

  const mockPrismaService = {
    document_vault: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
      count: jest.fn(),
    },
    application: {
      findUnique: jest.fn(),
    },
    application_document: {
      create: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
    admin: {
      findUnique: jest.fn(),
    },
  };

  const mockMediaService = {
    uploadFile: jest.fn().mockResolvedValue({
      status: 'OK',
      url: 'documents/test-file.pdf',
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentVaultService,
        DocumentProcessingService,
        DocumentClassificationService,
        // DocumentVersionService, // REMOVED: Service disabled
        // DocumentSearchService, // REMOVED (deprecated)
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: MediaService,
          useValue: mockMediaService,
        },
      ],
    }).compile();

    documentVaultService =
      module.get<DocumentVaultService>(DocumentVaultService);
    documentProcessingService = module.get<DocumentProcessingService>(
      DocumentProcessingService,
    );
    documentClassificationService = module.get<DocumentClassificationService>(
      DocumentClassificationService,
    );
    // documentVersionService = module.get<DocumentVersionService>(
    //   DocumentVersionService,
    // ); // REMOVED: Service disabled
    // documentSearchService = module.get<DocumentSearchService>(
    //   DocumentSearchService,
    // ); // REMOVED (deprecated)
  });

  describe('Service Instantiation', () => {
    it('should create DocumentVaultService', () => {
      expect(documentVaultService).toBeDefined();
    });

    it('should create DocumentProcessingService', () => {
      expect(documentProcessingService).toBeDefined();
    });

    it('should create DocumentClassificationService', () => {
      expect(documentClassificationService).toBeDefined();
    });

    // it('should create DocumentVersionService', () => {
    //   expect(documentVersionService).toBeDefined();
    // }); // REMOVED: Service disabled

    // it('should create DocumentSearchService', () => {
    //   expect(documentSearchService).toBeDefined();
    // }); // REMOVED (deprecated)
  });

  describe('DocumentVaultService Basic Functions', () => {
    it('should have calculateFileHash method', () => {
      expect(typeof documentVaultService.calculateFileHash).toBe('function');
    });

    it('should calculate file hash correctly', () => {
      const testBuffer = Buffer.from('test content');
      const hash = documentVaultService.calculateFileHash(testBuffer);

      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
      expect(hash.length).toBe(64); // SHA-256 produces 64-character hex string
    });
  });

  describe('DocumentProcessingService Basic Functions', () => {
    it('should have extractTextFromDocument method', () => {
      expect(typeof documentProcessingService.extractTextFromDocument).toBe(
        'function',
      );
    });

    it('should handle text files', async () => {
      const mockTextFile = {
        fieldname: 'file',
        originalname: 'test.txt',
        encoding: '7bit',
        mimetype: 'text/plain',
        size: 1024,
        buffer: Buffer.from('This is test content'),
        destination: '',
        filename: '',
        path: '',
        stream: null,
      } as Express.Multer.File;

      const result =
        await documentProcessingService.extractTextFromDocument(mockTextFile);
      expect(result).toBe('This is test content');
    });
  });

  describe('DocumentClassificationService Basic Functions', () => {
    it('should have classifyDocument method', () => {
      expect(typeof documentClassificationService.classifyDocument).toBe(
        'function',
      );
    });
  });

  // describe('DocumentVersionService Basic Functions', () => {
  //   it('should have createDocumentVersion method', () => {
  //     expect(typeof documentVersionService.createDocumentVersion).toBe(
  //       'function',
  //     );
  //   });

  //   it('should have getDocumentVersionHistory method', () => {
  //     expect(typeof documentVersionService.getDocumentVersionHistory).toBe(
  //       'function',
  //     );
  //   });
  // }); // REMOVED: Service disabled

  // describe('DocumentSearchService Basic Functions', () => {
  //   it('should have searchDocuments method', () => {
  //     expect(typeof documentSearchService.searchDocuments).toBe('function');
  //   });

  //   it('should have searchByTags method', () => {
  //     expect(typeof documentSearchService.searchByTags).toBe('function');
  //   });
  // }); // REMOVED (deprecated)

  describe('Integration Test - File Hash Consistency', () => {
    it('should produce consistent hashes for same content', () => {
      const content1 = Buffer.from('identical content');
      const content2 = Buffer.from('identical content');

      const hash1 = documentVaultService.calculateFileHash(content1);
      const hash2 = documentVaultService.calculateFileHash(content2);

      expect(hash1).toBe(hash2);
    });

    it('should produce different hashes for different content', () => {
      const content1 = Buffer.from('content one');
      const content2 = Buffer.from('content two');

      const hash1 = documentVaultService.calculateFileHash(content1);
      const hash2 = documentVaultService.calculateFileHash(content2);

      expect(hash1).not.toBe(hash2);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid file types in processing service', async () => {
      const invalidFile = {
        fieldname: 'file',
        originalname: 'test.exe',
        encoding: '7bit',
        mimetype: 'application/exe',
        size: 1024,
        buffer: Buffer.from('invalid content'),
        destination: '',
        filename: '',
        path: '',
        stream: null,
      } as Express.Multer.File;

      await expect(
        documentProcessingService.extractTextFromDocument(invalidFile),
      ).rejects.toThrow();
    });
  });

  describe('Document Classification Logic', () => {
    it('should analyze filename patterns', async () => {
      const passportFile = {
        fieldname: 'file',
        originalname: 'passport.pdf',
        encoding: '7bit',
        mimetype: 'application/pdf',
        size: 1024,
        buffer: Buffer.from('passport content'),
        destination: '',
        filename: '',
        path: '',
        stream: null,
      } as Express.Multer.File;

      // Mock the text extraction to return empty string for this test
      jest
        .spyOn(documentProcessingService, 'extractTextFromDocument')
        .mockResolvedValue('');

      const result =
        await documentClassificationService.classifyDocument(passportFile);

      expect(result).toBeDefined();
      expect(result.type).toBeDefined();
      expect(result.confidence).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBeLessThanOrEqual(1);
    });
  });
});
