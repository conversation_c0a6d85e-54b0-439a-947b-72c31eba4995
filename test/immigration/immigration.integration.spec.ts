/**
 * Immigration Integration Tests
 *
 * End-to-end tests for immigration endpoints including:
 * - Authentication and authorization
 * - Public vs admin access
 * - Visibility filtering
 * - Error handling
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as request from 'supertest';
import { ImmigrationModule } from '../../src/immigration/immigration.module';
import { PrismaService } from '../../src/utils/prisma.service';

describe('Immigration Integration Tests', () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let adminToken: string;

  const mockPrismaService = {
    immigration_service: {
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findMany: jest.fn(),
    },
  };

  const testImmigrationService = {
    id: 'immigration_test_123',
    name: 'Test Work Permit',
    amount: 50000,
    service: ['document_review', 'application_submission'],
    order: 1,
    website_visible: true,
    createdAt: '2025-07-08T19:16:03.656Z',
    updatedAt: '2025-07-08T19:16:03.656Z',
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [ImmigrationModule],
    })
      .overrideProvider(PrismaService)
      .useValue(mockPrismaService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );
    await app.init();

    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Generate admin token for testing
    adminToken = jwtService.sign(
      { id: 'admin_123', email: '<EMAIL>', tokenType: 'admin' },
      { secret: process.env.jwtAdminSecretKey || 'test-admin-secret' },
    );
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /immigration', () => {
    it('should return all immigration services when no filter is provided (backward compatibility)', async () => {
      const allServices = [
        testImmigrationService,
        {
          ...testImmigrationService,
          id: 'immigration_456',
          name: 'Student Visa',
          website_visible: false,
        },
      ];

      mockPrismaService.immigration_service.findMany.mockResolvedValue(
        allServices,
      );

      const response = await request(app.getHttpServer())
        .get('/immigration')
        .expect(200);

      expect(response.body).toEqual(allServices);
      expect(
        mockPrismaService.immigration_service.findMany,
      ).toHaveBeenCalledWith({
        where: {},
        orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
      });
    });

    it('should return only visible immigration services when website_visible=true', async () => {
      const visibleServices = [
        testImmigrationService,
        {
          ...testImmigrationService,
          id: 'immigration_456',
          name: 'Student Visa',
        },
      ];

      mockPrismaService.immigration_service.findMany.mockResolvedValue(
        visibleServices,
      );

      const response = await request(app.getHttpServer())
        .get('/immigration?website_visible=true')
        .expect(200);

      expect(response.body).toEqual(visibleServices);
      expect(
        mockPrismaService.immigration_service.findMany,
      ).toHaveBeenCalledWith({
        where: { website_visible: true },
        orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
      });
    });

    it('should return only hidden immigration services when website_visible=false', async () => {
      const hiddenServices = [
        {
          ...testImmigrationService,
          id: 'immigration_789',
          name: 'Hidden Service',
          website_visible: false,
        },
      ];

      mockPrismaService.immigration_service.findMany.mockResolvedValue(
        hiddenServices,
      );

      const response = await request(app.getHttpServer())
        .get('/immigration?website_visible=false')
        .expect(200);

      expect(response.body).toEqual(hiddenServices);
      expect(
        mockPrismaService.immigration_service.findMany,
      ).toHaveBeenCalledWith({
        where: { website_visible: false },
        orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
      });
    });

    it('should return empty array when no services match the filter', async () => {
      mockPrismaService.immigration_service.findMany.mockResolvedValue([]);

      const response = await request(app.getHttpServer())
        .get('/immigration?website_visible=true')
        .expect(200);

      expect(response.body).toEqual([]);
      expect(
        mockPrismaService.immigration_service.findMany,
      ).toHaveBeenCalledWith({
        where: { website_visible: true },
        orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
      });
    });

    it('should handle invalid website_visible values gracefully', async () => {
      const response = await request(app.getHttpServer())
        .get('/immigration?website_visible=invalid')
        .expect(400);

      expect(response.body).toHaveProperty('message');
      expect(
        Array.isArray(response.body.message)
          ? response.body.message
          : [response.body.message],
      ).toContain('website_visible must be either "true" or "false"');
      expect(
        mockPrismaService.immigration_service.findMany,
      ).not.toHaveBeenCalled();
    });
  });

  describe('POST /immigration', () => {
    it('should create immigration service with admin authentication', async () => {
      const createData = {
        name: 'New Work Permit',
        amount: 60000,
        service: ['document_review', 'application_submission', 'follow_up'],
        order: 2,
        website_visible: true,
      };

      mockPrismaService.immigration_service.create.mockResolvedValue({
        ...testImmigrationService,
        ...createData,
        id: 'new_immigration_123',
      });

      const response = await request(app.getHttpServer())
        .post('/immigration')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(createData)
        .expect(201);

      expect(response.body).toMatchObject(createData);
      expect(mockPrismaService.immigration_service.create).toHaveBeenCalledWith(
        {
          data: createData,
        },
      );
    });

    it('should reject creation without admin authentication', async () => {
      const createData = {
        name: 'New Work Permit',
        amount: 60000,
        service: ['document_review'],
        order: 2,
      };

      await request(app.getHttpServer())
        .post('/immigration')
        .send(createData)
        .expect(401);

      expect(
        mockPrismaService.immigration_service.create,
      ).not.toHaveBeenCalled();
    });
  });

  describe('PATCH /immigration/:id/visibility', () => {
    it('should update visibility with admin authentication', async () => {
      const visibilityData = { website_visible: false };
      const updatedService = {
        ...testImmigrationService,
        website_visible: false,
      };

      mockPrismaService.immigration_service.update.mockResolvedValue(
        updatedService,
      );

      const response = await request(app.getHttpServer())
        .patch('/immigration/immigration_test_123/visibility')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(visibilityData)
        .expect(200);

      expect(response.body).toMatchObject(updatedService);
      expect(mockPrismaService.immigration_service.update).toHaveBeenCalledWith(
        {
          where: { id: 'immigration_test_123' },
          data: { website_visible: false },
        },
      );
    });

    it('should reject visibility update without admin authentication', async () => {
      const visibilityData = { website_visible: false };

      await request(app.getHttpServer())
        .patch('/immigration/immigration_test_123/visibility')
        .send(visibilityData)
        .expect(401);

      expect(
        mockPrismaService.immigration_service.update,
      ).not.toHaveBeenCalled();
    });

    it('should validate visibility data', async () => {
      const invalidData = { website_visible: 'invalid' };

      await request(app.getHttpServer())
        .patch('/immigration/immigration_test_123/visibility')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidData)
        .expect(400);
    });
  });

  describe('PATCH /immigration/:id', () => {
    it('should update immigration service with admin authentication', async () => {
      const updateData = {
        name: 'Updated Work Permit',
        amount: 55000,
        service: [
          'document_review',
          'application_submission',
          'interview_prep',
        ],
        order: 1,
        website_visible: true,
      };

      const updatedService = { ...testImmigrationService, ...updateData };
      mockPrismaService.immigration_service.update.mockResolvedValue(
        updatedService,
      );

      const response = await request(app.getHttpServer())
        .patch('/immigration/immigration_test_123')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toMatchObject(updatedService);
      expect(mockPrismaService.immigration_service.update).toHaveBeenCalledWith(
        {
          where: { id: 'immigration_test_123' },
          data: updateData,
        },
      );
    });

    it('should reject update without admin authentication', async () => {
      const updateData = { name: 'Updated Work Permit' };

      await request(app.getHttpServer())
        .patch('/immigration/immigration_test_123')
        .send(updateData)
        .expect(401);
    });
  });

  describe('DELETE /immigration/:id', () => {
    it('should delete immigration service with admin authentication', async () => {
      mockPrismaService.immigration_service.delete.mockResolvedValue(
        testImmigrationService,
      );

      const response = await request(app.getHttpServer())
        .delete('/immigration/immigration_test_123')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toEqual(testImmigrationService);
      expect(mockPrismaService.immigration_service.delete).toHaveBeenCalledWith(
        {
          where: { id: 'immigration_test_123' },
        },
      );
    });

    it('should reject deletion without admin authentication', async () => {
      await request(app.getHttpServer())
        .delete('/immigration/immigration_test_123')
        .expect(401);
    });
  });
});
