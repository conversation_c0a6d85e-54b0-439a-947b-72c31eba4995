/**
 * Agent Service Tests
 *
 * Comprehensive tests for the AgentService.
 * Tests authentication, CRUD operations, and email functionality.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import {
  ConflictException,
  NotFoundException,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { AgentService } from '../../src/agent/agent.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { MailerService } from '../../src/mailer/mailer.service';
import { LoggerService } from '../../src/utils/logger.service';
import { AgentStatus } from '@prisma/client';
import * as bcrypt from 'bcrypt';

// Mock bcrypt
jest.mock('bcrypt');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('AgentService', () => {
  let service: AgentService;
  let prismaService: PrismaService;
  let jwtService: JwtService;
  let mailerService: MailerService;
  let loggerService: LoggerService;

  const mockPrismaService = {
    agent: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    admin: {
      findUnique: jest.fn(),
    },
    application: {
      count: jest.fn(),
    },
  };

  const mockJwtService = {
    signAsync: jest.fn(),
    verifyAsync: jest.fn(),
  };

  const mockMailerService = {
    sendEmail: jest.fn(),
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AgentService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    service = module.get<AgentService>(AgentService);
    prismaService = module.get<PrismaService>(PrismaService);
    jwtService = module.get<JwtService>(JwtService);
    mailerService = module.get<MailerService>(MailerService);
    loggerService = module.get<LoggerService>(LoggerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    const createAgentDto = {
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+353-1-234-5678',
      status: AgentStatus.Active,
    };

    const adminId = 'admin_123';

    it('should register a new agent successfully', async () => {
      const mockAdmin = {
        id: adminId,
        name: 'Admin User',
        email: '<EMAIL>',
      };
      const mockAgent = {
        id: 'agent_123',
        name: 'John Smith',
        email: '<EMAIL>',
        password_hash: 'hashed_password',
        phone: '+353-1-234-5678',
        status: AgentStatus.Active,
        created_at: new Date(),
        updated_at: new Date(),
        created_by_admin_id: adminId,
      };

      mockPrismaService.agent.findUnique.mockResolvedValue(null);
      mockPrismaService.admin.findUnique.mockResolvedValue(mockAdmin);
      mockedBcrypt.hash.mockResolvedValue('hashed_password' as never);
      mockPrismaService.agent.create.mockResolvedValue(mockAgent);
      mockMailerService.sendEmail.mockResolvedValue(undefined);

      const result = await service.register(createAgentDto, adminId);

      expect(result.agent).toEqual(mockAgent);
      expect(result.temporaryPassword).toBeDefined();
      expect(result.temporaryPassword).toHaveLength(16); // 8 bytes * 2 (hex)
      expect(mockPrismaService.agent.create).toHaveBeenCalled();
      expect(mockMailerService.sendEmail).toHaveBeenCalled();
    });

    it('should throw ConflictException if agent email already exists', async () => {
      const existingAgent = {
        id: 'existing_agent',
        email: '<EMAIL>',
      };

      mockPrismaService.agent.findUnique.mockResolvedValue(existingAgent);

      await expect(service.register(createAgentDto, adminId)).rejects.toThrow(
        ConflictException,
      );
      expect(mockPrismaService.agent.create).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException if admin not found', async () => {
      mockPrismaService.agent.findUnique.mockResolvedValue(null);
      mockPrismaService.admin.findUnique.mockResolvedValue(null);

      await expect(service.register(createAgentDto, adminId)).rejects.toThrow(
        NotFoundException,
      );
      expect(mockPrismaService.agent.create).not.toHaveBeenCalled();
    });
  });

  describe('login', () => {
    const loginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    it('should login agent successfully', async () => {
      const mockAgent = {
        id: 'agent_123',
        name: 'John Smith',
        email: '<EMAIL>',
        password_hash: 'hashed_password',
        status: AgentStatus.Active,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);
      mockedBcrypt.compare.mockResolvedValue(true as never);
      mockJwtService.signAsync.mockResolvedValue('mock_token');

      const result = await service.login(loginDto);

      expect(result.user).toEqual(
        expect.objectContaining({
          id: mockAgent.id,
          name: mockAgent.name,
          email: mockAgent.email,
          status: mockAgent.status,
        }),
      );
      expect(result.user).not.toHaveProperty('password_hash');
      expect(result.backendTokens.accessToken).toBe('mock_token');
      expect(result.backendTokens.refreshToken).toBe('mock_token');
    });

    it('should throw UnauthorizedException for invalid credentials', async () => {
      mockPrismaService.agent.findUnique.mockResolvedValue(null);

      await expect(service.login(loginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw UnauthorizedException for inactive agent', async () => {
      const inactiveAgent = {
        id: 'agent_123',
        email: '<EMAIL>',
        password_hash: 'hashed_password',
        status: AgentStatus.Inactive,
      };

      mockPrismaService.agent.findUnique.mockResolvedValue(inactiveAgent);
      mockedBcrypt.compare.mockResolvedValue(true as never);

      await expect(service.login(loginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });

  describe('updatePassword', () => {
    const agentId = 'agent_123';
    const updatePasswordDto = {
      currentPassword: 'oldPassword123',
      newPassword: 'newPassword123',
    };

    it('should update password successfully', async () => {
      const mockAgent = {
        id: agentId,
        email: '<EMAIL>',
        password_hash: 'old_hashed_password',
      };

      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);
      mockedBcrypt.compare.mockResolvedValue(true as never);
      mockedBcrypt.hash.mockResolvedValue('new_hashed_password' as never);
      mockPrismaService.agent.update.mockResolvedValue(undefined);

      await service.updatePassword(agentId, updatePasswordDto);

      expect(mockPrismaService.agent.update).toHaveBeenCalledWith({
        where: { id: agentId },
        data: { password_hash: 'new_hashed_password' },
      });
    });

    it('should throw NotFoundException if agent not found', async () => {
      mockPrismaService.agent.findUnique.mockResolvedValue(null);

      await expect(
        service.updatePassword(agentId, updatePasswordDto),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw UnauthorizedException for incorrect current password', async () => {
      const mockAgent = {
        id: agentId,
        password_hash: 'old_hashed_password',
      };

      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);
      mockedBcrypt.compare.mockResolvedValue(false as never);

      await expect(
        service.updatePassword(agentId, updatePasswordDto),
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('findAll', () => {
    const queryDto = {
      page: 1,
      limit: 10,
      status: AgentStatus.Active,
      search: 'john',
    };

    it('should return paginated agents list', async () => {
      const mockAgents = [
        {
          id: 'agent_1',
          name: 'John Smith',
          email: '<EMAIL>',
          status: AgentStatus.Active,
          created_by_admin: {
            id: 'admin_1',
            name: 'Admin User',
            email: '<EMAIL>',
          },
        },
      ];

      mockPrismaService.agent.count.mockResolvedValue(1);
      mockPrismaService.agent.findMany.mockResolvedValue(mockAgents);

      const result = await service.findAll(queryDto);

      expect(result).toEqual({
        agents: mockAgents,
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
    });
  });

  describe('delete', () => {
    const agentId = 'agent_123';
    const deletedBy = 'admin_123';

    it('should soft delete agent successfully', async () => {
      const mockAgent = {
        id: agentId,
        email: '<EMAIL>',
        status: AgentStatus.Active,
      };

      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);
      mockPrismaService.application.count.mockResolvedValue(0);
      mockPrismaService.agent.update.mockResolvedValue(undefined);

      await service.delete(agentId, deletedBy);

      expect(mockPrismaService.agent.update).toHaveBeenCalledWith({
        where: { id: agentId },
        data: { status: AgentStatus.Inactive },
      });
    });

    it('should throw BadRequestException if agent has active assignments', async () => {
      const mockAgent = {
        id: agentId,
        email: '<EMAIL>',
      };

      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);
      mockPrismaService.application.count.mockResolvedValue(5);

      await expect(service.delete(agentId, deletedBy)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('resetPassword', () => {
    const resetPasswordDto = {
      email: '<EMAIL>',
    };

    it('should send password reset email for existing agent', async () => {
      const resetToken = 'mock_reset_token';
      const mockAgent = {
        id: 'agent_123',
        name: 'John Smith',
        email: '<EMAIL>',
        password_hash: 'hashed_password',
        status: AgentStatus.Active,
      };

      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);
      mockJwtService.signAsync.mockResolvedValue(resetToken);
      mockMailerService.sendEmail.mockResolvedValue({ id: 'email_123' });

      await service.resetPassword(resetPasswordDto);

      expect(mockPrismaService.agent.findUnique).toHaveBeenCalledWith({
        where: { email: resetPasswordDto.email },
      });
      expect(mockJwtService.signAsync).toHaveBeenCalledWith(
        { email: mockAgent.email, type: 'password_reset' },
        {
          secret: process.env.RESET_PASSWORD_SECRET || 'reset-secret',
          expiresIn: '1h',
        },
      );
      expect(mockMailerService.sendEmail).toHaveBeenCalled();
      expect(mockLoggerService.info).toHaveBeenCalledWith(
        `Password reset email sent to agent: ${mockAgent.email}`,
      );
    });

    it('should not reveal if agent does not exist', async () => {
      mockPrismaService.agent.findUnique.mockResolvedValue(null);

      await service.resetPassword(resetPasswordDto);

      expect(mockPrismaService.agent.findUnique).toHaveBeenCalledWith({
        where: { email: resetPasswordDto.email },
      });
      expect(mockLoggerService.warn).toHaveBeenCalledWith(
        `Password reset attempted for non-existent agent: ${resetPasswordDto.email}`,
      );
      expect(mockJwtService.signAsync).not.toHaveBeenCalled();
      expect(mockMailerService.sendEmail).not.toHaveBeenCalled();
    });

    it('should handle email sending errors', async () => {
      const resetToken = 'mock_reset_token';
      const emailError = new Error('Email service unavailable');
      const mockAgent = {
        id: 'agent_123',
        email: '<EMAIL>',
      };

      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);
      mockJwtService.signAsync.mockResolvedValue(resetToken);
      mockMailerService.sendEmail.mockRejectedValue(emailError);

      await expect(service.resetPassword(resetPasswordDto)).rejects.toThrow(emailError);

      expect(mockLoggerService.error).toHaveBeenCalledWith(
        `Failed to reset agent password: ${emailError.message}`,
        emailError.stack,
      );
    });
  });

  describe('confirmResetPassword', () => {
    const confirmResetDto = {
      token: 'valid_reset_token',
      newPassword: 'NewSecurePassword123!',
    };

    it('should reset password with valid token', async () => {
      const tokenPayload = {
        email: '<EMAIL>',
        type: 'password_reset',
      };
      const hashedPassword = 'new_hashed_password';
      const mockAgent = {
        id: 'agent_123',
        name: 'John Smith',
        email: '<EMAIL>',
        password_hash: 'old_hashed_password',
        status: AgentStatus.Active,
      };

      mockJwtService.verifyAsync.mockResolvedValue(tokenPayload);
      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);
      mockedBcrypt.hash.mockResolvedValue(hashedPassword as never);
      mockPrismaService.agent.update.mockResolvedValue({
        ...mockAgent,
        password_hash: hashedPassword,
      });

      await service.confirmResetPassword(confirmResetDto);

      expect(mockJwtService.verifyAsync).toHaveBeenCalledWith(
        confirmResetDto.token,
        { secret: process.env.RESET_PASSWORD_SECRET || 'reset-secret' },
      );
      expect(mockPrismaService.agent.findUnique).toHaveBeenCalledWith({
        where: { email: tokenPayload.email },
      });
      expect(mockedBcrypt.hash).toHaveBeenCalledWith(confirmResetDto.newPassword, 10);
      expect(mockPrismaService.agent.update).toHaveBeenCalledWith({
        where: { id: mockAgent.id },
        data: { password_hash: hashedPassword },
      });
      expect(mockLoggerService.info).toHaveBeenCalledWith(
        `Password reset completed successfully for agent: ${mockAgent.email}`,
      );
    });

    it('should throw UnauthorizedException for invalid token type', async () => {
      const tokenPayload = {
        email: '<EMAIL>',
        type: 'invalid_type',
      };

      mockJwtService.verifyAsync.mockResolvedValue(tokenPayload);

      await expect(service.confirmResetPassword(confirmResetDto)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(mockLoggerService.error).toHaveBeenCalled();
    });

    it('should throw NotFoundException for non-existent agent', async () => {
      const tokenPayload = {
        email: '<EMAIL>',
        type: 'password_reset',
      };

      mockJwtService.verifyAsync.mockResolvedValue(tokenPayload);
      mockPrismaService.agent.findUnique.mockResolvedValue(null);

      await expect(service.confirmResetPassword(confirmResetDto)).rejects.toThrow(
        NotFoundException,
      );
      expect(mockLoggerService.error).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException for expired token', async () => {
      const expiredTokenError = new Error('jwt expired');
      expiredTokenError.name = 'TokenExpiredError';

      mockJwtService.verifyAsync.mockRejectedValue(expiredTokenError);

      await expect(service.confirmResetPassword(confirmResetDto)).rejects.toThrow(
        expiredTokenError,
      );
      expect(mockLoggerService.error).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException for invalid token', async () => {
      const invalidTokenError = new Error('invalid token');
      invalidTokenError.name = 'JsonWebTokenError';

      mockJwtService.verifyAsync.mockRejectedValue(invalidTokenError);

      await expect(service.confirmResetPassword(confirmResetDto)).rejects.toThrow(
        invalidTokenError,
      );
      expect(mockLoggerService.error).toHaveBeenCalled();
    });
  });

  describe('utility methods', () => {
    it('should generate temporary password', () => {
      const password = service.generateTemporaryPassword();
      expect(password).toHaveLength(16);
      expect(password).toMatch(/^[A-F0-9]+$/);
    });

    it('should hash password', async () => {
      mockedBcrypt.hash.mockResolvedValue('hashed_password' as never);

      const result = await service.hashPassword('password123');

      expect(result).toBe('hashed_password');
      expect(mockedBcrypt.hash).toHaveBeenCalledWith('password123', 10);
    });

    it('should validate password', async () => {
      mockedBcrypt.compare.mockResolvedValue(true as never);

      const result = await service.validatePassword(
        'password123',
        'hashed_password',
      );

      expect(result).toBe(true);
      expect(mockedBcrypt.compare).toHaveBeenCalledWith(
        'password123',
        'hashed_password',
      );
    });
  });
});
