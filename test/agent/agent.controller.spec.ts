/**
 * Agent Controller Tests
 *
 * Integration tests for the AgentController.
 * Tests all endpoints with proper authentication and authorization.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { AgentController } from '../../src/agent/agent.controller';
import { AgentService } from '../../src/agent/agent.service';
import { LoggerService } from '../../src/utils/logger.service';
import { AgentStatus } from '@prisma/client';

describe('AgentController', () => {
  let controller: AgentController;
  let agentService: AgentService;
  let loggerService: LoggerService;

  const mockAgentService = {
    register: jest.fn(),
    login: jest.fn(),
    refreshToken: jest.fn(),
    updatePassword: jest.fn(),
    resetPassword: jest.fn(),
    confirmResetPassword: jest.fn(),
    findAll: jest.fn(),
    findByIdWithAdmin: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AgentController],
      providers: [
        {
          provide: AgentService,
          useValue: mockAgentService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    controller = module.get<AgentController>(AgentController);
    agentService = module.get<AgentService>(AgentService);
    loggerService = module.get<LoggerService>(LoggerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    const createAgentDto = {
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+353-1-234-5678',
    };

    const mockAdmin = { id: 'admin_123', email: '<EMAIL>' };

    it('should register agent successfully', async () => {
      const mockResult = {
        agent: {
          id: 'agent_123',
          name: 'John Smith',
          email: '<EMAIL>',
          phone: '+353-1-234-5678',
          status: AgentStatus.Active,
          created_at: new Date(),
          updated_at: new Date(),
        },
        temporaryPassword: 'TEMP123456',
      };

      mockAgentService.register.mockResolvedValue(mockResult);

      const result = await controller.register(createAgentDto, mockAdmin);

      expect(result).toEqual({
        success: true,
        message: 'Agent created successfully. Welcome email sent.',
        agent: mockResult.agent,
        temporaryPassword: mockResult.temporaryPassword,
      });
      expect(mockAgentService.register).toHaveBeenCalledWith(
        createAgentDto,
        mockAdmin.id,
      );
    });

    it('should handle registration errors', async () => {
      mockAgentService.register.mockRejectedValue(
        new Error('Registration failed'),
      );

      await expect(
        controller.register(createAgentDto, mockAdmin),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('login', () => {
    const loginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    it('should login agent successfully', async () => {
      const mockResult = {
        agent: {
          id: 'agent_123',
          name: 'John Smith',
          email: '<EMAIL>',
          status: AgentStatus.Active,
        },
        backendTokens: {
          accessToken: 'access_token',
          refreshToken: 'refresh_token',
          expiresIn: 123456789,
        },
      };

      mockAgentService.login.mockResolvedValue(mockResult);

      const result = await controller.login(loginDto);

      expect(result).toEqual(mockResult);
      expect(mockAgentService.login).toHaveBeenCalledWith(loginDto);
    });

    it('should handle login errors', async () => {
      mockAgentService.login.mockRejectedValue(new Error('Login failed'));

      await expect(controller.login(loginDto)).rejects.toThrow(HttpException);
    });
  });

  describe('updatePassword', () => {
    const agentId = 'agent_123';
    const updatePasswordDto = {
      currentPassword: 'oldPassword123',
      newPassword: 'newPassword123',
    };
    const mockAgent = { id: agentId, email: '<EMAIL>' };

    it('should update password successfully', async () => {
      mockAgentService.updatePassword.mockResolvedValue(undefined);

      const result = await controller.updatePassword(
        agentId,
        updatePasswordDto,
        mockAgent,
      );

      expect(result).toEqual({
        success: true,
        message: 'Password updated successfully',
      });
      expect(mockAgentService.updatePassword).toHaveBeenCalledWith(
        agentId,
        updatePasswordDto,
      );
    });

    it('should throw forbidden error if agent tries to update another agent password', async () => {
      const differentAgentId = 'agent_456';

      await expect(
        controller.updatePassword(
          differentAgentId,
          updatePasswordDto,
          mockAgent,
        ),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('resetPassword', () => {
    const resetPasswordDto = {
      email: '<EMAIL>',
    };

    it('should send reset password email', async () => {
      mockAgentService.resetPassword.mockResolvedValue(undefined);

      const result = await controller.resetPassword(resetPasswordDto);

      expect(result).toEqual({
        success: true,
        message:
          'If an agent account with this email exists, a password reset link has been sent.',
      });
      expect(mockAgentService.resetPassword).toHaveBeenCalledWith(
        resetPasswordDto,
      );
    });

    it('should return success message even if service throws error', async () => {
      mockAgentService.resetPassword.mockRejectedValue(
        new Error('Service error'),
      );

      const result = await controller.resetPassword(resetPasswordDto);

      expect(result).toEqual({
        success: true,
        message:
          'If an agent account with this email exists, a password reset link has been sent.',
      });
    });
  });

  describe('getAllAgents', () => {
    const queryDto = {
      page: 1,
      limit: 10,
      status: AgentStatus.Active,
      search: 'john',
    };

    it('should return paginated agents list', async () => {
      const mockResult = {
        agents: [
          {
            id: 'agent_1',
            name: 'John Smith',
            email: '<EMAIL>',
            status: AgentStatus.Active,
            created_by_admin: {
              id: 'admin_1',
              name: 'Admin User',
              email: '<EMAIL>',
            },
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      mockAgentService.findAll.mockResolvedValue(mockResult);

      const result = await controller.getAllAgents(queryDto);

      expect(result).toEqual(mockResult);
      expect(mockAgentService.findAll).toHaveBeenCalledWith(queryDto);
    });
  });

  describe('getAgentById', () => {
    const agentId = 'agent_123';
    const mockAgent = {
      id: agentId,
      name: 'John Smith',
      email: '<EMAIL>',
      status: AgentStatus.Active,
      created_by_admin: {
        id: 'admin_1',
        name: 'Admin User',
        email: '<EMAIL>',
      },
    };

    it('should return agent details for admin', async () => {
      const mockAdmin = { id: 'admin_123', tokenType: 'admin' };
      mockAgentService.findByIdWithAdmin.mockResolvedValue(mockAgent);

      const result = await controller.getAgentById(agentId, mockAdmin);

      expect(result).toEqual(mockAgent);
      expect(mockAgentService.findByIdWithAdmin).toHaveBeenCalledWith(agentId);
    });

    it('should return agent details for self-access', async () => {
      const mockAgentUser = { id: agentId, tokenType: 'agent' };
      mockAgentService.findByIdWithAdmin.mockResolvedValue(mockAgent);

      const result = await controller.getAgentById(agentId, mockAgentUser);

      expect(result).toEqual(mockAgent);
    });

    it('should throw forbidden error if agent tries to access another agent', async () => {
      const mockAgentUser = { id: 'agent_456', tokenType: 'agent' };

      await expect(
        controller.getAgentById(agentId, mockAgentUser),
      ).rejects.toThrow(HttpException);
    });

    it('should throw not found error if agent does not exist', async () => {
      const mockAdmin = { id: 'admin_123', tokenType: 'admin' };
      mockAgentService.findByIdWithAdmin.mockResolvedValue(null);

      await expect(controller.getAgentById(agentId, mockAdmin)).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('updateAgent', () => {
    const agentId = 'agent_123';
    const updateAgentDto = {
      name: 'John Smith Updated',
      email: '<EMAIL>',
    };

    it('should update agent successfully for admin', async () => {
      const mockAdmin = { id: 'admin_123', tokenType: 'admin' };
      const mockUpdatedAgent = {
        id: agentId,
        name: 'John Smith Updated',
        email: '<EMAIL>',
        phone: '+353-1-234-5678',
        status: AgentStatus.Active,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockAgentService.update.mockResolvedValue(mockUpdatedAgent);

      const result = await controller.updateAgent(
        agentId,
        updateAgentDto,
        mockAdmin,
      );

      expect(result).toEqual({
        success: true,
        message: 'Agent updated successfully',
        agent: mockUpdatedAgent,
      });
      expect(mockAgentService.update).toHaveBeenCalledWith(
        agentId,
        updateAgentDto,
        mockAdmin.id,
      );
    });

    it('should prevent agent from updating status', async () => {
      const mockAgentUser = { id: agentId, tokenType: 'agent' };
      const updateWithStatus = {
        ...updateAgentDto,
        status: AgentStatus.Suspended,
      };

      await expect(
        controller.updateAgent(agentId, updateWithStatus, mockAgentUser),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('deleteAgent', () => {
    const agentId = 'agent_123';
    const mockAdmin = { id: 'admin_123', email: '<EMAIL>' };

    it('should delete agent successfully', async () => {
      mockAgentService.delete.mockResolvedValue(undefined);

      const result = await controller.deleteAgent(agentId, mockAdmin);

      expect(result).toEqual({
        success: true,
        message: 'Agent deleted successfully',
      });
      expect(mockAgentService.delete).toHaveBeenCalledWith(
        agentId,
        mockAdmin.id,
      );
    });

    it('should handle deletion errors', async () => {
      mockAgentService.delete.mockRejectedValue(new Error('Deletion failed'));

      await expect(controller.deleteAgent(agentId, mockAdmin)).rejects.toThrow(
        HttpException,
      );
    });
  });
});
