/**
 * Test Environment Setup for Agent Tests
 *
 * Sets up environment variables and configurations needed for agent testing.
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/careerireland_test';

// JWT secrets for testing
process.env.jwtSecretKey = 'test-user-secret-key-for-testing-only';
process.env.jwtAdminSecretKey = 'test-admin-secret-key-for-testing-only';
process.env.jwtAgentSecretKey = 'test-agent-secret-key-for-testing-only';
process.env.jwtMentorSecretKey = 'test-mentor-secret-key-for-testing-only';
process.env.jwtRefreshTokenKey = 'test-refresh-secret-key-for-testing-only';
process.env.RESET_PASSWORD_SECRET = 'test-reset-password-secret-for-testing-only';

// Token expiry settings
process.env.ACCESS_TOKEN_EXPIRY_DATE = '5h';
process.env.REFRESH_TOKEN_EXPIRY_DATE = '7d';

// Email configuration for testing
process.env.EMAIL = '<EMAIL>';
process.env.WEBSITE = 'https://test.careerireland.com';
process.env.VERIFY_CLIENT_EMAIL_URL = 'https://test.careerireland.com/verify-email';

// Disable email sending in tests
process.env.DISABLE_EMAIL_SENDING = 'true';

// Logging configuration
process.env.LOG_LEVEL = 'error'; // Reduce log noise in tests

// Other test configurations
process.env.BCRYPT_ROUNDS = '1'; // Faster hashing for tests
process.env.TEST_MODE = 'true';

console.log('Agent test environment configured');
