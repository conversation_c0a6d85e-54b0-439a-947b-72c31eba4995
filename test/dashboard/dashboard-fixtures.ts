/**
 * Dashboard Test Fixtures
 *
 * This file contains mock data and fixtures for dashboard module testing.
 * It provides consistent test data for unit and integration tests.
 *
 * <AUTHOR> Ireland Development Team
 * @version 2.0.0
 * @since 2024-12-27
 */

import {
  DashboardAnalytics,
  TopRatedMentor,
  LatestUser,
  LatestContact,
} from '../../src/dashboard/dashboard.service';

/**
 * Mock mentor data with reviews
 */
export const mockMentorsWithReviews = [
  {
    id: 'mentor-1',
    name: '<PERSON>',
    image: 'https://example.com/john.jpg',
    reviews: [{ rating: 5 }, { rating: 4 }, { rating: 5 }],
  },
  {
    id: 'mentor-2',
    name: '<PERSON>',
    image: 'https://example.com/jane.jpg',
    reviews: [{ rating: 4 }, { rating: 5 }],
  },
  {
    id: 'mentor-3',
    name: '<PERSON>',
    image: null,
    reviews: [{ rating: 3 }, { rating: 4 }, { rating: 4 }, { rating: 5 }],
  },
];

/**
 * Mock payment revenue data by service type
 */
export const mockRevenueByServiceType = [
  {
    service_type: 'mentor',
    _sum: { amount: 15000 },
  },
  {
    service_type: 'package',
    _sum: { amount: 8500 },
  },
  {
    service_type: 'immigration',
    _sum: { amount: 12000 },
  },
  {
    service_type: 'training',
    _sum: { amount: 6500 },
  },
];

/**
 * Mock payment aggregate data for mentor revenue
 */
export const mockMentorRevenueData = {
  _sum: { amount: 5000 },
};

/**
 * Mock unique user payments for client count
 */
export const mockUniqueUserPayments = [
  { userId: 'user-1' },
  { userId: 'user-2' },
  { userId: 'user-3' },
];

/**
 * Mock latest users data
 */
export const mockLatestUsers: LatestUser[] = [
  {
    id: 'user-1',
    name: 'Alice Johnson',
    email: '<EMAIL>',
    image: 'https://example.com/alice.jpg',
    createdAt: new Date('2024-12-27T10:00:00Z'),
  },
  {
    id: 'user-2',
    name: 'Bob Smith',
    email: '<EMAIL>',
    image: null,
    createdAt: new Date('2024-12-26T15:30:00Z'),
  },
  {
    id: 'user-3',
    name: 'Carol Davis',
    email: '<EMAIL>',
    image: 'https://example.com/carol.jpg',
    createdAt: new Date('2024-12-25T09:15:00Z'),
  },
];

/**
 * Mock latest contacts data
 */
export const mockLatestContacts: LatestContact[] = [
  {
    id: 'contact-1',
    name: 'David Wilson',
    email: '<EMAIL>',
    message: 'I need help with immigration services.',
    createdAt: new Date('2024-12-27T14:20:00Z'),
  },
  {
    id: 'contact-2',
    name: 'Emma Brown',
    email: '<EMAIL>',
    message: 'Can you provide more information about mentoring packages?',
    createdAt: new Date('2024-12-26T11:45:00Z'),
  },
];

/**
 * Mock top-rated mentors data
 */
export const mockTopRatedMentors: TopRatedMentor[] = [
  {
    id: 'mentor-1',
    name: 'John Doe',
    image: 'https://example.com/john.jpg',
    average_rating: '4.67',
    review_count: '3',
    revenue_generated: '5000',
    total_clients: '3',
  },
  {
    id: 'mentor-2',
    name: 'Jane Smith',
    image: 'https://example.com/jane.jpg',
    average_rating: '4.50',
    review_count: '2',
    revenue_generated: '3500',
    total_clients: '2',
  },
];

/**
 * Mock complete dashboard analytics data
 */
export const mockDashboardAnalytics: DashboardAnalytics = {
  total_mentors: '25',
  total_users: '150',
  mentor_service_revenue: '15000',
  package_revenue: '8500',
  immigration_service_revenue: '12000',
  training_revenue: '6500',
  total_revenue: '42000',
  top_rated_mentors: mockTopRatedMentors,
  latest_users: mockLatestUsers,
  latest_contacts: mockLatestContacts,
};

/**
 * Mock user and mentor counts
 */
export const mockUserCounts = {
  total_users: '150',
  total_mentors: '25',
};

/**
 * Mock revenue analytics
 */
export const mockRevenueAnalytics = {
  mentor_service_revenue: '15000',
  package_revenue: '8500',
  immigration_service_revenue: '12000',
  training_revenue: '6500',
  total_revenue: '42000',
};

/**
 * Error scenarios for testing
 */
export const mockDatabaseError = new Error('Database connection failed');
export const mockPrismaError = new Error('Prisma query failed');
