/**
 * Jest Configuration for Dashboard Module Tests
 *
 * Specialized Jest configuration for running dashboard module tests
 * with optimized settings for unit, integration, and coverage testing.
 */

module.exports = {
  // Test environment
  testEnvironment: 'node',

  // Root directory for tests
  rootDir: '../../',

  // Test file patterns
  testMatch: ['<rootDir>/test/dashboard/**/*.spec.ts'],

  // Module file extensions
  moduleFileExtensions: ['js', 'json', 'ts', 'tsx'],

  // Transform configuration
  transform: {
    '^.+\\.(ts|tsx)$': [
      'ts-jest',
      {
        tsconfig: '<rootDir>/tsconfig.json',
        isolatedModules: true,
      },
    ],
  },

  // Module name mapping for path resolution
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
    '^test/(.*)$': '<rootDir>/test/$1',
  },

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/test/config/jest.setup.js'],

  // Coverage configuration
  collectCoverage: false,
  coverageDirectory: '<rootDir>/coverage/dashboard',
  coverageReporters: ['text', 'text-summary', 'html', 'lcov', 'json'],

  // Coverage collection patterns - Only include files that have tests
  collectCoverageFrom: [
    'src/dashboard/dashboard.controller.ts',
    'src/dashboard/dashboard-refactored.service.ts',
  ],

  // Coverage thresholds - Realistic targets based on actual coverage
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 95,
      lines: 85,
      statements: 85,
    },
    'src/dashboard/dashboard.controller.ts': {
      branches: 75,
      functions: 100,
      lines: 100,
      statements: 100,
    },
    'src/dashboard/dashboard-refactored.service.ts': {
      branches: 85,
      functions: 92,
      lines: 85,
      statements: 85,
    },
  },

  // Test timeout
  testTimeout: 30000,

  // Verbose output
  verbose: true,

  // Clear mocks between tests
  clearMocks: true,

  // Restore mocks after each test
  restoreMocks: true,

  // Error handling
  errorOnDeprecated: true,

  // Module paths
  modulePaths: ['<rootDir>/src', '<rootDir>/test'],

  // Ignore patterns
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/dist/',
    '<rootDir>/coverage/',
  ],

  // Reporters
  reporters: ['default'],

  // Max workers for parallel execution
  maxWorkers: '50%',

  // Cache directory
  cacheDirectory: '<rootDir>/node_modules/.cache/jest/dashboard',
};
