/**
 * Dashboard Controller Unit Tests
 *
 * Comprehensive unit tests for the DashboardController including authentication,
 * authorization, and proper service integration.
 *
 * Test Coverage:
 * - GET /dashboard endpoint
 * - Admin authentication and authorization
 * - Service method integration
 * - Error handling scenarios
 * - Response format validation
 *
 * <AUTHOR> Ireland Development Team
 * @version 2.0.0
 * @since 2024-12-27
 */

import { Test, TestingModule } from '@nestjs/testing';
import { DashboardController } from '../../src/dashboard/dashboard.controller';
import { DashboardService } from '../../src/dashboard/dashboard.service';
import { JwtAdmin } from '../../src/guards/jwt.admin.guard';
import {
  mockDashboardAnalytics,
  mockDatabaseError,
} from './dashboard-fixtures';

describe('DashboardController', () => {
  let controller: DashboardController;
  let dashboardService: jest.Mocked<DashboardService>;

  // Mock DashboardService
  const mockDashboardService = {
    get: jest.fn(),
  };

  // Mock JwtAdmin Guard
  const mockJwtAdminGuard = {
    canActivate: jest.fn(() => true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DashboardController],
      providers: [
        {
          provide: DashboardService,
          useValue: mockDashboardService,
        },
      ],
    })
      .overrideGuard(JwtAdmin)
      .useValue(mockJwtAdminGuard)
      .compile();

    controller = module.get<DashboardController>(DashboardController);
    dashboardService = module.get(DashboardService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getAll', () => {
    it('should return dashboard analytics successfully', async () => {
      // Arrange
      mockDashboardService.get.mockResolvedValue(mockDashboardAnalytics);

      // Act
      const result = await controller.getAll();

      // Assert
      expect(result).toEqual(mockDashboardAnalytics);
      expect(dashboardService.get).toHaveBeenCalledTimes(1);
    });

    it('should handle service errors gracefully', async () => {
      // Arrange
      mockDashboardService.get.mockRejectedValue(mockDatabaseError);

      // Act & Assert
      await expect(controller.getAll()).rejects.toThrow(
        'Database connection failed',
      );
      expect(dashboardService.get).toHaveBeenCalledTimes(1);
    });

    it('should return correct data structure', async () => {
      // Arrange
      mockDashboardService.get.mockResolvedValue(mockDashboardAnalytics);

      // Act
      const result = await controller.getAll();

      // Assert
      expect(result).toHaveProperty('total_mentors');
      expect(result).toHaveProperty('total_users');
      expect(result).toHaveProperty('mentor_service_revenue');
      expect(result).toHaveProperty('package_revenue');
      expect(result).toHaveProperty('immigration_service_revenue');
      expect(result).toHaveProperty('training_revenue');
      expect(result).toHaveProperty('total_revenue');
      expect(result).toHaveProperty('top_rated_mentors');
      expect(result).toHaveProperty('latest_users');
      expect(result).toHaveProperty('latest_contacts');
    });

    it('should validate revenue values are strings', async () => {
      // Arrange
      mockDashboardService.get.mockResolvedValue(mockDashboardAnalytics);

      // Act
      const result = await controller.getAll();

      // Assert
      expect(typeof result.total_mentors).toBe('string');
      expect(typeof result.total_users).toBe('string');
      expect(typeof result.mentor_service_revenue).toBe('string');
      expect(typeof result.package_revenue).toBe('string');
      expect(typeof result.immigration_service_revenue).toBe('string');
      expect(typeof result.training_revenue).toBe('string');
      expect(typeof result.total_revenue).toBe('string');
    });

    it('should validate array properties', async () => {
      // Arrange
      mockDashboardService.get.mockResolvedValue(mockDashboardAnalytics);

      // Act
      const result = await controller.getAll();

      // Assert
      expect(Array.isArray(result.top_rated_mentors)).toBe(true);
      expect(Array.isArray(result.latest_users)).toBe(true);
      expect(Array.isArray(result.latest_contacts)).toBe(true);
    });

    it('should handle empty arrays gracefully', async () => {
      // Arrange
      const emptyAnalytics = {
        ...mockDashboardAnalytics,
        top_rated_mentors: [],
        latest_users: [],
        latest_contacts: [],
      };
      mockDashboardService.get.mockResolvedValue(emptyAnalytics);

      // Act
      const result = await controller.getAll();

      // Assert
      expect(result.top_rated_mentors).toEqual([]);
      expect(result.latest_users).toEqual([]);
      expect(result.latest_contacts).toEqual([]);
    });
  });

  describe('Authentication and Authorization', () => {
    it('should be protected by JwtAdmin guard', () => {
      // Get the metadata for the getAll method
      const guards = Reflect.getMetadata('__guards__', controller.getAll);

      // Assert that JwtAdmin guard is applied
      expect(guards).toBeDefined();
    });

    it('should require Bearer token authentication', () => {
      // Check if the controller has ApiBearerAuth decorator
      // This is a simplified test - in real scenarios, the authentication
      // would be tested through integration tests
      expect(controller).toBeDefined();
      expect(controller.getAll).toBeDefined();
    });
  });

  describe('API Documentation', () => {
    it('should have proper API tags', () => {
      // Get the metadata for the controller
      const apiTags = Reflect.getMetadata(
        'swagger/apiUseTags',
        DashboardController,
      );

      // Assert that API tags are configured
      expect(apiTags).toBeDefined();
    });

    it('should be accessible at /dashboard endpoint', () => {
      // Get the metadata for the controller
      const controllerPath = Reflect.getMetadata('path', DashboardController);

      // Assert that the controller path is correct
      expect(controllerPath).toBe('dashboard');
    });

    it('should handle GET requests', () => {
      // Get the metadata for the getAll method
      const httpMethod = Reflect.getMetadata('method', controller.getAll);

      // Assert that it handles GET requests
      expect(httpMethod).toBeDefined();
    });
  });
});
