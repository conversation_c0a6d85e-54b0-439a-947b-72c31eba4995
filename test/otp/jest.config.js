module.exports = {
  displayName: 'OTP Module Tests',
  preset: 'ts-jest',
  testEnvironment: 'node',
  rootDir: '../../',
  testMatch: ['<rootDir>/test/otp/**/*.spec.ts'],
  setupFilesAfterEnv: ['<rootDir>/test/config/jest.setup.js'],
  collectCoverageFrom: [
    'src/otp/**/*.(t|j)s',
    '!src/otp/**/*.spec.ts',
    '!src/otp/**/*.interface.ts',
    '!src/otp/**/index.ts',
  ],
  coverageDirectory: '<rootDir>/coverage/otp',
  coverageReporters: ['text', 'lcov', 'html'],
  testTimeout: 30000,
  verbose: true,
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 90,
      lines: 85,
      statements: 85,
    },
    'src/otp/otp.service.ts': {
      branches: 90,
      functions: 95,
      lines: 90,
      statements: 90,
    },
  },
};
