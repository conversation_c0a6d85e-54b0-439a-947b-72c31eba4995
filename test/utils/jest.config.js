module.exports = {
  displayName: 'Utils Module Tests',
  preset: 'ts-jest',
  testEnvironment: 'node',
  rootDir: '../../',
  testMatch: ['<rootDir>/test/utils/**/*.spec.ts'],
  setupFilesAfterEnv: ['<rootDir>/test/config/jest.setup.js'],
  collectCoverageFrom: [
    'src/utils/**/*.(t|j)s',
    '!src/utils/**/*.spec.ts',
    '!src/utils/**/*.interface.ts',
    '!src/utils/**/index.ts',
    '!src/utils/setup.doc.ts', // Exclude Swagger setup
  ],
  coverageDirectory: '<rootDir>/coverage/utils',
  coverageReporters: ['text', 'lcov', 'html'],
  testTimeout: 30000,
  verbose: true,
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 85,
      lines: 80,
      statements: 80,
    },
    'src/utils/logger.service.ts': {
      branches: 85,
      functions: 90,
      lines: 85,
      statements: 85,
    },
    'src/utils/prisma.service.ts': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95,
    },
  },
};
