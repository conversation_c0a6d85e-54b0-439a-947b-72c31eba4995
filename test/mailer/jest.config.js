module.exports = {
  displayName: 'Mailer Module Tests',
  preset: 'ts-jest',
  testEnvironment: 'node',
  rootDir: '../../',
  testMatch: ['<rootDir>/test/mailer/**/*.spec.ts'],
  setupFilesAfterEnv: ['<rootDir>/test/config/jest.setup.js'],
  collectCoverageFrom: [
    'src/mailer/**/*.(t|j)s',
    '!src/mailer/**/*.spec.ts',
    '!src/mailer/**/*.interface.ts',
    '!src/mailer/**/index.ts',
  ],
  coverageDirectory: '<rootDir>/coverage/mailer',
  coverageReporters: ['text', 'lcov', 'html'],
  testTimeout: 30000,
  verbose: true,
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 90,
      lines: 85,
      statements: 85,
    },
    'src/mailer/mailer.service.ts': {
      branches: 90,
      functions: 95,
      lines: 90,
      statements: 90,
    },
  },
};
